<?php

namespace App\Http\Middleware;

use App\Services\SignatureService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;

class ApiAuth
{
    protected $signatureService;

    public function __construct(SignatureService $signatureService)
    {
        $this->signatureService = $signatureService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // 1. 频率限制检查
        $rateLimitResult = $this->checkRateLimit($request);
        if ($rateLimitResult !== true) {
            return $rateLimitResult;
        }

        // 2. 签名验证
        $signatureResult = $this->verifySignature($request);
        if ($signatureResult !== true) {
            return $signatureResult;
        }

        // 3. IP白名单检查
        $ipResult = $this->checkIpWhitelist($request);
        if ($ipResult !== true) {
            return $ipResult;
        }

        return $next($request);
    }

    /**
     * 检查频率限制
     */
    private function checkRateLimit(Request $request)
    {
        $merchantId = $request->input('merchant_id');
        if (!$merchantId) {
            return $this->errorResponse('缺少商户ID', 'MISSING_MERCHANT_ID', 400);
        }

        $key = 'api_rate_limit:' . $merchantId . ':' . $request->ip();
        $maxAttempts = 60; // 每分钟最多60次
        
        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            
            Log::warning('API频率限制触发', [
                'merchant_id' => $merchantId,
                'ip' => $request->ip(),
                'url' => $request->url(),
            ]);

            return response()->json([
                'success' => false,
                'code' => 'RATE_LIMIT_EXCEEDED',
                'message' => "请求过于频繁，请{$seconds}秒后再试",
                'data' => null,
                'timestamp' => now()->toDateTimeString(),
            ], 429);
        }

        RateLimiter::hit($key, 60); // 1分钟窗口
        return true;
    }

    /**
     * 验证签名
     */
    private function verifySignature(Request $request)
    {
        try {
            $params = $request->all();
            
            if (!isset($params['merchant_id'])) {
                return $this->errorResponse('缺少商户ID', 'MISSING_MERCHANT_ID', 400);
            }

            if (!isset($params['sign'])) {
                return $this->errorResponse('缺少签名参数', 'MISSING_SIGNATURE', 400);
            }

            $merchantId = $params['merchant_id'];
            $signature = $params['sign'];

            $verificationResult = $this->signatureService->verifyMerchantSignature(
                $merchantId,
                $params,
                $signature
            );

            if (!$verificationResult['success']) {
                Log::warning('API签名验证失败', [
                    'merchant_id' => $merchantId,
                    'ip' => $request->ip(),
                    'url' => $request->url(),
                    'error' => $verificationResult['message'],
                ]);

                return $this->errorResponse(
                    $verificationResult['message'],
                    $verificationResult['code'],
                    401
                );
            }

            // 将商户信息添加到请求中
            $request->merge(['merchant' => $verificationResult['merchant']]);

            return true;
        } catch (\Exception $e) {
            Log::error('签名验证异常', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return $this->errorResponse('签名验证异常', 'SIGNATURE_ERROR', 500);
        }
    }

    /**
     * 检查IP白名单
     */
    private function checkIpWhitelist(Request $request)
    {
        try {
            $merchantId = $request->input('merchant_id');
            $clientIp = $this->getClientIp($request);

            $isAllowed = $this->signatureService->verifyIpWhitelist($merchantId, $clientIp);

            if (!$isAllowed) {
                Log::warning('IP白名单验证失败', [
                    'merchant_id' => $merchantId,
                    'client_ip' => $clientIp,
                    'url' => $request->url(),
                ]);

                return $this->errorResponse('IP地址不在白名单中', 'IP_NOT_ALLOWED', 403);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('IP白名单验证异常', [
                'error' => $e->getMessage(),
                'request_ip' => $request->ip(),
            ]);

            return $this->errorResponse('IP验证异常', 'IP_VERIFICATION_ERROR', 500);
        }
    }

    /**
     * 获取客户端真实IP
     */
    private function getClientIp(Request $request): string
    {
        $ipHeaders = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];

        foreach ($ipHeaders as $header) {
            $ip = $request->server($header);
            if ($ip && $ip !== 'unknown') {
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * 返回错误响应
     */
    private function errorResponse($message, $code, $httpCode = 400)
    {
        return response()->json([
            'success' => false,
            'code' => $code,
            'message' => $message,
            'data' => null,
            'timestamp' => now()->toDateTimeString(),
        ], $httpCode);
    }
}
