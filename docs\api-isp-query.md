# 运营商查询接口

## 接口信息
- **请求方式**: POST
- **请求路径**: /api
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sign | string | 是 | 签名 |
| agentAccount | string | 是 | 账号 |
| busiBody | object | 是 | 业务参数体 |
| └─ action | string | 是 | 指令，固定值: ISP |
| └─ phone | string | 是 | 手机号码 |

## 请求示例

```json
{
  "sign": "5ea7148faab85987c445f59685109624",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "ISP",
    "phone": "***********"
  }
}
```

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| errorCode | integer | 错误码 |
| errorDesc | string/null | 错误描述 |
| ispName | string/null | 运营商名称 |

## 响应示例

```json
{
  "errorCode": 1,
  "errorDesc": "",
  "ispName": "移动"
}
```

## CURL请求示例

```bash
curl --location --request POST '/api' \
--header 'Content-Type: application/json' \
--data-raw '{
  "sign": "5ea7148faab85987c445f59685109624",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "ISP",
    "phone": "***********"
  }
}'
```