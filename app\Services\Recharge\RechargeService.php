<?php

namespace App\Services\Recharge;

use App\Services\Recharge\Requests\RechargeRequest;
use App\Services\Recharge\Requests\OrderQueryRequest;
use App\Services\Recharge\Responses\RechargeResponse;
use App\Services\Recharge\Responses\OrderQueryResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * 充值服务实现类
 * 
 * 提供话费充值和订单查询功能的具体实现
 */
class RechargeService implements RechargeServiceInterface
{
    /**
     * @var string API接口地址
     */
    protected string $apiUrl;

    /**
     * @var string 商户账号
     */
    protected string $agentAccount;

    /**
     * @var string MD5密钥
     */
    protected string $md5Key;

    /**
     * @var int 请求超时时间（秒）
     */
    protected int $timeout = 30;

    /**
     * 构造函数
     * 
     * @param string $apiUrl API接口地址
     * @param string $agentAccount 商户账号
     * @param string $md5Key MD5密钥
     */
    public function __construct(string $apiUrl, string $agentAccount, string $md5Key)
    {
        $this->apiUrl = $apiUrl;
        $this->agentAccount = $agentAccount;
        $this->md5Key = $md5Key;
    }

    /**
     * 话费充值
     * 
     * @param RechargeRequest $request 充值请求对象
     * @return RechargeResponse 充值响应对象
     * @throws \Exception 当充值失败时抛出异常
     */
    public function recharge(RechargeRequest $request): RechargeResponse
    {
        try {
            // 构建业务参数
            $busiBody = $request->toBusiBody();
            
            // 构建完整请求数据（包含签名）
            $requestData = SignatureService::buildRequestData(
                $this->agentAccount,
                $busiBody,
                $this->md5Key
            );
            
            // 记录请求日志
            Log::info('充值请求', [
                'order_id' => $request->orderId,
                'phone' => $request->chargeAcct,
                'amount' => $request->chargeCash,
                'request_data' => $requestData
            ]);
            
            // 发送HTTP请求
            $response = Http::timeout($this->timeout)
                ->asJson()
                ->post($this->apiUrl, $requestData);
            
            // 检查HTTP响应状态
            if (!$response->successful()) {
                throw new \Exception('充值请求失败: HTTP ' . $response->status());
            }
            
            $responseData = $response->json();
            
            // 记录响应日志
            Log::info('充值响应', [
                'order_id' => $request->orderId,
                'response_data' => $responseData
            ]);
            
            // 创建响应对象
            $rechargeResponse = new RechargeResponse($responseData);
            
            // 如果充值失败，抛出异常
            if (!$rechargeResponse->isSuccess()) {
                throw new \Exception('充值失败: ' . $rechargeResponse->getErrorMessage());
            }
            
            return $rechargeResponse;
            
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('充值异常', [
                'order_id' => $request->orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * 订单查询
     * 
     * @param OrderQueryRequest $request 订单查询请求对象
     * @return OrderQueryResponse 订单查询响应对象
     * @throws \Exception 当查询失败时抛出异常
     */
    public function queryOrder(OrderQueryRequest $request): OrderQueryResponse
    {
        try {
            // 构建业务参数
            $busiBody = $request->toBusiBody();
            
            // 构建完整请求数据（包含签名）
            $requestData = SignatureService::buildRequestData(
                $this->agentAccount,
                $busiBody,
                $this->md5Key
            );
            
            // 记录请求日志
            Log::info('订单查询请求', [
                'order_id' => $request->orderId,
                'request_data' => $requestData
            ]);
            
            // 发送HTTP请求
            $response = Http::timeout($this->timeout)
                ->asJson()
                ->post($this->apiUrl, $requestData);
            
            // 检查HTTP响应状态
            if (!$response->successful()) {
                throw new \Exception('订单查询请求失败: HTTP ' . $response->status());
            }
            
            $responseData = $response->json();
            
            // 记录响应日志
            Log::info('订单查询响应', [
                'order_id' => $request->orderId,
                'response_data' => $responseData
            ]);
            
            // 创建响应对象
            $queryResponse = new OrderQueryResponse($responseData);
            
            // 如果查询失败，抛出异常
            if (!$queryResponse->isSuccess()) {
                throw new \Exception('订单查询失败: ' . $queryResponse->getErrorMessage());
            }
            
            return $queryResponse;
            
        } catch (\Exception $e) {
            // 记录错误日志
            Log::error('订单查询异常', [
                'order_id' => $request->orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * 设置请求超时时间
     * 
     * @param int $timeout 超时时间（秒）
     * @return self
     */
    public function setTimeout(int $timeout): self
    {
        $this->timeout = $timeout;
        return $this;
    }
}