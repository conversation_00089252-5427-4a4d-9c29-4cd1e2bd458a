<?php

namespace App\Services\Recharge\Responses;

/**
 * 订单查询响应数据传输对象
 * 
 * 封装订单查询接口返回的数据
 */
class OrderQueryResponse
{
    /**
     * @var string|null 交易流水号
     */
    public ?string $chargeId;

    /**
     * @var float|null 代理商余额
     */
    public ?float $agentBalance;

    /**
     * @var string|null 订单状态文本
     */
    public ?string $orderStatuText;

    /**
     * @var string|null 订单状态代码
     */
    public ?string $orderStatuInt;

    /**
     * @var string|null 订单完成时间
     */
    public ?string $finishTime;

    /**
     * @var float|null 订单支付金额
     */
    public ?float $orderPayment;

    /**
     * @var int 错误代码（1表示成功）
     */
    public int $errorCode;

    /**
     * @var string 错误描述
     */
    public string $errorDesc;

    /**
     * @var array 原始响应数据
     */
    public array $rawResponse;

    /**
     * 构造函数
     * 
     * @param array $response 原始响应数据
     */
    public function __construct(array $response)
    {
        $this->rawResponse = $response;
        
        // 解析基础响应数据
        $this->errorCode = (int)($response['errorCode'] ?? -999);
        $this->errorDesc = $response['errorDesc'] ?? '未知错误';
        
        // 解析订单数据
        $this->chargeId = $response['chargeId'] ?? null;
        $this->agentBalance = isset($response['agentBalance']) ? (float)$response['agentBalance'] : null;
        $this->orderStatuText = $response['orderStatuText'] ?? null;
        $this->orderStatuInt = $response['orderStatuInt'] ?? null;
        $this->finishTime = $response['finishTime'] ?? null;
        $this->orderPayment = isset($response['orderPayment']) ? (float)$response['orderPayment'] : null;
    }

    /**
     * 判断查询是否成功
     * 
     * @return bool 是否成功
     */
    public function isSuccess(): bool
    {
        return $this->errorCode === 1;
    }

    /**
     * 判断订单是否充值成功
     * 
     * @return bool 是否充值成功
     */
    public function isOrderSuccess(): bool
    {
        // 订单状态代码 "16" 表示缴费成功
        return $this->orderStatuInt === '16';
    }

    /**
     * 获取错误信息
     * 
     * @return string 错误信息
     */
    public function getErrorMessage(): string
    {
        return sprintf('[%d] %s', $this->errorCode, $this->errorDesc);
    }

    /**
     * 获取订单状态描述
     * 
     * @return string 订单状态描述
     */
    public function getOrderStatusText(): string
    {
        return $this->orderStatuText ?? '未知状态';
    }
}