<?php

namespace App\Admin\Controllers;

use App\Models\Order;
use App\Models\RechargeRecord;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Box;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;

class RechargeReportController extends AdminController
{
    public function index(Content $content)
    {
        return $content
            ->title('充值统计')
            ->description('充值数据统计分析')
            ->body(function (Row $row) {
                // 第一行：统计卡片
                $row->column(3, $this->totalRechargeCard());
                $row->column(3, $this->rechargeAmountCard());
                $row->column(3, $this->rechargeSuccessRateCard());
                $row->column(3, $this->avgRechargeTimeCard());
            })
            ->body(function (Row $row) {
                // 第二行：图表
                $row->column(6, $this->dailyRechargeChart());
                $row->column(6, $this->providerDistributionChart());
            })
            ->body(function (Row $row) {
                // 第三行：运营商统计
                $row->column(12, $this->providerStatsTable());
            });
    }

    /**
     * 总充值数卡片
     */
    private function totalRechargeCard()
    {
        $total = Order::where('order_type', Order::TYPE_RECHARGE)->count();
        $today = Order::where('order_type', Order::TYPE_RECHARGE)
            ->whereDate('created_at', today())
            ->count();

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">{$total}</h2>
        <p class="text-muted mb-0">今日：{$today}</p>
    </div>
    <i class="feather icon-smartphone font-size-40"></i>
</div>
HTML;

        return Card::make('总充值数', $content)->tool('<a href="/admin/orders?order_type=2">查看详情</a>');
    }

    /**
     * 充值金额卡片
     */
    private function rechargeAmountCard()
    {
        $total = Order::where('order_type', Order::TYPE_RECHARGE)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->sum('amount');
        $today = Order::where('order_type', Order::TYPE_RECHARGE)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->whereDate('orders.created_at', today())
            ->sum('amount');

        $total = number_format($total, 2);
        $today = number_format($today, 2);

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">¥{$total}</h2>
        <p class="text-muted mb-0">今日：¥{$today}</p>
    </div>
    <i class="feather icon-credit-card font-size-40"></i>
</div>
HTML;

        return Card::make('充值总额', $content);
    }

    /**
     * 充值成功率卡片
     */
    private function rechargeSuccessRateCard()
    {
        $total = RechargeRecord::whereIn('status', [
            RechargeRecord::STATUS_SUCCESS,
            RechargeRecord::STATUS_FAILED
        ])->count();
        $success = RechargeRecord::where('status', RechargeRecord::STATUS_SUCCESS)->count();

        $rate = $total > 0 ? round($success / $total * 100, 2) : 0;

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">{$rate}%</h2>
        <p class="text-muted mb-0">成功率</p>
    </div>
    <i class="feather icon-check-circle font-size-40"></i>
</div>
HTML;

        return Card::make('充值成功率', $content);
    }

    /**
     * 平均充值时长卡片
     */
    private function avgRechargeTimeCard()
    {
        $avgTime = RechargeRecord::where('status', RechargeRecord::STATUS_SUCCESS)
            ->whereNotNull('submitted_at')
            ->whereNotNull('completed_at')
            ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, submitted_at, completed_at)) as avg_time')
            ->value('avg_time');

        $avgTime = round($avgTime ?: 0);

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">{$avgTime}秒</h2>
        <p class="text-muted mb-0">平均充值时长</p>
    </div>
    <i class="feather icon-clock font-size-40"></i>
</div>
HTML;

        return Card::make('平均处理时长', $content);
    }

    /**
     * 每日充值图表
     */
    private function dailyRechargeChart()
    {
        $data = Order::where('order_type', Order::TYPE_RECHARGE)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->where('orders.created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(orders.amount) as amount')
            )
            ->get();

        $dates = [];
        $counts = [];
        $amounts = [];

        foreach ($data as $item) {
            $dates[] = $item->date;
            $counts[] = $item->count;
            $amounts[] = round($item->amount, 2);
        }

        $chartData = [
            'labels' => $dates,
            'datasets' => [
                [
                    'label' => '充值笔数',
                    'data' => $counts,
                    'borderColor' => 'rgb(54, 162, 235)',
                    'yAxisID' => 'y-axis-1',
                ],
                [
                    'label' => '充值金额',
                    'data' => $amounts,
                    'borderColor' => 'rgb(255, 159, 64)',
                    'yAxisID' => 'y-axis-2',
                ]
            ]
        ];

        $chartDataJson = json_encode($chartData);
        
        $chartHtml = <<<HTML
<canvas id="dailyRechargeChart" height="300"></canvas>
<script>
Dcat.ready(function () {
    const ctx = document.getElementById('dailyRechargeChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {$chartDataJson},
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    id: 'y-axis-1',
                    type: 'linear',
                    display: true,
                    position: 'left'
                }, {
                    id: 'y-axis-2',
                    type: 'linear',
                    display: true,
                    position: 'right',
                    gridLines: {
                        drawOnChartArea: false
                    }
                }]
            }
        }
    });
});
</script>
HTML;

        return Box::make('最近30天充值趋势', $chartHtml);
    }

    /**
     * 运营商分布图
     */
    private function providerDistributionChart()
    {
        $data = RechargeRecord::where('status', RechargeRecord::STATUS_SUCCESS)
            ->groupBy('provider')
            ->select(
                'provider',
                DB::raw('COUNT(*) as count')
            )
            ->get();

        $labels = [];
        $values = [];

        $providerNames = [
            'unicom' => '联通',
            'mobile' => '移动',
            'telecom' => '电信',
        ];

        foreach ($data as $item) {
            $labels[] = $providerNames[$item->provider] ?? $item->provider;
            $values[] = $item->count;
        }

        $chartData = [
            'labels' => $labels,
            'datasets' => [[
                'data' => $values,
                'backgroundColor' => [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                ],
            ]]
        ];

        $chartDataJson = json_encode($chartData);
        
        $chartHtml = <<<HTML
<canvas id="providerChart" height="300"></canvas>
<script>
Dcat.ready(function () {
    const ctx = document.getElementById('providerChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {$chartDataJson},
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>
HTML;

        return Box::make('运营商分布', $chartHtml);
    }

    /**
     * 运营商统计表
     */
    private function providerStatsTable()
    {
        $stats = RechargeRecord::groupBy('provider')
            ->select(
                'provider',
                DB::raw('COUNT(*) as total_count'),
                DB::raw('SUM(CASE WHEN recharge_records.status = ' . RechargeRecord::STATUS_SUCCESS . ' THEN 1 ELSE 0 END) as success_count'),
                DB::raw('SUM(CASE WHEN recharge_records.status = ' . RechargeRecord::STATUS_FAILED . ' THEN 1 ELSE 0 END) as failed_count'),
                DB::raw('SUM(recharge_records.amount) as total_amount'),
                DB::raw('AVG(recharge_records.retry_times) as avg_retry')
            )
            ->get();

        $providerNames = [
            'unicom' => '联通',
            'mobile' => '移动',
            'telecom' => '电信',
        ];

        $rows = '';
        foreach ($stats as $stat) {
            $providerName = $providerNames[$stat->provider] ?? $stat->provider;
            $successRate = $stat->total_count > 0 
                ? round($stat->success_count / $stat->total_count * 100, 2) 
                : 0;
            $totalAmount = number_format($stat->total_amount, 2);
            $avgRetry = round($stat->avg_retry, 2);
            
            $rows .= <<<HTML
<tr>
    <td>{$providerName}</td>
    <td>{$stat->total_count}</td>
    <td>{$stat->success_count}</td>
    <td>{$stat->failed_count}</td>
    <td>{$successRate}%</td>
    <td>¥{$totalAmount}</td>
    <td>{$avgRetry}</td>
</tr>
HTML;
        }

        $tableHtml = <<<HTML
<table class="table table-hover">
    <thead>
        <tr>
            <th>运营商</th>
            <th>总笔数</th>
            <th>成功</th>
            <th>失败</th>
            <th>成功率</th>
            <th>充值总额</th>
            <th>平均重试</th>
        </tr>
    </thead>
    <tbody>
        {$rows}
    </tbody>
</table>
HTML;

        return Box::make('运营商充值统计', $tableHtml);
    }
}