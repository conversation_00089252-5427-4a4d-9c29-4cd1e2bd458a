# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Laravel 9 application with Dcat Admin installed for backend management. The application is named "charge" and uses MySQL database.

## Common Development Commands

### Build and Development
- `php artisan serve` - Start development server
- `npm run dev` - Run Vite development server for frontend assets
- `npm run build` - Build production assets

### Database
- `php artisan migrate` - Run database migrations
- `php artisan migrate:rollback` - Rollback last migration
- `php artisan migrate:fresh` - Drop all tables and re-run all migrations
- `php artisan db:seed` - Run database seeders

### Testing
- `php artisan test` - Run all tests
- `php artisan test --testsuite=Unit` - Run unit tests only
- `php artisan test --testsuite=Feature` - Run feature tests only
- `./vendor/bin/phpunit` - Alternative way to run PHPUnit tests

### Code Quality
- `./vendor/bin/pint` - Run Laravel Pint code formatter
- `./vendor/bin/pint --test` - Check code style without making changes

### Cache Management
- `php artisan config:clear` - Clear configuration cache
- `php artisan cache:clear` - Clear application cache
- `php artisan route:clear` - Clear route cache
- `php artisan view:clear` - Clear compiled view files
- `php artisan optimize` - Cache configuration and routes

## Architecture

### Directory Structure
- `app/Admin/` - Dcat Admin controllers, routes, and configuration
  - `Controllers/` - Admin panel controllers
  - `Metrics/` - Dashboard metrics and widgets
  - `bootstrap.php` - Admin initialization
  - `routes.php` - Admin routes definition
- `app/Http/Controllers/` - Web and API controllers
- `app/Models/` - Eloquent ORM models
- `config/admin.php` - Dcat Admin configuration
- `database/migrations/` - Database schema migrations
- `routes/` - Application routing files
  - `web.php` - Web routes
  - `api.php` - API routes

### Key Dependencies
- Laravel Framework 9.x
- Dcat Admin 2.2.3-beta - Admin panel framework
- Laravel Sanctum - API authentication
- Doctrine DBAL - Database abstraction layer

### Database Configuration
- Connection: MySQL
- Database: charge
- Username: charge
- Password: Ps5wXGhDxGbJKBHe

### Admin Panel
The admin panel is accessible at `/admin` with default credentials:
- Username: admin
- Password: admin

Remember to change these credentials in production!

## Important Notes
- Always run migrations after pulling changes that might include database updates
- Use `.env` file for environment-specific configuration
- The project uses Vite for asset bundling instead of Laravel Mix
- Dcat Admin resources are published to `public/vendor/dcat-admin/`