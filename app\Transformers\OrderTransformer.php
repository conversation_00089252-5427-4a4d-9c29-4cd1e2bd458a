<?php

namespace App\Transformers;

use App\Models\Order;
use League\Fractal\TransformerAbstract;

class OrderTransformer extends TransformerAbstract
{
    /**
     * 可包含的关联资源
     */
    protected array $availableIncludes = [
        'product',
        'payments',
        'recharge_record',
    ];

    /**
     * 转换订单数据
     */
    public function transform(Order $order): array
    {
        return [
            'id' => $order->id,
            'order_no' => $order->order_no,
            'merchant_order_no' => $order->merchant_order_no,
            'type' => $order->type,
            'type_text' => $order->type_text,
            'status' => $order->status,
            'status_text' => $order->status_text,
            'amount' => (float) $order->amount,
            'voucher_deduct' => (float) ($order->voucher_deduct ?? 0),
            'actual_amount' => (float) ($order->actual_amount ?? $order->amount),
            'mobile' => $order->mobile,
            'quantity' => $order->quantity,
            'remark' => $order->remark,
            'created_at' => $order->created_at->toDateTimeString(),
            'paid_at' => $order->paid_at?->toDateTimeString(),
            'completed_at' => $order->completed_at?->toDateTimeString(),
        ];
    }

    /**
     * 包含产品信息
     */
    public function includeProduct(Order $order)
    {
        if ($order->product) {
            return $this->item($order->product, new ProductTransformer());
        }
        
        return $this->null();
    }

    /**
     * 包含支付信息
     */
    public function includePayments(Order $order)
    {
        return $this->collection($order->payments, new PaymentTransformer());
    }

    /**
     * 包含充值记录
     */
    public function includeRechargeRecord(Order $order)
    {
        if ($order->rechargeRecord) {
            return $this->item($order->rechargeRecord, new RechargeRecordTransformer());
        }
        
        return $this->null();
    }
}
