<?php

namespace App\Services\Recharge;

use Illuminate\Support\ServiceProvider;

/**
 * 充值服务提供者
 * 
 * 注册充值服务到Laravel容器
 */
class RechargeServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     * 
     * @return void
     */
    public function register(): void
    {
        // 绑定充值服务接口到具体实现
        $this->app->singleton(RechargeServiceInterface::class, function ($app) {
            // 从配置文件获取参数
            $config = config('recharge');
            
            return new RechargeService(
                $config['api_url'] ?? '',
                $config['agent_account'] ?? '',
                $config['md5_key'] ?? ''
            );
        });
    }

    /**
     * 启动服务
     * 
     * @return void
     */
    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
            __DIR__ . '/../../../config/recharge.php' => config_path('recharge.php'),
        ], 'recharge-config');
    }
}