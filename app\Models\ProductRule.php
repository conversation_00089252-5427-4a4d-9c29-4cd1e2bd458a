<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'rule_type',
        'rule_value',
    ];

    protected $casts = [
        'rule_value' => 'json',
    ];

    // 规则类型常量
    const TYPE_LIMIT_PER_USER = 'limit_per_user';     // 每人限购
    const TYPE_STOCK_LIMIT = 'stock_limit';           // 库存限制
    const TYPE_SALE_PERIOD = 'sale_period';           // 销售时间段
    const TYPE_VALID_PERIOD = 'valid_period';         // 券有效期
    const TYPE_MIN_PURCHASE = 'min_purchase';         // 最小购买量
    const TYPE_MAX_PURCHASE = 'max_purchase';         // 最大购买量
    const TYPE_USER_LEVEL = 'user_level';             // 用户等级限制
    const TYPE_REGION_LIMIT = 'region_limit';         // 地区限制

    /**
     * 规则类型文本映射
     */
    public static $typeTexts = [
        self::TYPE_LIMIT_PER_USER => '每人限购',
        self::TYPE_STOCK_LIMIT => '库存限制',
        self::TYPE_SALE_PERIOD => '销售时间段',
        self::TYPE_VALID_PERIOD => '券有效期',
        self::TYPE_MIN_PURCHASE => '最小购买量',
        self::TYPE_MAX_PURCHASE => '最大购买量',
        self::TYPE_USER_LEVEL => '用户等级限制',
        self::TYPE_REGION_LIMIT => '地区限制',
    ];

    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取规则类型文本
     */
    public function getTypeTextAttribute()
    {
        return self::$typeTexts[$this->rule_type] ?? '未知规则';
    }

    /**
     * 获取规则描述
     */
    public function getDescriptionAttribute()
    {
        switch ($this->rule_type) {
            case self::TYPE_LIMIT_PER_USER:
                return "每人限购 {$this->rule_value['max_count']} 次";
                
            case self::TYPE_STOCK_LIMIT:
                return "库存限制 {$this->rule_value['max_stock']} 件";
                
            case self::TYPE_SALE_PERIOD:
                return "销售时间：{$this->rule_value['start_time']} 至 {$this->rule_value['end_time']}";
                
            case self::TYPE_VALID_PERIOD:
                return "券有效期 {$this->rule_value['days']} 天";
                
            case self::TYPE_MIN_PURCHASE:
                return "最少购买 {$this->rule_value['min_quantity']} 件";
                
            case self::TYPE_MAX_PURCHASE:
                return "最多购买 {$this->rule_value['max_quantity']} 件";
                
            case self::TYPE_USER_LEVEL:
                $levels = implode('、', $this->rule_value['allowed_levels'] ?? []);
                return "限制用户等级：{$levels}";
                
            case self::TYPE_REGION_LIMIT:
                $regions = implode('、', $this->rule_value['allowed_regions'] ?? []);
                return "限制地区：{$regions}";
                
            default:
                return '自定义规则';
        }
    }

    /**
     * 验证规则
     */
    public function validate($context = []): bool
    {
        switch ($this->rule_type) {
            case self::TYPE_LIMIT_PER_USER:
                return $this->validateLimitPerUser($context);
                
            case self::TYPE_STOCK_LIMIT:
                return $this->validateStockLimit($context);
                
            case self::TYPE_SALE_PERIOD:
                return $this->validateSalePeriod($context);
                
            case self::TYPE_MIN_PURCHASE:
                return $this->validateMinPurchase($context);
                
            case self::TYPE_MAX_PURCHASE:
                return $this->validateMaxPurchase($context);
                
            default:
                return true;
        }
    }

    /**
     * 验证每人限购
     */
    private function validateLimitPerUser($context): bool
    {
        if (!isset($context['user_id'])) return true;
        
        $maxCount = $this->rule_value['max_count'] ?? 0;
        if ($maxCount <= 0) return true;
        
        $purchaseCount = Order::where('user_id', $context['user_id'])
            ->where('product_id', $this->product_id)
            ->whereIn('status', [Order::STATUS_PAID, Order::STATUS_SUCCESS])
            ->count();
            
        return $purchaseCount < $maxCount;
    }

    /**
     * 验证库存限制
     */
    private function validateStockLimit($context): bool
    {
        $maxStock = $this->rule_value['max_stock'] ?? 0;
        if ($maxStock <= 0) return true;
        
        return $this->product->sales_count < $maxStock;
    }

    /**
     * 验证销售时间段
     */
    private function validateSalePeriod($context): bool
    {
        $startTime = $this->rule_value['start_time'] ?? null;
        $endTime = $this->rule_value['end_time'] ?? null;
        
        if (!$startTime || !$endTime) return true;
        
        $now = now();
        return $now >= $startTime && $now <= $endTime;
    }

    /**
     * 验证最小购买量
     */
    private function validateMinPurchase($context): bool
    {
        $minQuantity = $this->rule_value['min_quantity'] ?? 1;
        $quantity = $context['quantity'] ?? 1;
        
        return $quantity >= $minQuantity;
    }

    /**
     * 验证最大购买量
     */
    private function validateMaxPurchase($context): bool
    {
        $maxQuantity = $this->rule_value['max_quantity'] ?? 0;
        if ($maxQuantity <= 0) return true;
        
        $quantity = $context['quantity'] ?? 1;
        return $quantity <= $maxQuantity;
    }

    /**
     * 作用域：按产品筛选
     */
    public function scopeByProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * 作用域：按规则类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('rule_type', $type);
    }
}
