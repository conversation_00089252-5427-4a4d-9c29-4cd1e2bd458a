<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);

        $this->call([
            AdminMenuSeeder::class,
            SampleDataSeeder::class,
            RechargeProviderSeeder::class,
            MobileSegmentSeeder::class,
            ProductRuleSeeder::class,
            SystemConfigSeeder::class,
        ]);
    }
}
