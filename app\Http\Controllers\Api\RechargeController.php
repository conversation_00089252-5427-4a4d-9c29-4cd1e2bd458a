<?php

namespace App\Http\Controllers\Api;

use App\Services\OrderService;
use App\Services\MobileCarrierService;
use App\Services\ConfigService;
use App\Transformers\OrderTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Spatie\ValidationRules\Rules\Delimited;

class RechargeController extends BaseApiController
{
    protected $orderService;
    protected $mobileCarrierService;
    protected $configService;

    public function __construct(
        OrderService $orderService,
        MobileCarrierService $mobileCarrierService,
        ConfigService $configService
    ) {
        $this->orderService = $orderService;
        $this->mobileCarrierService = $mobileCarrierService;
        $this->configService = $configService;
    }

    /**
     * 创建充值订单
     */
    public function createOrder(Request $request)
    {
        try {
            // 验证请求参数
            $validated = $this->validateRequest([
                'mobile' => ['required', 'regex:/^1[3-9]\d{9}$/'],
                'amount' => ['required', 'numeric', 'min:1'],
                'merchant_order_no' => ['required', 'string', 'max:64'],
                'notify_url' => ['nullable', 'url'],
                'remark' => ['nullable', 'string', 'max:255'],
            ], [
                'mobile.required' => '手机号不能为空',
                'mobile.regex' => '手机号格式不正确',
                'amount.required' => '充值金额不能为空',
                'amount.numeric' => '充值金额必须是数字',
                'amount.min' => '充值金额必须大于0',
                'merchant_order_no.required' => '商户订单号不能为空',
                'merchant_order_no.max' => '商户订单号长度不能超过64位',
                'notify_url.url' => '回调地址格式不正确',
            ]);

            $merchant = $this->getMerchant();

            // 验证充值金额是否支持
            $supportedAmounts = $this->configService->getSupportedAmounts();
            if (!in_array($validated['amount'], $supportedAmounts)) {
                return $this->error('不支持的充值金额', 'UNSUPPORTED_AMOUNT');
            }

            // 检查商户订单号是否重复
            $existingOrder = \App\Models\Order::where('merchant_id', $merchant->id)
                ->where('merchant_order_no', $validated['merchant_order_no'])
                ->first();

            if ($existingOrder) {
                return $this->error('商户订单号已存在', 'DUPLICATE_ORDER_NO');
            }

            // 获取手机号运营商信息
            $carrierInfo = $this->mobileCarrierService->getCarrierInfo($validated['mobile']);

            // 检查商户余额
            $balanceCheck = app(\App\Services\BalanceService::class)
                ->checkMerchantBalance($merchant->id, $validated['amount']);

            if (!$balanceCheck['sufficient']) {
                return $this->error('商户余额不足', 'INSUFFICIENT_BALANCE', 400, [
                    'available_balance' => $balanceCheck['available_balance'],
                    'required_amount' => $balanceCheck['required_amount'],
                    'shortage' => $balanceCheck['shortage'],
                ]);
            }

            // 创建充值订单
            $orderData = [
                'user_id' => null, // API订单没有用户ID
                'merchant_id' => $merchant->id,
                'type' => \App\Models\Order::TYPE_RECHARGE,
                'mobile' => $validated['mobile'],
                'amount' => $validated['amount'],
                'merchant_order_no' => $validated['merchant_order_no'],
                'notify_url' => $validated['notify_url'] ?? null,
                'remark' => $validated['remark'] ?? '',
                'operator' => 'api',
            ];

            $result = $this->orderService->createRechargeOrder($orderData);

            if (!$result['success']) {
                return $this->error($result['message'], 'ORDER_CREATE_FAILED');
            }

            $order = $result['order'];

            // 记录API调用
            $this->logApiCall('create_recharge_order', $validated, [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
            ]);

            // 返回订单信息
            return $this->item($order, new OrderTransformer(), ['recharge_record']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('创建充值订单失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'params' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->serverError('创建订单失败');
        }
    }

    /**
     * 查询订单状态
     */
    public function queryOrder(Request $request)
    {
        try {
            // 验证请求参数
            $validated = $this->validateRequest([
                'order_no' => ['required_without:merchant_order_no', 'string'],
                'merchant_order_no' => ['required_without:order_no', 'string'],
            ], [
                'order_no.required_without' => '订单号和商户订单号至少提供一个',
                'merchant_order_no.required_without' => '订单号和商户订单号至少提供一个',
            ]);

            $merchant = $this->getMerchant();

            // 查询订单
            $query = \App\Models\Order::where('merchant_id', $merchant->id);

            if (isset($validated['order_no'])) {
                $query->where('order_no', $validated['order_no']);
            } else {
                $query->where('merchant_order_no', $validated['merchant_order_no']);
            }

            $order = $query->with(['rechargeRecord', 'payments'])->first();

            if (!$order) {
                return $this->notFound('订单不存在');
            }

            // 记录API调用
            $this->logApiCall('query_order', $validated, [
                'order_id' => $order->id,
                'status' => $order->status,
            ]);

            return $this->item($order, new OrderTransformer(), ['recharge_record', 'payments']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('查询订单失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'params' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('查询订单失败');
        }
    }

    /**
     * 获取支持的充值金额
     */
    public function getSupportedAmounts()
    {
        try {
            $amounts = $this->configService->getSupportedAmounts();

            return $this->success([
                'amounts' => $amounts,
                'count' => count($amounts),
            ]);

        } catch (\Exception $e) {
            Log::error('获取支持金额失败', [
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('获取支持金额失败');
        }
    }

    /**
     * 获取手机号运营商信息
     */
    public function getCarrierInfo(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'mobile' => ['required', 'regex:/^1[3-9]\d{9}$/'],
            ], [
                'mobile.required' => '手机号不能为空',
                'mobile.regex' => '手机号格式不正确',
            ]);

            $carrierInfo = $this->mobileCarrierService->getCarrierInfo($validated['mobile']);

            return $this->success($carrierInfo);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('获取运营商信息失败', [
                'params' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('获取运营商信息失败');
        }
    }
}
