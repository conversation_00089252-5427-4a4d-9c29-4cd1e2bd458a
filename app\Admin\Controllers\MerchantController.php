<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Merchant;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Str;

class MerchantController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Merchant(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('account', '商户账号');
            $grid->column('name', '商户名称');
            $grid->column('contact_name', '联系人');
            $grid->column('contact_phone', '联系电话');
            $grid->column('balance', '余额')->display(function ($value) {
                return '¥' . number_format($value, 2);
            });
            $grid->column('frozen_balance', '冻结余额')->display(function ($value) {
                return '¥' . number_format($value, 2);
            });
            $grid->column('available_balance', '可用余额')->display(function () {
                return '¥' . number_format($this->available_balance, 2);
            });
            $grid->column('status', '状态')->switch();
            $grid->column('created_at', '创建时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('account', '商户账号');
                $filter->like('name', '商户名称');
                $filter->equal('status', '状态')->select([0 => '禁用', 1 => '启用']);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Merchant(), function (Show $show) {
            $show->field('id');
            $show->field('account', '商户账号');
            $show->field('name', '商户名称');
            $show->field('contact_name', '联系人');
            $show->field('contact_phone', '联系电话');
            $show->field('email', '邮箱');
            $show->field('md5_key', 'MD5密钥');
            $show->field('balance', '余额');
            $show->field('frozen_balance', '冻结余额');
            $show->field('status', '状态')->using([0 => '禁用', 1 => '启用']);
            $show->field('ip_whitelist', 'IP白名单')->json();
            $show->field('remark', '备注');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Merchant(), function (Form $form) {
            $form->display('id');
            $form->text('account', '商户账号')->required()
                ->creationRules(['required', 'unique:merchants'])
                ->updateRules(['required', "unique:merchants,account,{{id}}"]);
            $form->text('name', '商户名称')->required();
            $form->text('contact_name', '联系人');
            $form->mobile('contact_phone', '联系电话');
            $form->email('email', '邮箱');
            
            if ($form->isCreating()) {
                $form->text('md5_key', 'MD5密钥')
                    ->default(Str::random(32))
                    ->required()
                    ->help('用于API签名验证，请妥善保管');
            } else {
                $form->text('md5_key', 'MD5密钥')
                    ->required()
                    ->help('用于API签名验证，请妥善保管');
            }
            
            $form->currency('balance', '余额')->symbol('¥')->default(0);
            $form->currency('frozen_balance', '冻结余额')->symbol('¥')->default(0);
            $form->switch('status', '状态')->default(1);
            $form->tags('ip_whitelist', 'IP白名单')
                ->help('留空表示不限制IP');
            $form->textarea('remark', '备注');

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}