<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'system_order_no',
        'merchant_id',
        'merchant_order_no',
        'user_id',
        'product_id',
        'order_type',
        'mobile',
        'amount',
        'cost',
        'status',
        'notify_url',
        'notify_status',
        'notify_times',
        'remark',
        'paid_at',
        'completed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'cost' => 'decimal:2',
        'paid_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // 订单类型
    const TYPE_PRODUCT = 'product';   // 产品购买
    const TYPE_RECHARGE = 'recharge'; // 话费充值

    /**
     * 类型文本映射
     */
    public static $typeTexts = [
        self::TYPE_PRODUCT => '产品购买',
        self::TYPE_RECHARGE => '话费充值',
    ];

    // 订单状态
    const STATUS_PENDING = 0;        // 待支付
    const STATUS_PAID = 1;           // 已支付
    const STATUS_PROCESSING = 2;     // 处理中
    const STATUS_RECHARGING = 6;     // 充值中
    const STATUS_SUCCESS = 16;       // 成功
    const STATUS_FAILED = 26;        // 失败
    const STATUS_CANCELLED = 30;     // 已取消
    const STATUS_REFUNDED = 40;      // 已退款

    // 通知状态
    const NOTIFY_PENDING = 0;
    const NOTIFY_SUCCESS = 1;

    /**
     * 状态文本映射
     */
    public static $statusTexts = [
        self::STATUS_PENDING => '待支付',
        self::STATUS_PAID => '已支付',
        self::STATUS_PROCESSING => '处理中',
        self::STATUS_RECHARGING => '充值中',
        self::STATUS_SUCCESS => '成功',
        self::STATUS_FAILED => '失败',
        self::STATUS_CANCELLED => '已取消',
        self::STATUS_REFUNDED => '已退款',
    ];

    /**
     * 关联商户
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 订单日志
     */
    public function logs()
    {
        return $this->hasMany(OrderLog::class);
    }

    /**
     * 充值记录
     */
    public function rechargeRecord()
    {
        return $this->hasOne(RechargeRecord::class);
    }

    /**
     * 关联支付记录
     */
    public function payments()
    {
        return $this->hasMany(OrderPayment::class);
    }

    /**
     * 获取最新的支付记录
     */
    public function latestPayment()
    {
        return $this->hasOne(OrderPayment::class)->latest();
    }

    /**
     * 获取已支付的支付记录
     */
    public function paidPayment()
    {
        return $this->hasOne(OrderPayment::class)->where('status', OrderPayment::STATUS_PAID);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::$statusTexts[$this->status] ?? '未知';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute()
    {
        return self::$typeTexts[$this->type] ?? '未知';
    }

    /**
     * 是否是产品订单
     */
    public function isProductOrder(): bool
    {
        return $this->type === self::TYPE_PRODUCT;
    }

    /**
     * 是否是充值订单
     */
    public function isRechargeOrder(): bool
    {
        return $this->type === self::TYPE_RECHARGE;
    }

    /**
     * 是否已支付
     */
    public function isPaid(): bool
    {
        return $this->status >= self::STATUS_PAID;
    }

    /**
     * 是否已完成
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    /**
     * 是否失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 是否可以取消
     */
    public function canCancel(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PAID]);
    }

    /**
     * 记录日志
     */
    public function log($action, $content = null, $extra = null)
    {
        return $this->logs()->create([
            'operator_type' => 'system',
            'operator_id' => auth()->id(),
            'action' => $action,
            'from_status' => $this->getOriginal('status'),
            'to_status' => $this->status,
            'content' => $content,
            'extra_data' => $extra,
        ]);
    }
}