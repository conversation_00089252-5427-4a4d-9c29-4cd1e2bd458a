# 充值服务封装

本目录包含了充值API的完整封装实现，提供了简洁易用的接口来处理话费充值业务。

## 目录结构

```
app/Services/Recharge/
├── RechargeServiceInterface.php    # 充值服务接口定义
├── RechargeService.php            # 充值服务具体实现
├── SignatureService.php           # 签名生成和验证服务
├── RechargeServiceProvider.php    # Laravel服务提供者
├── Requests/                      # 请求数据传输对象
│   ├── RechargeRequest.php       # 充值请求
│   └── OrderQueryRequest.php     # 订单查询请求
└── Responses/                     # 响应数据传输对象
    ├── RechargeResponse.php      # 充值响应
    └── OrderQueryResponse.php    # 订单查询响应
```

## 使用方法

### 1. 配置

在 `.env` 文件中配置以下参数：

```env
RECHARGE_API_URL=http://api.example.com/api
RECHARGE_AGENT_ACCOUNT=your_agent_account
RECHARGE_MD5_KEY=your_md5_key
RECHARGE_TIMEOUT=30
RECHARGE_ENABLE_LOG=true
```

### 2. 依赖注入

通过依赖注入获取充值服务：

```php
use App\Services\Recharge\RechargeServiceInterface;

class RechargeController extends Controller
{
    protected RechargeServiceInterface $rechargeService;
    
    public function __construct(RechargeServiceInterface $rechargeService)
    {
        $this->rechargeService = $rechargeService;
    }
}
```

### 3. 话费充值

```php
use App\Services\Recharge\Requests\RechargeRequest;

// 创建充值请求
$request = new RechargeRequest('ORDER123', '***********', '100');
$request->setRetUrl('http://your-callback-url.com/callback');

try {
    // 执行充值
    $response = $this->rechargeService->recharge($request);
    
    if ($response->isSuccess()) {
        // 充值成功
        $chargeId = $response->getChargeId();
        // 保存交易流水号等信息
    }
} catch (\Exception $e) {
    // 处理异常
    Log::error('充值失败: ' . $e->getMessage());
}
```

### 4. 订单查询

```php
use App\Services\Recharge\Requests\OrderQueryRequest;

// 创建查询请求
$request = new OrderQueryRequest('ORDER123');

try {
    // 执行查询
    $response = $this->rechargeService->queryOrder($request);
    
    if ($response->isSuccess()) {
        // 查询成功
        if ($response->isOrderSuccess()) {
            // 订单充值成功
            echo "充值成功，完成时间: " . $response->finishTime;
        } else {
            // 订单充值失败或处理中
            echo "订单状态: " . $response->getOrderStatusText();
        }
    }
} catch (\Exception $e) {
    // 处理异常
    Log::error('查询失败: ' . $e->getMessage());
}
```

## 签名验证

签名服务提供了独立的签名生成和验证功能：

```php
use App\Services\Recharge\SignatureService;

// 生成签名
$busiBody = [
    'action' => 'CZ',
    'orderId' => 'TEST001',
    // ... 其他参数
];
$sign = SignatureService::generateSign($busiBody, $md5Key);

// 验证签名
$isValid = SignatureService::verifySign($receivedSign, $busiBody, $md5Key);
```

## 错误处理

所有的错误码都在配置文件 `config/recharge.php` 中定义：

```php
$errorCode = -11;
$errorMessage = config('recharge.error_codes.' . $errorCode, '未知错误');
```

## 日志记录

服务会自动记录所有请求和响应日志，便于问题排查。日志包括：
- 请求参数
- 响应数据
- 异常信息

## 注意事项

1. **订单号唯一性**: 确保每个订单号都是唯一的，避免重复提交
2. **URL编码**: `ispName` 和 `retUrl` 参数会自动进行URL编码
3. **超时处理**: 默认超时时间为30秒，可通过配置调整
4. **签名安全**: MD5密钥必须妥善保管，不要泄露到代码仓库中
5. **IP白名单**: 确保服务器IP已添加到充值平台的白名单中

## 扩展

如需添加新的充值类型或功能，可以：
1. 在 `Requests` 目录添加新的请求类
2. 在 `Responses` 目录添加新的响应类
3. 在 `RechargeServiceInterface` 中定义新方法
4. 在 `RechargeService` 中实现新方法