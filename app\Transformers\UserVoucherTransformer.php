<?php

namespace App\Transformers;

use App\Models\UserVoucher;
use League\Fractal\TransformerAbstract;

class UserVoucherTransformer extends TransformerAbstract
{
    /**
     * 可包含的关联资源
     */
    protected array $availableIncludes = [
        'product',
        'order',
        'used_order',
    ];

    /**
     * 转换用户券数据
     */
    public function transform(UserVoucher $voucher): array
    {
        return [
            'id' => $voucher->id,
            'voucher_value' => (float) $voucher->voucher_value,
            'voucher_code' => $voucher->voucher_code,
            'status' => $voucher->status,
            'status_text' => $voucher->status_text,
            'source' => $voucher->source,
            'source_id' => $voucher->source_id,
            'used_at' => $voucher->used_at?->toDateTimeString(),
            'expired_at' => $voucher->expired_at?->toDateTimeString(),
            'valid_until' => $voucher->valid_until?->toDateTimeString(),
            'is_available' => $voucher->isAvailable(),
            'is_expired' => $voucher->isExpired(),
            'created_at' => $voucher->created_at->toDateTimeString(),
        ];
    }

    /**
     * 包含产品信息
     */
    public function includeProduct(UserVoucher $voucher)
    {
        if ($voucher->product) {
            return $this->item($voucher->product, new ProductTransformer());
        }
        
        return $this->null();
    }

    /**
     * 包含来源订单信息
     */
    public function includeOrder(UserVoucher $voucher)
    {
        if ($voucher->order) {
            return $this->item($voucher->order, new OrderTransformer());
        }
        
        return $this->null();
    }

    /**
     * 包含使用订单信息
     */
    public function includeUsedOrder(UserVoucher $voucher)
    {
        if ($voucher->usedOrder) {
            return $this->item($voucher->usedOrder, new OrderTransformer());
        }
        
        return $this->null();
    }
}
