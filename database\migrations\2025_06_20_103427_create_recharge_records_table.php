<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recharge_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->unique()->constrained()->comment('订单ID');
            $table->string('provider', 50)->comment('充值渠道：unicom,mobile,telecom');
            $table->string('provider_order_no', 64)->nullable()->comment('渠道订单号');
            $table->string('mobile', 11)->comment('充值手机号');
            $table->integer('amount')->comment('充值金额');
            $table->tinyInteger('status')->default(0)->comment('充值状态');
            $table->json('request_data')->nullable()->comment('请求数据');
            $table->json('response_data')->nullable()->comment('响应数据');
            $table->integer('retry_times')->default(0)->comment('重试次数');
            $table->string('error_code', 50)->nullable()->comment('错误码');
            $table->string('error_msg', 500)->nullable()->comment('错误信息');
            $table->timestamp('submitted_at')->nullable()->comment('提交时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamps();
            
            $table->index('provider_order_no');
            $table->index('mobile');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recharge_records');
    }
};