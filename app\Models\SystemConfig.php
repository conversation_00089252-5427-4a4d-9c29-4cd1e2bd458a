<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SystemConfig extends Model
{
    use HasFactory;

    protected $fillable = [
        'group',
        'key',
        'value',
        'type',
        'name',
        'description',
        'options',
        'is_public',
        'is_required',
        'sort_order',
    ];

    protected $casts = [
        'options' => 'json',
        'is_public' => 'boolean',
        'is_required' => 'boolean',
    ];

    // 数据类型常量
    const TYPE_STRING = 'string';
    const TYPE_INT = 'int';
    const TYPE_FLOAT = 'float';
    const TYPE_BOOL = 'bool';
    const TYPE_JSON = 'json';
    const TYPE_ARRAY = 'array';

    // 配置分组常量
    const GROUP_SYSTEM = 'system';
    const GROUP_PAYMENT = 'payment';
    const GROUP_RECHARGE = 'recharge';
    const GROUP_NOTIFICATION = 'notification';
    const GROUP_SMS = 'sms';
    const GROUP_EMAIL = 'email';

    /**
     * 获取配置值
     */
    public static function get($key, $default = null, $group = null)
    {
        $cacheKey = $group ? "config:{$group}:{$key}" : "config:{$key}";
        
        return Cache::remember($cacheKey, 3600, function () use ($key, $default, $group) {
            $query = self::where('key', $key);
            
            if ($group) {
                $query->where('group', $group);
            }
            
            $config = $query->first();
            
            if (!$config) {
                return $default;
            }
            
            return self::castValue($config->value, $config->type);
        });
    }

    /**
     * 设置配置值
     */
    public static function set($key, $value, $group = null)
    {
        $query = self::where('key', $key);
        
        if ($group) {
            $query->where('group', $group);
        }
        
        $config = $query->first();
        
        if ($config) {
            $config->update(['value' => $value]);
        } else {
            self::create([
                'group' => $group ?: self::GROUP_SYSTEM,
                'key' => $key,
                'value' => $value,
                'type' => self::TYPE_STRING,
                'name' => $key,
            ]);
        }
        
        // 清除缓存
        $cacheKey = $group ? "config:{$group}:{$key}" : "config:{$key}";
        Cache::forget($cacheKey);
        Cache::forget("config_group:{$group}");
    }

    /**
     * 获取分组配置
     */
    public static function getGroup($group)
    {
        $cacheKey = "config_group:{$group}";
        
        return Cache::remember($cacheKey, 3600, function () use ($group) {
            $configs = self::where('group', $group)
                ->orderBy('sort_order')
                ->get();
                
            $result = [];
            foreach ($configs as $config) {
                $result[$config->key] = self::castValue($config->value, $config->type);
            }
            
            return $result;
        });
    }

    /**
     * 获取公开配置（前端可访问）
     */
    public static function getPublicConfigs()
    {
        $cacheKey = 'config_public';
        
        return Cache::remember($cacheKey, 3600, function () {
            $configs = self::where('is_public', true)
                ->orderBy('group')
                ->orderBy('sort_order')
                ->get();
                
            $result = [];
            foreach ($configs as $config) {
                $result[$config->group][$config->key] = self::castValue($config->value, $config->type);
            }
            
            return $result;
        });
    }

    /**
     * 类型转换
     */
    private static function castValue($value, $type)
    {
        if ($value === null) {
            return null;
        }
        
        switch ($type) {
            case self::TYPE_INT:
                return (int) $value;
                
            case self::TYPE_FLOAT:
                return (float) $value;
                
            case self::TYPE_BOOL:
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
                
            case self::TYPE_JSON:
            case self::TYPE_ARRAY:
                return json_decode($value, true);
                
            case self::TYPE_STRING:
            default:
                return (string) $value;
        }
    }

    /**
     * 获取类型化的值
     */
    public function getTypedValueAttribute()
    {
        return self::castValue($this->value, $this->type);
    }

    /**
     * 清除所有配置缓存
     */
    public static function clearCache()
    {
        $groups = self::distinct('group')->pluck('group');
        
        foreach ($groups as $group) {
            Cache::forget("config_group:{$group}");
        }
        
        Cache::forget('config_public');
        
        // 清除单个配置缓存
        $configs = self::all();
        foreach ($configs as $config) {
            Cache::forget("config:{$config->group}:{$config->key}");
            Cache::forget("config:{$config->key}");
        }
    }

    /**
     * 批量设置配置
     */
    public static function setBatch(array $configs, $group = null)
    {
        foreach ($configs as $key => $value) {
            self::set($key, $value, $group);
        }
    }

    /**
     * 验证配置值
     */
    public function validateValue($value): bool
    {
        if ($this->is_required && empty($value)) {
            return false;
        }
        
        // 根据类型验证
        switch ($this->type) {
            case self::TYPE_INT:
                return is_numeric($value);
                
            case self::TYPE_FLOAT:
                return is_numeric($value);
                
            case self::TYPE_BOOL:
                return in_array($value, [true, false, 'true', 'false', '1', '0', 1, 0]);
                
            case self::TYPE_JSON:
            case self::TYPE_ARRAY:
                json_decode($value);
                return json_last_error() === JSON_ERROR_NONE;
                
            default:
                return true;
        }
    }

    /**
     * 作用域：按分组筛选
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * 作用域：公开配置
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 作用域：必填配置
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }
}
