<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'price',
        'cost',
        'total_value',
        'status',
        'sort',
        'sales_count',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost' => 'decimal:2',
        'total_value' => 'decimal:2',
        'status' => 'boolean',
    ];

    // 状态常量
    const STATUS_OFFLINE = 0;
    const STATUS_ONLINE = 1;

    /**
     * 产品包含的券配置
     */
    public function vouchers()
    {
        return $this->hasMany(ProductVoucher::class);
    }

    /**
     * 产品规则
     */
    public function rules()
    {
        return $this->hasMany(ProductRule::class);
    }

    /**
     * 关联订单
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * 关联用户券
     */
    public function userVouchers()
    {
        return $this->hasMany(UserVoucher::class);
    }

    /**
     * 获取利润率
     */
    public function getProfitRateAttribute()
    {
        if ($this->price == 0) return 0;
        return round(($this->price - $this->cost) / $this->price * 100, 2);
    }

    /**
     * 获取指定规则
     */
    public function getRule($ruleType)
    {
        $rule = $this->rules()->where('rule_type', $ruleType)->first();
        return $rule ? $rule->rule_value : null;
    }

    /**
     * 检查库存是否充足
     */
    public function checkStock($quantity = 1): bool
    {
        // 检查产品库存字段
        if (isset($this->stock)) {
            // 如果库存为-1表示无限库存
            if ($this->stock === -1) {
                return true;
            }
            return $this->stock >= $quantity;
        }

        // 检查库存规则
        $stockRule = $this->rules()->where('rule_type', ProductRule::TYPE_STOCK_LIMIT)->first();
        if (!$stockRule) return true;

        $maxStock = $stockRule->rule_value['max_stock'] ?? 0;
        return ($this->sales_count + $quantity) <= $maxStock;
    }

    /**
     * 验证产品规则
     */
    public function validateRules($context = []): array
    {
        $errors = [];

        foreach ($this->rules as $rule) {
            $ruleErrors = $rule->validate($context);
            if (!empty($ruleErrors)) {
                if (is_array($ruleErrors)) {
                    $errors = array_merge($errors, $ruleErrors);
                } else {
                    $errors[] = $ruleErrors;
                }
            }
        }

        return $errors;
    }

    /**
     * 作用域：上架产品
     */
    public function scopeOnline($query)
    {
        return $query->where('status', self::STATUS_ONLINE);
    }

    /**
     * 检查产品是否上架
     */
    public function isOnline(): bool
    {
        return $this->status === self::STATUS_ONLINE;
    }

    /**
     * 检查产品是否下架
     */
    public function isOffline(): bool
    {
        return $this->status === self::STATUS_OFFLINE;
    }



    /**
     * 获取产品状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status === self::STATUS_ONLINE ? '上架' : '下架';
    }

    /**
     * 获取库存状态文本
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->stock === -1) {
            return '无限库存';
        }

        if ($this->stock <= 0) {
            return '缺货';
        }

        if ($this->stock <= 10) {
            return '库存紧张';
        }

        return '库存充足';
    }

    /**
     * 减少库存
     */
    public function decreaseStock($quantity = 1): bool
    {
        // 无限库存不需要减少
        if ($this->stock === -1) {
            return true;
        }

        if ($this->stock < $quantity) {
            return false;
        }

        $this->decrement('stock', $quantity);
        return true;
    }

    /**
     * 增加库存
     */
    public function increaseStock($quantity = 1): bool
    {
        // 无限库存不需要增加
        if ($this->stock === -1) {
            return true;
        }

        $this->increment('stock', $quantity);
        return true;
    }
}