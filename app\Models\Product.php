<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'description',
        'price',
        'cost',
        'total_value',
        'status',
        'sort',
        'sales_count',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost' => 'decimal:2',
        'total_value' => 'decimal:2',
        'status' => 'boolean',
    ];

    // 状态常量
    const STATUS_OFFLINE = 0;
    const STATUS_ONLINE = 1;

    /**
     * 产品包含的券配置
     */
    public function vouchers()
    {
        return $this->hasMany(ProductVoucher::class);
    }

    /**
     * 获取利润率
     */
    public function getProfitRateAttribute()
    {
        if ($this->price == 0) return 0;
        return round(($this->price - $this->cost) / $this->price * 100, 2);
    }

    /**
     * 作用域：上架产品
     */
    public function scopeOnline($query)
    {
        return $query->where('status', self::STATUS_ONLINE);
    }
}