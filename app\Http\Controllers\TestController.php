<?php

namespace App\Http\Controllers;

use App\Services\ProductService;
use App\Services\VoucherService;
use App\Services\OrderService;
use App\Services\BalanceService;
use App\Services\SignatureService;
use App\Models\User;
use App\Models\Merchant;
use Illuminate\Http\Request;

class TestController extends Controller
{
    protected $productService;
    protected $voucherService;
    protected $orderService;
    protected $balanceService;
    protected $signatureService;

    public function __construct(
        ProductService $productService,
        VoucherService $voucherService,
        OrderService $orderService,
        BalanceService $balanceService,
        SignatureService $signatureService
    ) {
        $this->productService = $productService;
        $this->voucherService = $voucherService;
        $this->orderService = $orderService;
        $this->balanceService = $balanceService;
        $this->signatureService = $signatureService;
    }

    /**
     * 测试产品服务
     */
    public function testProductService()
    {
        try {
            // 获取可购买的产品列表
            $products = $this->productService->getAvailableProducts();
            
            return response()->json([
                'success' => true,
                'message' => '产品服务测试成功',
                'data' => [
                    'products_count' => count($products),
                    'products' => $products,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '产品服务测试失败：' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 测试创建产品订单
     */
    public function testCreateProductOrder(Request $request)
    {
        try {
            // 获取或创建测试用户
            $user = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                ['name' => '测试用户', 'password' => bcrypt('password')]
            );

            // 获取第一个产品
            $products = $this->productService->getAvailableProducts();
            if (empty($products)) {
                return response()->json([
                    'success' => false,
                    'message' => '没有可用的产品',
                ]);
            }

            $product = $products[0];

            // 创建订单
            $orderData = [
                'user_id' => $user->id,
                'product_id' => $product['id'],
                'quantity' => 1,
                'operator' => 'test',
            ];

            $result = $this->orderService->createProductOrder($orderData);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? '产品订单创建成功' : $result['message'],
                'data' => $result['success'] ? [
                    'order' => $result['order']->toArray(),
                    'price_data' => $result['price_data'],
                ] : null,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '创建产品订单失败：' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 测试余额服务
     */
    public function testBalanceService()
    {
        try {
            // 获取或创建测试商户
            $merchant = Merchant::first();
            if (!$merchant) {
                return response()->json([
                    'success' => false,
                    'message' => '没有找到商户，请先创建商户',
                ]);
            }

            // 测试充值
            $rechargeResult = $this->balanceService->rechargeMerchantBalance(
                $merchant->id,
                100,
                'test',
                '测试充值'
            );

            // 获取余额信息
            $balanceInfo = $this->balanceService->getMerchantBalance($merchant->id);

            return response()->json([
                'success' => true,
                'message' => '余额服务测试成功',
                'data' => [
                    'recharge_result' => $rechargeResult,
                    'balance_info' => $balanceInfo,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '余额服务测试失败：' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 测试签名服务
     */
    public function testSignatureService()
    {
        try {
            // 获取测试商户
            $merchant = Merchant::first();
            if (!$merchant) {
                return response()->json([
                    'success' => false,
                    'message' => '没有找到商户，请先创建商户',
                ]);
            }

            // 测试参数
            $params = [
                'merchant_id' => $merchant->id,
                'mobile' => '13800138000',
                'amount' => 10,
                'order_no' => 'TEST' . time(),
            ];

            // 生成签名
            $signature = $this->signatureService->generateSignature($params, $merchant->md5_key);

            // 验证签名
            $isValid = $this->signatureService->verifySignature($params, $merchant->md5_key, $signature);

            // 验证商户签名
            $merchantVerification = $this->signatureService->verifyMerchantSignature(
                $merchant->id,
                $params,
                $signature
            );

            return response()->json([
                'success' => true,
                'message' => '签名服务测试成功',
                'data' => [
                    'params' => $params,
                    'signature' => $signature,
                    'is_valid' => $isValid,
                    'merchant_verification' => $merchantVerification,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '签名服务测试失败：' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 测试券服务
     */
    public function testVoucherService()
    {
        try {
            // 获取测试用户
            $user = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                ['name' => '测试用户', 'password' => bcrypt('password')]
            );

            // 获取用户可用券
            $availableVouchers = $this->voucherService->getUserAvailableVouchers($user->id);

            // 获取用户券统计
            $voucherStats = $this->voucherService->getUserVoucherStats($user->id);

            // 计算最优券使用方案
            $optimalUsage = $this->voucherService->calculateOptimalVoucherUsage($user->id, 30);

            return response()->json([
                'success' => true,
                'message' => '券服务测试成功',
                'data' => [
                    'available_vouchers' => $availableVouchers,
                    'voucher_stats' => $voucherStats,
                    'optimal_usage_for_30' => $optimalUsage,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '券服务测试失败：' . $e->getMessage(),
            ]);
        }
    }

    /**
     * 测试完整的购买流程
     */
    public function testFullPurchaseFlow()
    {
        try {
            // 1. 获取测试用户和商户
            $user = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                ['name' => '测试用户', 'password' => bcrypt('password')]
            );

            $merchant = Merchant::first();
            if (!$merchant) {
                return response()->json([
                    'success' => false,
                    'message' => '没有找到商户，请先创建商户',
                ]);
            }

            // 2. 确保商户有足够余额
            $this->balanceService->rechargeMerchantBalance($merchant->id, 1000, 'test', '测试充值');

            // 3. 获取产品
            $products = $this->productService->getAvailableProducts();
            if (empty($products)) {
                return response()->json([
                    'success' => false,
                    'message' => '没有可用的产品',
                ]);
            }

            $product = $products[0];

            // 4. 创建产品订单
            $orderResult = $this->orderService->createProductOrder([
                'user_id' => $user->id,
                'product_id' => $product['id'],
                'quantity' => 1,
                'merchant_id' => $merchant->id,
                'operator' => 'test',
            ]);

            if (!$orderResult['success']) {
                return response()->json($orderResult);
            }

            $order = $orderResult['order'];

            // 5. 处理订单支付
            $paymentResult = $this->orderService->processOrderPayment($order, [
                'method' => 'balance',
                'merchant_id' => $merchant->id,
                'operator' => 'test',
            ]);

            return response()->json([
                'success' => true,
                'message' => '完整购买流程测试成功',
                'data' => [
                    'user' => $user->toArray(),
                    'merchant' => $merchant->toArray(),
                    'product' => $product,
                    'order_creation' => $orderResult,
                    'payment_result' => $paymentResult,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '完整购买流程测试失败：' . $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * 测试所有服务
     */
    public function testAllServices()
    {
        $results = [];

        // 测试产品服务
        $results['product_service'] = $this->testProductService()->getData();

        // 测试余额服务
        $results['balance_service'] = $this->testBalanceService()->getData();

        // 测试签名服务
        $results['signature_service'] = $this->testSignatureService()->getData();

        // 测试券服务
        $results['voucher_service'] = $this->testVoucherService()->getData();

        return response()->json([
            'success' => true,
            'message' => '所有服务测试完成',
            'results' => $results,
        ]);
    }
}
