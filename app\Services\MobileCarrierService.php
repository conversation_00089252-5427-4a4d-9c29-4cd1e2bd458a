<?php

namespace App\Services;

use App\Models\MobileSegment;
use Illuminate\Support\Facades\Cache;

class MobileCarrierService
{
    /**
     * 获取手机号运营商信息
     */
    public function getCarrierInfo(string $mobile): array
    {
        return MobileSegment::getCarrierInfo($mobile);
    }

    /**
     * 批量获取手机号运营商信息
     */
    public function batchGetCarrierInfo(array $mobiles): array
    {
        $result = [];
        
        foreach ($mobiles as $mobile) {
            $result[$mobile] = $this->getCarrierInfo($mobile);
        }
        
        return $result;
    }

    /**
     * 根据运营商获取可用的充值渠道
     */
    public function getAvailableProviders(string $mobile): array
    {
        $carrierInfo = $this->getCarrierInfo($mobile);
        $carrier = $carrierInfo['carrier'];
        
        if ($carrier === 'unknown') {
            return [];
        }
        
        return \App\Models\RechargeProvider::enabled()
            ->whereJsonContains('config->carriers', $carrier)
            ->orderByWeight()
            ->get()
            ->toArray();
    }

    /**
     * 验证手机号格式
     */
    public function validateMobile(string $mobile): bool
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile);
    }

    /**
     * 格式化手机号（添加空格）
     */
    public function formatMobile(string $mobile): string
    {
        if (!$this->validateMobile($mobile)) {
            return $mobile;
        }
        
        return substr($mobile, 0, 3) . ' ' . substr($mobile, 3, 4) . ' ' . substr($mobile, 7, 4);
    }

    /**
     * 获取运营商统计信息
     */
    public function getCarrierStatistics(): array
    {
        return Cache::remember('carrier_statistics', 3600, function () {
            $stats = MobileSegment::getCarrierStats();
            $total = array_sum(array_column($stats, 'count'));
            
            $result = [];
            foreach ($stats as $carrier => $stat) {
                $result[$carrier] = [
                    'name' => $stat['name'],
                    'count' => $stat['count'],
                    'percentage' => $total > 0 ? round(($stat['count'] / $total) * 100, 2) : 0,
                ];
            }
            
            return $result;
        });
    }

    /**
     * 更新号段缓存
     */
    public function refreshSegmentCache(): void
    {
        Cache::forget('carrier_statistics');
        Cache::forget('carrier_stats');
        Cache::forget('province_stats');
        
        // 清除所有号段缓存
        $segments = MobileSegment::pluck('segment');
        foreach ($segments as $segment) {
            Cache::forget("mobile_segment:{$segment}");
        }
    }

    /**
     * 导入号段数据
     */
    public function importSegments(array $segments): array
    {
        $imported = 0;
        $errors = [];
        
        foreach ($segments as $segment) {
            try {
                // 验证数据格式
                if (!$this->validateSegmentData($segment)) {
                    $errors[] = "无效的号段数据：" . json_encode($segment);
                    continue;
                }
                
                MobileSegment::updateOrCreate(
                    ['segment' => $segment['segment']],
                    [
                        'carrier' => $segment['carrier'],
                        'province' => $segment['province'],
                        'city' => $segment['city'],
                    ]
                );
                
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "导入号段 {$segment['segment']} 失败：" . $e->getMessage();
            }
        }
        
        // 刷新缓存
        $this->refreshSegmentCache();
        
        return [
            'imported' => $imported,
            'errors' => $errors,
            'total' => count($segments),
        ];
    }

    /**
     * 验证号段数据格式
     */
    private function validateSegmentData(array $segment): bool
    {
        $required = ['segment', 'carrier', 'province', 'city'];
        
        foreach ($required as $field) {
            if (!isset($segment[$field]) || empty($segment[$field])) {
                return false;
            }
        }
        
        // 验证号段格式
        if (!preg_match('/^1\d{6}$/', $segment['segment'])) {
            return false;
        }
        
        // 验证运营商
        $validCarriers = ['mobile', 'unicom', 'telecom'];
        if (!in_array($segment['carrier'], $validCarriers)) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取号段覆盖率统计
     */
    public function getSegmentCoverage(): array
    {
        $totalPossible = 1000; // 假设总共有1000个可能的号段
        $actualCount = MobileSegment::count();
        
        return [
            'total_possible' => $totalPossible,
            'actual_count' => $actualCount,
            'coverage_rate' => round(($actualCount / $totalPossible) * 100, 2),
            'missing_count' => $totalPossible - $actualCount,
        ];
    }

    /**
     * 检查号段是否存在
     */
    public function segmentExists(string $segment): bool
    {
        return MobileSegment::where('segment', $segment)->exists();
    }

    /**
     * 根据省份获取号段
     */
    public function getSegmentsByProvince(string $province): array
    {
        return MobileSegment::byProvince($province)
            ->select(['segment', 'carrier', 'city'])
            ->get()
            ->toArray();
    }

    /**
     * 根据运营商获取号段
     */
    public function getSegmentsByCarrier(string $carrier): array
    {
        return MobileSegment::byCarrier($carrier)
            ->select(['segment', 'province', 'city'])
            ->get()
            ->toArray();
    }
}
