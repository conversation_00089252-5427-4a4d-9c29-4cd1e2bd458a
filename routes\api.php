<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\RechargeController;
use App\Http\Controllers\Api\BalanceController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ProductController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 公开接口（不需要认证）
Route::prefix('v1')->group(function () {
    // 认证相关
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
    Route::post('reset-password', [AuthController::class, 'resetPassword']);

    // 产品相关
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{id}', [ProductController::class, 'show']);
    Route::get('products/popular', [ProductController::class, 'popular']);
    Route::get('products/statistics', [ProductController::class, 'statistics']);

    // 获取支持的充值金额
    Route::get('supported-amounts', [RechargeController::class, 'getSupportedAmounts']);

    // 获取手机号运营商信息
    Route::post('carrier-info', [RechargeController::class, 'getCarrierInfo']);
});

// 需要认证的API接口
Route::prefix('v1')->middleware('api.auth')->group(function () {

    // 充值相关接口
    Route::prefix('recharge')->group(function () {
        Route::post('create-order', [RechargeController::class, 'createOrder']);
        Route::post('query-order', [RechargeController::class, 'queryOrder']);
    });

    // 余额相关接口
    Route::prefix('balance')->group(function () {
        Route::get('/', [BalanceController::class, 'getBalance']);
        Route::get('logs', [BalanceController::class, 'getBalanceLogs']);
        Route::post('check', [BalanceController::class, 'checkBalance']);
    });

    // 订单相关接口
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::get('{orderNo}', [OrderController::class, 'show']);
        Route::get('statistics', [OrderController::class, 'statistics']);
    });
});

// Sanctum认证接口（如果需要）
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
