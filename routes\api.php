<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\RechargeController;
use App\Http\Controllers\Api\BalanceController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\ConfigController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 公开接口（不需要认证）
Route::prefix('v1')->group(function () {
    // 系统状态和配置
    Route::get('health', [ConfigController::class, 'healthCheck']);
    Route::get('config/system', [ConfigController::class, 'systemConfig']);
    Route::get('config/supported-amounts', [ConfigController::class, 'supportedAmounts']);
    Route::get('config/supported-carriers', [ConfigController::class, 'supportedCarriers']);
    Route::get('config/check-amount/{amount}', [ConfigController::class, 'checkAmount']);
    Route::get('config/calculate-fee/{amount}', [ConfigController::class, 'calculateFee']);
    Route::get('config/api-documentation', [ConfigController::class, 'apiDocumentation']);

    // 产品相关（公开查看）
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{id}', [ProductController::class, 'show']);
    Route::get('products/popular', [ProductController::class, 'popular']);
    Route::get('products/statistics', [ProductController::class, 'statistics']);

    // 获取支持的充值金额
    Route::get('supported-amounts', [RechargeController::class, 'getSupportedAmounts']);

    // 获取手机号运营商信息
    Route::post('carrier-info', [RechargeController::class, 'getCarrierInfo']);
});

// 需要商户认证的API接口
Route::prefix('v1')->middleware('api.auth')->group(function () {

    // 充值相关接口
    Route::prefix('recharge')->group(function () {
        Route::post('create-order', [RechargeController::class, 'createOrder']);
        Route::post('query-order', [RechargeController::class, 'queryOrder']);
    });

    // 余额相关接口
    Route::prefix('balance')->group(function () {
        Route::get('/', [BalanceController::class, 'getBalance']);
        Route::get('logs', [BalanceController::class, 'getBalanceLogs']);
        Route::post('check', [BalanceController::class, 'checkBalance']);
    });

    // 订单相关接口
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::get('{orderNo}', [OrderController::class, 'show']);
        Route::get('statistics', [OrderController::class, 'statistics']);
    });

    // 产品购买接口（需要商户认证）
    Route::post('products/{id}/purchase', [ProductController::class, 'purchase']);
});

// Sanctum认证接口（如果需要）
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
