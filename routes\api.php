<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\RechargeController;
use App\Http\Controllers\Api\BalanceController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\ConfigController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\FrontendRechargeController;
use App\Http\Controllers\Api\FrontendProductController;
use App\Http\Controllers\Api\DocumentationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 公开接口（不需要认证）
Route::prefix('v1')->group(function () {
    // 用户认证相关
    Route::post('auth/register', [AuthController::class, 'register']);
    Route::post('auth/login', [AuthController::class, 'login']);
    Route::post('auth/login-mobile', [AuthController::class, 'loginByMobile']);
    Route::post('auth/send-sms', [AuthController::class, 'sendSmsCode']);

    // 系统状态和配置
    Route::get('health', [ConfigController::class, 'healthCheck']);
    Route::get('config/system', [ConfigController::class, 'systemConfig']);
    Route::get('config/supported-amounts', [ConfigController::class, 'supportedAmounts']);
    Route::get('config/supported-carriers', [ConfigController::class, 'supportedCarriers']);
    Route::get('config/check-amount/{amount}', [ConfigController::class, 'checkAmount']);
    Route::get('config/calculate-fee/{amount}', [ConfigController::class, 'calculateFee']);
    Route::get('config/api-documentation', [ConfigController::class, 'apiDocumentation']);
    Route::get('frontend/documentation', [DocumentationController::class, 'frontendApiDocumentation']);

    // 前端产品相关（公开）
    Route::get('frontend/products', [FrontendProductController::class, 'index']);
    Route::get('frontend/products/{id}', [FrontendProductController::class, 'show']);
    Route::get('frontend/products/popular', [FrontendProductController::class, 'popular']);
    Route::get('frontend/products/categories', [FrontendProductController::class, 'categories']);
    Route::get('frontend/products/statistics', [FrontendProductController::class, 'statistics']);

    // 前端充值相关（公开）
    Route::get('frontend/recharge/config', [FrontendRechargeController::class, 'getRechargeConfig']);
    Route::post('frontend/recharge/carrier-info', [FrontendRechargeController::class, 'getCarrierInfo']);
    Route::post('frontend/recharge/create-order', [FrontendRechargeController::class, 'createOrder']);
    Route::post('frontend/recharge/query-order', [FrontendRechargeController::class, 'queryOrder']);
    Route::post('frontend/orders/{id}/pay', [FrontendRechargeController::class, 'payOrder']);

    // 商户API产品相关（兼容）
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{id}', [ProductController::class, 'show']);
    Route::get('products/popular', [ProductController::class, 'popular']);
    Route::get('products/statistics', [ProductController::class, 'statistics']);

    // 获取支持的充值金额（兼容旧接口）
    Route::get('supported-amounts', [RechargeController::class, 'getSupportedAmounts']);

    // 获取手机号运营商信息（兼容）
    Route::post('carrier-info', [RechargeController::class, 'getCarrierInfo']);

    // 充值回调接口（不需要认证）
    Route::post('recharge/notify/{provider}', [RechargeController::class, 'handleCallback'])->name('api.recharge.notify');
});

// 需要用户认证的前端API接口
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // 用户认证相关
    Route::post('auth/logout', [AuthController::class, 'logout']);
    Route::post('auth/refresh', [AuthController::class, 'refresh']);
    Route::get('auth/me', [AuthController::class, 'me']);

    // 用户管理
    Route::get('user/profile', [UserController::class, 'profile']);
    Route::put('user/profile', [UserController::class, 'updateProfile']);
    Route::post('user/change-password', [UserController::class, 'changePassword']);
    Route::get('user/statistics', [UserController::class, 'statistics']);

    // 用户券管理
    Route::get('user/vouchers', [UserController::class, 'vouchers']);
    Route::get('user/voucher-stats', [UserController::class, 'voucherStats']);
    Route::post('user/calculate-vouchers', [UserController::class, 'calculateOptimalVouchers']);

    // 用户订单管理
    Route::get('user/orders', [UserController::class, 'orders']);
    Route::get('user/orders/{id}', [UserController::class, 'orderDetail']);
    Route::post('user/orders/{id}/cancel', [UserController::class, 'cancelOrder']);

    // 前端产品购买（需要用户登录）
    Route::post('frontend/products/{id}/purchase', [FrontendProductController::class, 'purchase']);
    Route::get('frontend/products/{id}/check-limit', [FrontendProductController::class, 'checkPurchaseLimit']);

    // 前端充值相关（需要用户登录）
    Route::get('frontend/recharge/user-vouchers', [FrontendRechargeController::class, 'getUserVouchers']);
    Route::post('frontend/recharge/calculate-voucher', [FrontendRechargeController::class, 'calculateVoucherDeduction']);
});

// 需要商户认证的API接口
Route::prefix('v1')->middleware('api.auth')->group(function () {

    // 充值相关接口
    Route::prefix('recharge')->group(function () {
        Route::post('create-order', [RechargeController::class, 'createOrder']);
        Route::post('query-order', [RechargeController::class, 'queryOrder']);
    });

    // 余额相关接口
    Route::prefix('balance')->group(function () {
        Route::get('/', [BalanceController::class, 'getBalance']);
        Route::get('logs', [BalanceController::class, 'getBalanceLogs']);
        Route::post('check', [BalanceController::class, 'checkBalance']);
    });

    // 订单相关接口
    Route::prefix('orders')->group(function () {
        Route::get('/', [OrderController::class, 'index']);
        Route::get('{orderNo}', [OrderController::class, 'show']);
        Route::get('statistics', [OrderController::class, 'statistics']);
    });

    // 产品购买接口（需要商户认证）
    Route::post('products/{id}/purchase', [ProductController::class, 'purchase']);
});

// Sanctum认证接口（如果需要）
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
