<?php

namespace App\Services\Recharge\Responses;

/**
 * 充值响应数据传输对象
 * 
 * 封装充值接口返回的数据
 */
class RechargeResponse
{
    /**
     * @var string|null 系统生成的交易流水号
     */
    public ?string $chargeId;

    /**
     * @var int 错误代码（1表示成功）
     */
    public int $errorCode;

    /**
     * @var string 错误描述
     */
    public string $errorDesc;

    /**
     * @var array 原始响应数据
     */
    public array $rawResponse;

    /**
     * 构造函数
     * 
     * @param array $response 原始响应数据
     */
    public function __construct(array $response)
    {
        $this->rawResponse = $response;
        
        // 解析响应数据
        $this->errorCode = (int)($response['errorCode'] ?? -999);
        $this->errorDesc = $response['errorDesc'] ?? '未知错误';
        $this->chargeId = $response['chargeId'] ?? null;
    }

    /**
     * 判断充值是否成功
     * 
     * @return bool 是否成功
     */
    public function isSuccess(): bool
    {
        return $this->errorCode === 1;
    }

    /**
     * 获取错误信息
     * 
     * @return string 错误信息
     */
    public function getErrorMessage(): string
    {
        return sprintf('[%d] %s', $this->errorCode, $this->errorDesc);
    }

    /**
     * 获取交易流水号
     * 
     * @return string|null 交易流水号
     */
    public function getChargeId(): ?string
    {
        return $this->chargeId;
    }
}