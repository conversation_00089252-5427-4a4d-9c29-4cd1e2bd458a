<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'payment_no',
        'payment_method',
        'payment_amount',
        'voucher_deduct',
        'status',
        'paid_at',
        'refunded_at',
    ];

    protected $casts = [
        'payment_amount' => 'decimal:2',
        'voucher_deduct' => 'decimal:2',
        'paid_at' => 'datetime',
        'refunded_at' => 'datetime',
    ];

    // 支付状态常量
    const STATUS_PENDING = 0;   // 待支付
    const STATUS_PAID = 1;      // 已支付
    const STATUS_REFUNDED = 2;  // 已退款

    // 支付方式常量
    const METHOD_BALANCE = 'balance';   // 余额支付
    const METHOD_ALIPAY = 'alipay';     // 支付宝
    const METHOD_WECHAT = 'wechat';     // 微信支付

    /**
     * 状态文本映射
     */
    public static $statusTexts = [
        self::STATUS_PENDING => '待支付',
        self::STATUS_PAID => '已支付',
        self::STATUS_REFUNDED => '已退款',
    ];

    /**
     * 支付方式文本映射
     */
    public static $methodTexts = [
        self::METHOD_BALANCE => '余额支付',
        self::METHOD_ALIPAY => '支付宝',
        self::METHOD_WECHAT => '微信支付',
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::$statusTexts[$this->status] ?? '未知';
    }

    /**
     * 获取支付方式文本
     */
    public function getMethodTextAttribute()
    {
        return self::$methodTexts[$this->payment_method] ?? '未知';
    }

    /**
     * 获取实际支付金额
     */
    public function getActualAmountAttribute()
    {
        return $this->payment_amount - $this->voucher_deduct;
    }

    /**
     * 是否已支付
     */
    public function isPaid(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * 是否已退款
     */
    public function isRefunded(): bool
    {
        return $this->status === self::STATUS_REFUNDED;
    }

    /**
     * 是否可以退款
     */
    public function canRefund(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * 作用域：已支付的记录
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }

    /**
     * 作用域：按支付方式筛选
     */
    public function scopeByMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }
}
