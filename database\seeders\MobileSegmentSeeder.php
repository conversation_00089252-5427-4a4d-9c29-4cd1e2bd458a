<?php

namespace Database\Seeders;

use App\Models\MobileSegment;
use Illuminate\Database\Seeder;

class MobileSegmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $segments = [
            // 中国移动号段
            ['segment' => '1340000', 'carrier' => 'mobile', 'province' => '北京', 'city' => '北京'],
            ['segment' => '1341000', 'carrier' => 'mobile', 'province' => '天津', 'city' => '天津'],
            ['segment' => '1342000', 'carrier' => 'mobile', 'province' => '河北', 'city' => '石家庄'],
            ['segment' => '1343000', 'carrier' => 'mobile', 'province' => '山西', 'city' => '太原'],
            ['segment' => '1344000', 'carrier' => 'mobile', 'province' => '内蒙古', 'city' => '呼和浩特'],
            ['segment' => '1345000', 'carrier' => 'mobile', 'province' => '辽宁', 'city' => '沈阳'],
            ['segment' => '1346000', 'carrier' => 'mobile', 'province' => '吉林', 'city' => '长春'],
            ['segment' => '1347000', 'carrier' => 'mobile', 'province' => '黑龙江', 'city' => '哈尔滨'],
            ['segment' => '1348000', 'carrier' => 'mobile', 'province' => '上海', 'city' => '上海'],
            ['segment' => '1349000', 'carrier' => 'mobile', 'province' => '江苏', 'city' => '南京'],
            ['segment' => '1350000', 'carrier' => 'mobile', 'province' => '浙江', 'city' => '杭州'],
            ['segment' => '1351000', 'carrier' => 'mobile', 'province' => '安徽', 'city' => '合肥'],
            ['segment' => '1352000', 'carrier' => 'mobile', 'province' => '福建', 'city' => '福州'],
            ['segment' => '1357000', 'carrier' => 'mobile', 'province' => '山东', 'city' => '济南'],
            ['segment' => '1358000', 'carrier' => 'mobile', 'province' => '河南', 'city' => '郑州'],
            ['segment' => '1359000', 'carrier' => 'mobile', 'province' => '湖北', 'city' => '武汉'],
            ['segment' => '1470000', 'carrier' => 'mobile', 'province' => '湖南', 'city' => '长沙'],
            ['segment' => '1820000', 'carrier' => 'mobile', 'province' => '广东', 'city' => '广州'],
            ['segment' => '1821000', 'carrier' => 'mobile', 'province' => '广西', 'city' => '南宁'],
            ['segment' => '1822000', 'carrier' => 'mobile', 'province' => '海南', 'city' => '海口'],
            
            // 中国联通号段
            ['segment' => '1300000', 'carrier' => 'unicom', 'province' => '北京', 'city' => '北京'],
            ['segment' => '1301000', 'carrier' => 'unicom', 'province' => '天津', 'city' => '天津'],
            ['segment' => '1302000', 'carrier' => 'unicom', 'province' => '河北', 'city' => '石家庄'],
            ['segment' => '1310000', 'carrier' => 'unicom', 'province' => '山西', 'city' => '太原'],
            ['segment' => '1311000', 'carrier' => 'unicom', 'province' => '内蒙古', 'city' => '呼和浩特'],
            ['segment' => '1312000', 'carrier' => 'unicom', 'province' => '辽宁', 'city' => '沈阳'],
            ['segment' => '1320000', 'carrier' => 'unicom', 'province' => '吉林', 'city' => '长春'],
            ['segment' => '1450000', 'carrier' => 'unicom', 'province' => '黑龙江', 'city' => '哈尔滨'],
            ['segment' => '1550000', 'carrier' => 'unicom', 'province' => '上海', 'city' => '上海'],
            ['segment' => '1560000', 'carrier' => 'unicom', 'province' => '江苏', 'city' => '南京'],
            ['segment' => '1760000', 'carrier' => 'unicom', 'province' => '浙江', 'city' => '杭州'],
            ['segment' => '1850000', 'carrier' => 'unicom', 'province' => '安徽', 'city' => '合肥'],
            ['segment' => '1860000', 'carrier' => 'unicom', 'province' => '福建', 'city' => '福州'],
            ['segment' => '1960000', 'carrier' => 'unicom', 'province' => '山东', 'city' => '济南'],
            
            // 中国电信号段
            ['segment' => '1330000', 'carrier' => 'telecom', 'province' => '北京', 'city' => '北京'],
            ['segment' => '1331000', 'carrier' => 'telecom', 'province' => '天津', 'city' => '天津'],
            ['segment' => '1332000', 'carrier' => 'telecom', 'province' => '河北', 'city' => '石家庄'],
            ['segment' => '1490000', 'carrier' => 'telecom', 'province' => '山西', 'city' => '太原'],
            ['segment' => '1530000', 'carrier' => 'telecom', 'province' => '内蒙古', 'city' => '呼和浩特'],
            ['segment' => '1730000', 'carrier' => 'telecom', 'province' => '辽宁', 'city' => '沈阳'],
            ['segment' => '1770000', 'carrier' => 'telecom', 'province' => '吉林', 'city' => '长春'],
            ['segment' => '1800000', 'carrier' => 'telecom', 'province' => '黑龙江', 'city' => '哈尔滨'],
            ['segment' => '1810000', 'carrier' => 'telecom', 'province' => '上海', 'city' => '上海'],
            ['segment' => '1890000', 'carrier' => 'telecom', 'province' => '江苏', 'city' => '南京'],
            ['segment' => '1910000', 'carrier' => 'telecom', 'province' => '浙江', 'city' => '杭州'],
            ['segment' => '1930000', 'carrier' => 'telecom', 'province' => '安徽', 'city' => '合肥'],
            ['segment' => '1990000', 'carrier' => 'telecom', 'province' => '福建', 'city' => '福州'],
        ];

        foreach ($segments as $segment) {
            MobileSegment::create($segment);
        }
    }
}
