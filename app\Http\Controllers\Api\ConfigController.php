<?php

namespace App\Http\Controllers\Api;

use App\Services\ConfigService;
use App\Services\MobileCarrierService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ConfigController extends BaseApiController
{
    protected $configService;
    protected $mobileCarrierService;

    public function __construct(ConfigService $configService, MobileCarrierService $mobileCarrierService)
    {
        $this->configService = $configService;
        $this->mobileCarrierService = $mobileCarrierService;
    }

    /**
     * 获取系统配置信息
     */
    public function systemConfig()
    {
        try {
            $config = [
                'supported_amounts' => $this->configService->getSupportedAmounts(),
                'min_recharge_amount' => $this->configService->getMinRechargeAmount(),
                'max_recharge_amount' => $this->configService->getMaxRechargeAmount(),
                'recharge_fee_rate' => $this->configService->getRechargeFeeRate(),
                'supported_carriers' => $this->mobileCarrierService->getSupportedCarriers(),
                'site_name' => $this->configService->getSystemConfig('site_name', '话费充值系统'),
                'customer_service_phone' => $this->configService->getSystemConfig('customer_service_phone', '************'),
            ];

            return $this->success($config);

        } catch (\Exception $e) {
            return $this->serverError('获取系统配置失败');
        }
    }

    /**
     * 获取支持的充值金额
     */
    public function supportedAmounts()
    {
        try {
            $amounts = $this->configService->getSupportedAmounts();

            return $this->success([
                'amounts' => $amounts,
                'count' => count($amounts),
                'min_amount' => min($amounts),
                'max_amount' => max($amounts),
            ]);

        } catch (\Exception $e) {
            return $this->serverError('获取支持金额失败');
        }
    }

    /**
     * 获取支持的运营商
     */
    public function supportedCarriers()
    {
        try {
            $carriers = $this->mobileCarrierService->getSupportedCarriers();

            return $this->success([
                'carriers' => $carriers,
                'count' => count($carriers),
            ]);

        } catch (\Exception $e) {
            return $this->serverError('获取支持运营商失败');
        }
    }

    /**
     * 检查金额是否支持
     */
    public function checkAmount($amount)
    {
        try {
            $isSupported = $this->configService->isAmountSupported((float)$amount);
            $supportedAmounts = $this->configService->getSupportedAmounts();

            return $this->success([
                'amount' => (float)$amount,
                'is_supported' => $isSupported,
                'supported_amounts' => $supportedAmounts,
            ]);

        } catch (\Exception $e) {
            return $this->serverError('检查金额失败');
        }
    }

    /**
     * 计算充值手续费
     */
    public function calculateFee($amount)
    {
        try {
            $amount = (float)$amount;
            $fee = $this->configService->calculateRechargeFee($amount);
            $feeRate = $this->configService->getRechargeFeeRate();

            return $this->success([
                'amount' => $amount,
                'fee' => $fee,
                'fee_rate' => $feeRate,
                'total_amount' => $amount + $fee,
            ]);

        } catch (\Exception $e) {
            return $this->serverError('计算手续费失败');
        }
    }

    /**
     * API健康检查
     */
    public function healthCheck()
    {
        try {
            $status = [
                'status' => 'ok',
                'timestamp' => now()->toDateTimeString(),
                'version' => '1.0',
                'services' => [
                    'database' => $this->checkDatabase(),
                    'cache' => $this->checkCache(),
                    'config' => $this->checkConfig(),
                ],
            ];

            return $this->success($status);

        } catch (\Exception $e) {
            return $this->error('系统异常', 'SYSTEM_ERROR', 500);
        }
    }

    /**
     * 获取API使用说明
     */
    public function apiDocumentation()
    {
        try {
            $documentation = [
                'version' => '1.0',
                'base_url' => config('app.url') . '/api/v1',
                'authentication' => [
                    'type' => 'MD5签名',
                    'description' => '所有需要认证的接口都需要提供merchant_id和sign参数',
                ],
                'endpoints' => [
                    [
                        'name' => '创建充值订单',
                        'method' => 'POST',
                        'path' => '/recharge/create-order',
                        'auth_required' => true,
                        'description' => '创建话费充值订单',
                    ],
                    [
                        'name' => '查询订单状态',
                        'method' => 'POST',
                        'path' => '/recharge/query-order',
                        'auth_required' => true,
                        'description' => '查询充值订单状态',
                    ],
                    [
                        'name' => '查询商户余额',
                        'method' => 'GET',
                        'path' => '/balance',
                        'auth_required' => true,
                        'description' => '查询商户账户余额',
                    ],
                    [
                        'name' => '获取产品列表',
                        'method' => 'GET',
                        'path' => '/products',
                        'auth_required' => false,
                        'description' => '获取可购买的产品列表',
                    ],
                    [
                        'name' => '购买产品',
                        'method' => 'POST',
                        'path' => '/products/{id}/purchase',
                        'auth_required' => true,
                        'description' => '购买指定产品',
                    ],
                ],
                'response_format' => [
                    'success' => '是否成功（boolean）',
                    'code' => '响应码（string）',
                    'message' => '响应消息（string）',
                    'data' => '响应数据（object|array）',
                    'timestamp' => '响应时间（string）',
                ],
                'error_codes' => [
                    'SUCCESS' => '操作成功',
                    'VALIDATION_ERROR' => '参数验证失败',
                    'MISSING_MERCHANT_ID' => '缺少商户ID',
                    'MISSING_SIGNATURE' => '缺少签名参数',
                    'INVALID_SIGNATURE' => '签名验证失败',
                    'MERCHANT_NOT_FOUND' => '商户不存在',
                    'MERCHANT_DISABLED' => '商户已禁用',
                    'IP_NOT_ALLOWED' => 'IP地址不在白名单中',
                    'RATE_LIMIT_EXCEEDED' => '请求频率超限',
                    'INSUFFICIENT_BALANCE' => '商户余额不足',
                    'UNSUPPORTED_AMOUNT' => '不支持的充值金额',
                    'ORDER_CREATE_FAILED' => '订单创建失败',
                    'NOT_FOUND' => '资源不存在',
                    'SERVER_ERROR' => '服务器内部错误',
                ],
            ];

            return $this->success($documentation);

        } catch (\Exception $e) {
            return $this->serverError('获取API文档失败');
        }
    }

    /**
     * 检查数据库连接
     */
    private function checkDatabase(): string
    {
        try {
            \DB::connection()->getPdo();
            return 'ok';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * 检查缓存
     */
    private function checkCache(): string
    {
        try {
            \Cache::put('health_check', 'test', 1);
            $value = \Cache::get('health_check');
            return $value === 'test' ? 'ok' : 'error';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * 检查配置
     */
    private function checkConfig(): string
    {
        try {
            $amounts = $this->configService->getSupportedAmounts();
            return count($amounts) > 0 ? 'ok' : 'error';
        } catch (\Exception $e) {
            return 'error';
        }
    }
}
