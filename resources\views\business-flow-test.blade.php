<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务流程测试</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section {
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }
        .test-result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-success {
            border-left: 4px solid #28a745;
            background-color: #d4edda;
        }
        .test-error {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
        }
        .test-info {
            border-left: 4px solid #17a2b8;
            background-color: #d1ecf1;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">业务流程测试</h1>
        <p class="text-muted">测试系统核心业务流程，确保功能正常</p>

        <!-- 系统健康检查 -->
        <div class="test-section">
            <h3>系统健康检查</h3>
            <button class="btn btn-primary" onclick="testSystemHealth()">检查系统状态</button>
            <div class="test-result mt-3" id="healthResult">等待测试...</div>
        </div>

        <!-- 配置服务测试 -->
        <div class="test-section">
            <h3>配置服务测试</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testSupportedAmounts()">测试支持金额</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testSupportedCarriers()">测试支持运营商</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testSystemConfig()">测试系统配置</button>
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testRechargeConfig()">测试充值配置</button>
                </div>
            </div>
            <div class="test-result mt-3" id="configResult">等待测试...</div>
        </div>

        <!-- 产品服务测试 -->
        <div class="test-section">
            <h3>产品服务测试</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-success w-100 mb-2" onclick="testProductList()">测试产品列表</button>
                    <button class="btn btn-outline-success w-100 mb-2" onclick="testProductDetail()">测试产品详情</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-warning w-100 mb-2" onclick="testPopularProducts()">测试热门产品</button>
                    <button class="btn btn-outline-warning w-100 mb-2" onclick="testProductCategories()">测试产品分类</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-danger w-100 mb-2" onclick="testProductPurchase()">测试产品购买</button>
                </div>
            </div>
            <div class="test-result mt-3" id="productResult">等待测试...</div>
        </div>

        <!-- 充值服务测试 -->
        <div class="test-section">
            <h3>充值服务测试</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testCarrierInfo()">测试运营商查询</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testCreateRechargeOrder()">测试创建充值订单</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testQueryOrder()">测试订单查询</button>
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testPayOrder()">测试订单支付</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-success w-100 mb-2" onclick="testCompleteFlow()">测试完整流程</button>
                </div>
            </div>
            <div class="test-result mt-3" id="rechargeResult">等待测试...</div>
        </div>

        <!-- 用户服务测试 -->
        <div class="test-section">
            <h3>用户服务测试</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-2">
                        <input type="number" id="testUserId" class="form-control" placeholder="测试用户ID" value="1">
                    </div>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testUserProfile()">测试用户资料</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testUserStatistics()">测试用户统计</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testUserVouchers()">测试用户券</button>
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testUserOrders()">测试用户订单</button>
                </div>
            </div>
            <div class="test-result mt-3" id="userResult">等待测试...</div>
        </div>

        <!-- 全部测试 -->
        <div class="test-section">
            <h3>综合测试</h3>
            <button class="btn btn-success btn-lg" onclick="runAllTests()">运行所有测试</button>
            <button class="btn btn-warning btn-lg ms-2" onclick="clearResults()">清除结果</button>
            <div class="test-result mt-3" id="allTestsResult">等待测试...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 设置CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        let testResults = [];

        // 显示测试结果
        function showResult(elementId, result, success = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `test-result mt-3 ${success ? 'test-success' : 'test-error'}`;
        }

        // 显示测试信息
        function showInfo(elementId, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'test-result mt-3 test-info';
        }

        // 测试API
        function testApi(url, method = 'GET', data = null) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: url,
                    method: method,
                    data: data,
                    dataType: 'json'
                }).done(function(response) {
                    resolve({ success: true, data: response });
                }).fail(function(xhr) {
                    resolve({ success: false, data: xhr.responseJSON || { error: 'Request failed' } });
                });
            });
        }

        // 系统健康检查
        async function testSystemHealth() {
            showInfo('healthResult', '正在检查系统健康状态...');
            const result = await testApi('/api/v1/health');
            showResult('healthResult', result.data, result.success);
            testResults.push({ test: '系统健康检查', success: result.success, result: result.data });
        }

        // 测试支持金额
        async function testSupportedAmounts() {
            showInfo('configResult', '正在测试支持的充值金额...');
            const result = await testApi('/api/v1/config/supported-amounts');
            showResult('configResult', result.data, result.success);
        }

        // 测试支持运营商
        async function testSupportedCarriers() {
            showInfo('configResult', '正在测试支持的运营商...');
            const result = await testApi('/api/v1/config/supported-carriers');
            showResult('configResult', result.data, result.success);
        }

        // 测试系统配置
        async function testSystemConfig() {
            showInfo('configResult', '正在测试系统配置...');
            const result = await testApi('/api/v1/config/system');
            showResult('configResult', result.data, result.success);
        }

        // 测试充值配置
        async function testRechargeConfig() {
            showInfo('configResult', '正在测试充值配置...');
            const result = await testApi('/api/v1/frontend/recharge/config');
            showResult('configResult', result.data, result.success);
        }

        // 测试产品列表
        async function testProductList() {
            showInfo('productResult', '正在测试产品列表...');
            const result = await testApi('/api/v1/frontend/products');
            showResult('productResult', result.data, result.success);
        }

        // 测试产品详情
        async function testProductDetail() {
            showInfo('productResult', '正在测试产品详情...');
            const result = await testApi('/api/v1/frontend/products/1');
            showResult('productResult', result.data, result.success);
        }

        // 测试热门产品
        async function testPopularProducts() {
            showInfo('productResult', '正在测试热门产品...');
            const result = await testApi('/api/v1/frontend/products/popular');
            showResult('productResult', result.data, result.success);
        }

        // 测试产品分类
        async function testProductCategories() {
            showInfo('productResult', '正在测试产品分类...');
            const result = await testApi('/api/v1/frontend/products/categories');
            showResult('productResult', result.data, result.success);
        }

        // 测试产品购买
        async function testProductPurchase() {
            showInfo('productResult', '正在测试产品购买...');
            const result = await testApi('/api/v1/frontend/products/1/purchase', 'POST', JSON.stringify({
                user_id: 1,
                quantity: 1
            }));
            showResult('productResult', result.data, result.success);
        }

        // 测试运营商查询
        async function testCarrierInfo() {
            showInfo('rechargeResult', '正在测试运营商查询...');
            const result = await testApi('/api/v1/frontend/recharge/carrier-info', 'POST', JSON.stringify({
                mobile: '13800138000'
            }));
            showResult('rechargeResult', result.data, result.success);
        }

        // 测试创建充值订单
        async function testCreateRechargeOrder() {
            showInfo('rechargeResult', '正在测试创建充值订单...');
            const result = await testApi('/api/v1/frontend/recharge/create-order', 'POST', JSON.stringify({
                mobile: '13800138000',
                amount: 10,
                user_id: 1
            }));
            showResult('rechargeResult', result.data, result.success);
        }

        // 测试订单查询
        async function testQueryOrder() {
            showInfo('rechargeResult', '正在测试订单查询...');
            const result = await testApi('/api/v1/frontend/recharge/query-order', 'POST', JSON.stringify({
                order_no: 'ORD20250621120000001'
            }));
            showResult('rechargeResult', result.data, result.success);
        }

        // 测试订单支付
        async function testPayOrder() {
            showInfo('rechargeResult', '正在测试订单支付...');
            const result = await testApi('/api/v1/frontend/orders/1/pay', 'POST', JSON.stringify({
                payment_method: 'alipay'
            }));
            showResult('rechargeResult', result.data, result.success);
        }

        // 测试用户资料
        async function testUserProfile() {
            const userId = document.getElementById('testUserId').value;
            showInfo('userResult', '正在测试用户资料...');
            const result = await testApi(`/api/v1/users/${userId}/profile`);
            showResult('userResult', result.data, result.success);
        }

        // 测试用户统计
        async function testUserStatistics() {
            const userId = document.getElementById('testUserId').value;
            showInfo('userResult', '正在测试用户统计...');
            const result = await testApi(`/api/v1/users/${userId}/statistics`);
            showResult('userResult', result.data, result.success);
        }

        // 测试用户券
        async function testUserVouchers() {
            const userId = document.getElementById('testUserId').value;
            showInfo('userResult', '正在测试用户券...');
            const result = await testApi(`/api/v1/users/${userId}/vouchers`);
            showResult('userResult', result.data, result.success);
        }

        // 测试用户订单
        async function testUserOrders() {
            const userId = document.getElementById('testUserId').value;
            showInfo('userResult', '正在测试用户订单...');
            const result = await testApi(`/api/v1/users/${userId}/orders`);
            showResult('userResult', result.data, result.success);
        }

        // 测试完整流程
        async function testCompleteFlow() {
            showInfo('rechargeResult', '正在测试完整充值流程...');
            
            try {
                // 1. 查询运营商
                const carrierResult = await testApi('/api/v1/frontend/recharge/carrier-info', 'POST', JSON.stringify({
                    mobile: '13800138000'
                }));
                
                if (!carrierResult.success) {
                    throw new Error('运营商查询失败');
                }

                // 2. 创建订单
                const orderResult = await testApi('/api/v1/frontend/recharge/create-order', 'POST', JSON.stringify({
                    mobile: '13800138000',
                    amount: 10,
                    user_id: 1
                }));

                if (!orderResult.success) {
                    throw new Error('订单创建失败');
                }

                const flowResult = {
                    step1_carrier: carrierResult.data,
                    step2_order: orderResult.data,
                    flow_status: 'success'
                };

                showResult('rechargeResult', flowResult, true);
            } catch (error) {
                showResult('rechargeResult', { error: error.message }, false);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            showInfo('allTestsResult', '正在运行所有测试...');
            testResults = [];

            const tests = [
                { name: '系统健康检查', func: testSystemHealth },
                { name: '支持金额测试', func: testSupportedAmounts },
                { name: '产品列表测试', func: testProductList },
                { name: '运营商查询测试', func: testCarrierInfo },
                { name: '用户资料测试', func: testUserProfile },
            ];

            for (const test of tests) {
                try {
                    await test.func();
                    await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
                } catch (error) {
                    testResults.push({ test: test.name, success: false, error: error.message });
                }
            }

            const summary = {
                total_tests: testResults.length,
                passed: testResults.filter(r => r.success).length,
                failed: testResults.filter(r => !r.success).length,
                results: testResults
            };

            showResult('allTestsResult', summary, summary.failed === 0);
        }

        // 清除结果
        function clearResults() {
            const resultElements = document.querySelectorAll('.test-result');
            resultElements.forEach(element => {
                element.textContent = '等待测试...';
                element.className = 'test-result mt-3';
            });
            testResults = [];
        }
    </script>
</body>
</html>
