<?php

namespace Database\Seeders;

use App\Models\RechargeProvider;
use Illuminate\Database\Seeder;

class RechargeProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $providers = [
            [
                'code' => 'unicom_api',
                'name' => '联通充值接口',
                'api_url' => 'https://api.unicom.com/recharge',
                'app_id' => 'unicom_app_001',
                'app_secret' => 'unicom_secret_key_123456',
                'config' => [
                    'carriers' => ['unicom'],
                    'supported_amounts' => [5, 10, 20, 30, 50, 100, 200, 300, 500],
                    'timeout' => 30,
                    'max_retry' => 3,
                ],
                'weight' => 100,
                'status' => true,
                'success_rate' => 95.50,
                'avg_duration' => 2500,
            ],
            [
                'code' => 'mobile_api',
                'name' => '移动充值接口',
                'api_url' => 'https://api.mobile.com/recharge',
                'app_id' => 'mobile_app_001',
                'app_secret' => 'mobile_secret_key_123456',
                'config' => [
                    'carriers' => ['mobile'],
                    'supported_amounts' => [5, 10, 20, 30, 50, 100, 200, 300, 500],
                    'timeout' => 30,
                    'max_retry' => 3,
                ],
                'weight' => 90,
                'status' => true,
                'success_rate' => 94.20,
                'avg_duration' => 3000,
            ],
            [
                'code' => 'telecom_api',
                'name' => '电信充值接口',
                'api_url' => 'https://api.telecom.com/recharge',
                'app_id' => 'telecom_app_001',
                'app_secret' => 'telecom_secret_key_123456',
                'config' => [
                    'carriers' => ['telecom'],
                    'supported_amounts' => [5, 10, 20, 30, 50, 100, 200, 300, 500],
                    'timeout' => 30,
                    'max_retry' => 3,
                ],
                'weight' => 85,
                'status' => true,
                'success_rate' => 93.80,
                'avg_duration' => 2800,
            ],
            [
                'code' => 'universal_api',
                'name' => '通用充值接口',
                'api_url' => 'https://api.universal.com/recharge',
                'app_id' => 'universal_app_001',
                'app_secret' => 'universal_secret_key_123456',
                'config' => [
                    'carriers' => ['mobile', 'unicom', 'telecom'],
                    'supported_amounts' => [5, 10, 20, 30, 50, 100, 200, 300, 500],
                    'timeout' => 45,
                    'max_retry' => 3,
                ],
                'weight' => 70,
                'status' => true,
                'success_rate' => 92.10,
                'avg_duration' => 3500,
            ],
            [
                'code' => 'backup_api',
                'name' => '备用充值接口',
                'api_url' => 'https://api.backup.com/recharge',
                'app_id' => 'backup_app_001',
                'app_secret' => 'backup_secret_key_123456',
                'config' => [
                    'carriers' => ['mobile', 'unicom', 'telecom'],
                    'supported_amounts' => [10, 20, 30, 50, 100],
                    'timeout' => 60,
                    'max_retry' => 5,
                ],
                'weight' => 50,
                'status' => false, // 默认禁用，作为备用
                'success_rate' => 88.50,
                'avg_duration' => 4000,
            ],
        ];

        foreach ($providers as $provider) {
            RechargeProvider::create($provider);
        }
    }
}
