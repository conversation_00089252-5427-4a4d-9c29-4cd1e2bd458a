<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 数据库优化配置
    |--------------------------------------------------------------------------
    |
    | 这里配置数据库性能优化相关的参数
    |
    */

    // 查询优化配置
    'query_optimization' => [
        // 是否启用查询缓存
        'enable_cache' => env('DB_QUERY_CACHE', true),
        
        // 缓存时间（秒）
        'cache_ttl' => env('DB_CACHE_TTL', 300),
        
        // 慢查询阈值（毫秒）
        'slow_query_threshold' => env('DB_SLOW_QUERY_THRESHOLD', 1000),
        
        // 是否记录慢查询
        'log_slow_queries' => env('DB_LOG_SLOW_QUERIES', true),
        
        // 查询结果集大小限制
        'max_result_size' => env('DB_MAX_RESULT_SIZE', 10000),
    ],

    // 连接池配置
    'connection_pool' => [
        // 最大连接数
        'max_connections' => env('DB_MAX_CONNECTIONS', 100),
        
        // 最小连接数
        'min_connections' => env('DB_MIN_CONNECTIONS', 10),
        
        // 连接超时时间（秒）
        'connection_timeout' => env('DB_CONNECTION_TIMEOUT', 30),
        
        // 空闲连接超时时间（秒）
        'idle_timeout' => env('DB_IDLE_TIMEOUT', 600),
    ],

    // 索引优化建议
    'index_recommendations' => [
        'orders' => [
            'primary_queries' => [
                'merchant_status_time' => ['merchant_id', 'status', 'created_at'],
                'user_status_time' => ['user_id', 'status', 'created_at'],
                'mobile_time' => ['mobile', 'created_at'],
                'type_status' => ['order_type', 'status'],
            ],
            'covering_indexes' => [
                'order_summary' => ['merchant_id', 'status', 'amount', 'created_at'],
            ],
        ],
        'user_vouchers' => [
            'primary_queries' => [
                'user_available' => ['user_id', 'status', 'expired_at'],
                'user_value' => ['user_id', 'status', 'voucher_value'],
                'product_status' => ['product_id', 'status'],
            ],
        ],
        'recharge_records' => [
            'primary_queries' => [
                'order_status' => ['order_id', 'status'],
                'mobile_time' => ['mobile', 'created_at'],
                'status_time' => ['status', 'created_at'],
            ],
        ],
    ],

    // 分区配置建议
    'partitioning' => [
        'orders' => [
            'type' => 'range',
            'column' => 'created_at',
            'interval' => 'monthly',
            'retention' => '2 years',
        ],
        'order_logs' => [
            'type' => 'range',
            'column' => 'created_at',
            'interval' => 'monthly',
            'retention' => '1 year',
        ],
        'merchant_balance_logs' => [
            'type' => 'range',
            'column' => 'created_at',
            'interval' => 'monthly',
            'retention' => '2 years',
        ],
        'notification_logs' => [
            'type' => 'range',
            'column' => 'created_at',
            'interval' => 'monthly',
            'retention' => '6 months',
        ],
    ],

    // 数据归档配置
    'archiving' => [
        'enabled' => env('DB_ARCHIVING_ENABLED', false),
        'tables' => [
            'orders' => [
                'archive_after' => '1 year',
                'conditions' => ['status' => ['completed', 'cancelled']],
            ],
            'order_logs' => [
                'archive_after' => '6 months',
                'conditions' => [],
            ],
            'notification_logs' => [
                'archive_after' => '3 months',
                'conditions' => [],
            ],
        ],
    ],

    // 读写分离配置
    'read_write_splitting' => [
        'enabled' => env('DB_READ_WRITE_SPLIT', false),
        'read_connections' => [
            'weight' => [
                'read1' => 50,
                'read2' => 30,
                'read3' => 20,
            ],
        ],
        'write_connection' => 'master',
        'sticky_reads' => true,
        'read_timeout' => 5,
    ],

    // 数据库监控配置
    'monitoring' => [
        'enabled' => env('DB_MONITORING_ENABLED', true),
        'metrics' => [
            'query_count' => true,
            'slow_queries' => true,
            'connection_count' => true,
            'deadlocks' => true,
            'table_locks' => true,
        ],
        'alerts' => [
            'slow_query_threshold' => 2000, // 毫秒
            'connection_threshold' => 80, // 百分比
            'deadlock_threshold' => 5, // 每分钟
        ],
    ],

    // 优化建议
    'optimization_tips' => [
        'general' => [
            '使用适当的数据类型，避免过大的字段',
            '为经常查询的字段添加索引',
            '避免SELECT *，只查询需要的字段',
            '使用LIMIT限制查询结果数量',
            '定期分析和优化慢查询',
        ],
        'indexing' => [
            '为WHERE、ORDER BY、GROUP BY字段添加索引',
            '复合索引的字段顺序很重要',
            '避免在小表上创建过多索引',
            '定期检查和删除未使用的索引',
        ],
        'queries' => [
            '使用EXISTS代替IN进行子查询',
            '避免在WHERE子句中使用函数',
            '使用UNION ALL代替UNION（如果不需要去重）',
            '合理使用JOIN，避免笛卡尔积',
        ],
    ],
];
