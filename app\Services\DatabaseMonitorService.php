<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class DatabaseMonitorService
{
    protected $config;

    public function __construct()
    {
        $this->config = config('database_optimization');
    }

    /**
     * 监控数据库性能
     */
    public function monitor(): array
    {
        $metrics = [];

        if ($this->config['monitoring']['enabled']) {
            $metrics = [
                'connection_count' => $this->getConnectionCount(),
                'slow_queries' => $this->getSlowQueries(),
                'table_sizes' => $this->getTableSizes(),
                'index_usage' => $this->getIndexUsage(),
                'deadlocks' => $this->getDeadlockCount(),
                'query_cache_hit_rate' => $this->getQueryCacheHitRate(),
                'timestamp' => now()->toDateTimeString(),
            ];

            // 检查告警阈值
            $this->checkAlerts($metrics);

            // 缓存监控数据
            Cache::put('db_monitor_metrics', $metrics, 300);
        }

        return $metrics;
    }

    /**
     * 获取连接数
     */
    protected function getConnectionCount(): int
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            return (int) $result[0]->Value ?? 0;
        } catch (\Exception $e) {
            Log::warning('Failed to get connection count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取慢查询数量
     */
    protected function getSlowQueries(): array
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Slow_queries'");
            $slowQueries = (int) $result[0]->Value ?? 0;

            // 获取慢查询日志（如果启用）
            $recentSlowQueries = [];
            if ($this->config['query_optimization']['log_slow_queries']) {
                // 这里可以读取慢查询日志文件或从专门的慢查询表中获取
                $recentSlowQueries = $this->getRecentSlowQueries();
            }

            return [
                'total' => $slowQueries,
                'recent' => $recentSlowQueries,
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to get slow queries: ' . $e->getMessage());
            return ['total' => 0, 'recent' => []];
        }
    }

    /**
     * 获取表大小信息
     */
    protected function getTableSizes(): array
    {
        try {
            $database = config('database.connections.mysql.database');
            $query = "
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    table_rows
                FROM information_schema.tables 
                WHERE table_schema = ? 
                AND table_type = 'BASE TABLE'
                ORDER BY (data_length + index_length) DESC
                LIMIT 10
            ";

            $results = DB::select($query, [$database]);
            return collect($results)->map(function ($row) {
                return [
                    'table' => $row->table_name,
                    'size_mb' => $row->size_mb,
                    'rows' => $row->table_rows,
                ];
            })->toArray();
        } catch (\Exception $e) {
            Log::warning('Failed to get table sizes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取索引使用情况
     */
    protected function getIndexUsage(): array
    {
        try {
            $database = config('database.connections.mysql.database');
            $query = "
                SELECT 
                    t.table_name,
                    t.index_name,
                    t.cardinality,
                    s.rows_examined,
                    s.rows_sent
                FROM information_schema.statistics t
                LEFT JOIN information_schema.tables s ON t.table_name = s.table_name
                WHERE t.table_schema = ?
                AND t.index_name != 'PRIMARY'
                ORDER BY t.cardinality DESC
                LIMIT 20
            ";

            $results = DB::select($query, [$database]);
            return collect($results)->map(function ($row) {
                return [
                    'table' => $row->table_name,
                    'index' => $row->index_name,
                    'cardinality' => $row->cardinality,
                ];
            })->toArray();
        } catch (\Exception $e) {
            Log::warning('Failed to get index usage: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取死锁数量
     */
    protected function getDeadlockCount(): int
    {
        try {
            $result = DB::select("SHOW ENGINE INNODB STATUS");
            $status = $result[0]->Status ?? '';
            
            // 解析死锁信息
            preg_match('/Number of deadlocks: (\d+)/', $status, $matches);
            return (int) ($matches[1] ?? 0);
        } catch (\Exception $e) {
            Log::warning('Failed to get deadlock count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取查询缓存命中率
     */
    protected function getQueryCacheHitRate(): float
    {
        try {
            $hits = DB::select("SHOW STATUS LIKE 'Qcache_hits'");
            $inserts = DB::select("SHOW STATUS LIKE 'Qcache_inserts'");
            
            $hitCount = (int) ($hits[0]->Value ?? 0);
            $insertCount = (int) ($inserts[0]->Value ?? 0);
            
            $total = $hitCount + $insertCount;
            return $total > 0 ? round(($hitCount / $total) * 100, 2) : 0;
        } catch (\Exception $e) {
            Log::warning('Failed to get query cache hit rate: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * 获取最近的慢查询
     */
    protected function getRecentSlowQueries(): array
    {
        // 这里可以实现读取慢查询日志的逻辑
        // 或者从专门的慢查询监控表中获取数据
        return [];
    }

    /**
     * 检查告警阈值
     */
    protected function checkAlerts(array $metrics): void
    {
        $alerts = $this->config['monitoring']['alerts'];

        // 检查连接数告警
        $maxConnections = $this->getMaxConnections();
        if ($maxConnections > 0) {
            $connectionUsage = ($metrics['connection_count'] / $maxConnections) * 100;
            if ($connectionUsage > $alerts['connection_threshold']) {
                $this->sendAlert('high_connection_usage', [
                    'current' => $metrics['connection_count'],
                    'max' => $maxConnections,
                    'usage_percent' => $connectionUsage,
                ]);
            }
        }

        // 检查死锁告警
        if ($metrics['deadlocks'] > $alerts['deadlock_threshold']) {
            $this->sendAlert('high_deadlock_count', [
                'count' => $metrics['deadlocks'],
                'threshold' => $alerts['deadlock_threshold'],
            ]);
        }
    }

    /**
     * 获取最大连接数
     */
    protected function getMaxConnections(): int
    {
        try {
            $result = DB::select("SHOW VARIABLES LIKE 'max_connections'");
            return (int) ($result[0]->Value ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 发送告警
     */
    protected function sendAlert(string $type, array $data): void
    {
        Log::warning("Database alert: {$type}", $data);
        
        // 这里可以集成邮件、短信、钉钉等告警方式
        // 例如：
        // event(new DatabaseAlertEvent($type, $data));
    }

    /**
     * 获取优化建议
     */
    public function getOptimizationSuggestions(): array
    {
        $suggestions = [];
        $metrics = Cache::get('db_monitor_metrics', []);

        if (empty($metrics)) {
            return ['请先运行监控以获取优化建议'];
        }

        // 基于监控数据生成建议
        if (isset($metrics['table_sizes'])) {
            foreach ($metrics['table_sizes'] as $table) {
                if ($table['size_mb'] > 1000) { // 大于1GB
                    $suggestions[] = "表 {$table['table']} 大小为 {$table['size_mb']}MB，建议考虑分区或归档";
                }
            }
        }

        if (isset($metrics['query_cache_hit_rate']) && $metrics['query_cache_hit_rate'] < 80) {
            $suggestions[] = "查询缓存命中率较低({$metrics['query_cache_hit_rate']}%)，建议优化查询或调整缓存配置";
        }

        if (isset($metrics['slow_queries']['total']) && $metrics['slow_queries']['total'] > 100) {
            $suggestions[] = "慢查询数量较多({$metrics['slow_queries']['total']})，建议分析和优化慢查询";
        }

        return array_merge($suggestions, $this->config['optimization_tips']['general']);
    }
}
