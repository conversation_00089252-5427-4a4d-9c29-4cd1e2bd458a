<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductRule;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductService
{
    /**
     * 获取可购买的产品列表
     */
    public function getAvailableProducts(): array
    {
        return Product::online()
            ->with(['vouchers', 'rules'])
            ->orderBy('sort_order')
            ->get()
            ->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'code' => $product->code,
                    'price' => $product->price,
                    'cost' => $product->cost,
                    'voucher_total_value' => $product->voucher_total_value,
                    'description' => $product->description,
                    'vouchers' => $product->vouchers->map(function ($voucher) {
                        return [
                            'value' => $voucher->voucher_value,
                            'quantity' => $voucher->voucher_quantity,
                        ];
                    }),
                    'rules' => $this->formatProductRules($product->rules),
                    'can_purchase' => $this->canPurchase($product),
                ];
            })
            ->toArray();
    }

    /**
     * 获取产品详情
     */
    public function getProductDetail($productId): ?array
    {
        $product = Product::with(['vouchers', 'rules'])->find($productId);
        
        if (!$product || !$product->isOnline()) {
            return null;
        }

        return [
            'id' => $product->id,
            'name' => $product->name,
            'code' => $product->code,
            'price' => $product->price,
            'cost' => $product->cost,
            'voucher_total_value' => $product->voucher_total_value,
            'description' => $product->description,
            'sales_count' => $product->sales_count,
            'vouchers' => $product->vouchers->map(function ($voucher) {
                return [
                    'value' => $voucher->voucher_value,
                    'quantity' => $voucher->voucher_quantity,
                ];
            }),
            'rules' => $this->formatProductRules($product->rules),
            'can_purchase' => $this->canPurchase($product),
        ];
    }

    /**
     * 验证产品是否可以购买
     */
    public function canPurchase(Product $product, $userId = null, $quantity = 1): bool
    {
        // 检查产品状态
        if (!$product->isOnline()) {
            return false;
        }

        // 检查库存
        if (!$product->checkStock()) {
            return false;
        }

        // 验证产品规则
        $context = [
            'user_id' => $userId,
            'quantity' => $quantity,
        ];

        $errors = $product->validateRules($context);
        
        return empty($errors);
    }

    /**
     * 验证购买请求
     */
    public function validatePurchase($productId, $userId, $quantity = 1): array
    {
        $product = Product::find($productId);
        
        if (!$product) {
            return ['success' => false, 'message' => '产品不存在'];
        }

        if (!$product->isOnline()) {
            return ['success' => false, 'message' => '产品已下架'];
        }

        if (!$product->checkStock()) {
            return ['success' => false, 'message' => '产品库存不足'];
        }

        // 验证产品规则
        $context = [
            'user_id' => $userId,
            'quantity' => $quantity,
        ];

        $errors = $product->validateRules($context);
        
        if (!empty($errors)) {
            return ['success' => false, 'message' => implode('；', $errors)];
        }

        return ['success' => true, 'product' => $product];
    }

    /**
     * 计算购买总价
     */
    public function calculateTotalPrice($productId, $quantity = 1): array
    {
        $product = Product::find($productId);
        
        if (!$product) {
            return ['success' => false, 'message' => '产品不存在'];
        }

        $totalPrice = $product->price * $quantity;
        $totalCost = $product->cost * $quantity;
        $totalVoucherValue = $product->voucher_total_value * $quantity;

        return [
            'success' => true,
            'data' => [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'unit_price' => $product->price,
                'quantity' => $quantity,
                'total_price' => $totalPrice,
                'total_cost' => $totalCost,
                'total_voucher_value' => $totalVoucherValue,
                'profit' => $totalPrice - $totalCost,
            ]
        ];
    }

    /**
     * 更新产品销量
     */
    public function updateSalesCount($productId, $quantity = 1): bool
    {
        try {
            $product = Product::find($productId);
            if (!$product) {
                return false;
            }

            $product->increment('sales_count', $quantity);
            
            Log::info('产品销量更新', [
                'product_id' => $productId,
                'quantity' => $quantity,
                'new_sales_count' => $product->fresh()->sales_count,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('更新产品销量失败', [
                'product_id' => $productId,
                'quantity' => $quantity,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 格式化产品规则
     */
    private function formatProductRules($rules): array
    {
        return $rules->map(function ($rule) {
            return [
                'type' => $rule->rule_type,
                'type_text' => $rule->type_text,
                'description' => $rule->description,
                'value' => $rule->rule_value,
            ];
        })->toArray();
    }

    /**
     * 获取产品的券配置
     */
    public function getProductVouchers($productId): array
    {
        $product = Product::with('vouchers')->find($productId);
        
        if (!$product) {
            return [];
        }

        return $product->vouchers->map(function ($voucher) {
            return [
                'value' => $voucher->voucher_value,
                'quantity' => $voucher->voucher_quantity,
                'total_value' => $voucher->voucher_value * $voucher->voucher_quantity,
            ];
        })->toArray();
    }

    /**
     * 检查产品规则限制
     */
    public function checkProductLimits($productId, $userId): array
    {
        $product = Product::with('rules')->find($productId);
        
        if (!$product) {
            return ['can_purchase' => false, 'reason' => '产品不存在'];
        }

        // 检查每人限购规则
        $limitRule = $product->rules->where('rule_type', ProductRule::TYPE_LIMIT_PER_USER)->first();
        if ($limitRule) {
            $maxCount = $limitRule->rule_value['max_count'] ?? 0;
            $purchaseCount = Order::where('user_id', $userId)
                ->where('product_id', $productId)
                ->whereIn('status', [Order::STATUS_PAID, Order::STATUS_SUCCESS])
                ->count();
                
            if ($purchaseCount >= $maxCount) {
                return [
                    'can_purchase' => false, 
                    'reason' => "您已购买{$purchaseCount}次，超过限购{$maxCount}次"
                ];
            }
            
            return [
                'can_purchase' => true,
                'remaining_count' => $maxCount - $purchaseCount,
                'max_count' => $maxCount,
            ];
        }

        return ['can_purchase' => true];
    }

    /**
     * 获取热门产品
     */
    public function getPopularProducts($limit = 10): array
    {
        return Product::online()
            ->orderBy('sales_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->price,
                    'voucher_total_value' => $product->voucher_total_value,
                    'sales_count' => $product->sales_count,
                ];
            })
            ->toArray();
    }
}
