<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_logs', function (Blueprint $table) {
            $table->id();
            $table->string('type', 50)->comment('通知类型：order_status,recharge_result,system_alert等');
            $table->string('channel', 20)->comment('通知渠道：http,email,sms,webhook');
            $table->string('recipient', 255)->comment('接收者（URL、邮箱、手机号等）');
            $table->string('subject', 255)->nullable()->comment('通知主题');
            $table->text('content')->comment('通知内容');
            $table->json('headers')->nullable()->comment('请求头（HTTP通知）');
            $table->json('params')->nullable()->comment('请求参数');
            $table->tinyInteger('status')->default(0)->comment('发送状态：0-待发送，1-发送中，2-成功，3-失败');
            $table->integer('retry_times')->default(0)->comment('重试次数');
            $table->integer('max_retry')->default(3)->comment('最大重试次数');
            $table->timestamp('next_retry_at')->nullable()->comment('下次重试时间');
            $table->timestamp('sent_at')->nullable()->comment('发送时间');
            $table->text('response')->nullable()->comment('响应内容');
            $table->string('error_msg', 500)->nullable()->comment('错误信息');
            $table->string('related_type', 50)->nullable()->comment('关联类型');
            $table->unsignedBigInteger('related_id')->nullable()->comment('关联ID');
            $table->timestamps();
            
            $table->index(['type', 'status']);
            $table->index(['status', 'next_retry_at']);
            $table->index(['related_type', 'related_id']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_logs');
    }
};
