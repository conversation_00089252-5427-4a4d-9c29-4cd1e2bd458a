<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RechargeRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'provider_id',
        'provider',
        'provider_order_no',
        'charge_id',
        'mobile',
        'amount',
        'carrier',
        'province',
        'city',
        'status',
        'request_data',
        'response_data',
        'provider_response',
        'retry_times',
        'error_code',
        'error_msg',
        'error_message',
        'submitted_at',
        'completed_at',
    ];

    protected $casts = [
        'request_data' => 'json',
        'response_data' => 'json',
        'provider_response' => 'json',
        'amount' => 'decimal:2',
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // 充值状态
    const STATUS_PENDING = 0;      // 待处理
    const STATUS_SUBMITTING = 1;   // 提交中
    const STATUS_SUBMITTED = 2;    // 已提交
    const STATUS_PROCESSING = 3;   // 处理中
    const STATUS_SUCCESS = 10;     // 成功
    const STATUS_FAILED = 20;      // 失败

    /**
     * 状态文本映射
     */
    public static $statusTexts = [
        self::STATUS_PENDING => '待处理',
        self::STATUS_SUBMITTING => '提交中',
        self::STATUS_SUBMITTED => '已提交',
        self::STATUS_PROCESSING => '处理中',
        self::STATUS_SUCCESS => '成功',
        self::STATUS_FAILED => '失败',
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联充值渠道
     */
    public function rechargeProvider()
    {
        return $this->belongsTo(RechargeProvider::class, 'provider', 'code');
    }

    /**
     * 关联充值渠道（通过ID）
     */
    public function provider()
    {
        return $this->belongsTo(RechargeProvider::class, 'provider_id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::$statusTexts[$this->status] ?? '未知';
    }

    /**
     * 是否可重试
     */
    public function canRetry(): bool
    {
        return $this->status === self::STATUS_FAILED
            && $this->retry_times < 3
            && !in_array($this->error_code, ['INSUFFICIENT_BALANCE', 'INVALID_MOBILE']);
    }

    /**
     * 记录请求
     */
    public function recordRequest($data): void
    {
        $this->update([
            'request_data' => $data,
            'submitted_at' => now(),
            'status' => self::STATUS_SUBMITTING,
        ]);
    }

    /**
     * 记录响应
     */
    public function recordResponse($data, $success = true): void
    {
        $update = [
            'response_data' => $data,
            'completed_at' => now(),
        ];

        if ($success) {
            $update['status'] = self::STATUS_SUCCESS;
        } else {
            $update['status'] = self::STATUS_FAILED;
            $update['error_code'] = $data['error_code'] ?? 'UNKNOWN';
            $update['error_msg'] = $data['error_msg'] ?? '未知错误';
        }

        $this->update($update);
    }

    /**
     * 作用域：成功的记录
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * 作用域：失败的记录
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：按渠道筛选
     */
    public function scopeByProvider($query, $provider)
    {
        return $query->where('provider', $provider);
    }
}
