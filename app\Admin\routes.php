<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');
    
    // 商户管理
    $router->resource('merchants', 'MerchantController');
    
    // 产品管理
    $router->resource('products', 'ProductController');
    
    // 订单管理
    $router->resource('orders', 'OrderController');
    
    // 充值记录
    $router->resource('recharge-records', 'RechargeRecordController');

    // 充值渠道管理
    $router->resource('recharge-providers', 'RechargeProviderController');

    // 产品规则管理
    $router->resource('product-rules', 'ProductRuleController');

    // 号段管理
    $router->resource('mobile-segments', 'MobileSegmentController');
    $router->get('mobile-segments-stats', 'MobileSegmentController@stats');

    // 统计报表
    $router->get('reports/sales', 'SalesReportController@index');
    $router->get('reports/recharge', 'RechargeReportController@index');

});
