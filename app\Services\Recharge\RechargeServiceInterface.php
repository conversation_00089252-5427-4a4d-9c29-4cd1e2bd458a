<?php

namespace App\Services\Recharge;

use App\Services\Recharge\Requests\RechargeRequest;
use App\Services\Recharge\Requests\OrderQueryRequest;
use App\Services\Recharge\Responses\RechargeResponse;
use App\Services\Recharge\Responses\OrderQueryResponse;

/**
 * 充值服务接口
 * 
 * 定义充值系统的核心功能接口
 */
interface RechargeServiceInterface
{
    /**
     * 话费充值
     * 
     * @param RechargeRequest $request 充值请求对象
     * @return RechargeResponse 充值响应对象
     * @throws \Exception 当充值失败时抛出异常
     */
    public function recharge(RechargeRequest $request): RechargeResponse;

    /**
     * 订单查询
     * 
     * @param OrderQueryRequest $request 订单查询请求对象
     * @return OrderQueryResponse 订单查询响应对象
     * @throws \Exception 当查询失败时抛出异常
     */
    public function queryOrder(OrderQueryRequest $request): OrderQueryResponse;
}