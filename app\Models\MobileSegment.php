<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class MobileSegment extends Model
{
    use HasFactory;

    protected $fillable = [
        'segment',
        'carrier',
        'province',
        'city',
    ];

    // 运营商常量
    const CARRIER_MOBILE = 'mobile';   // 移动
    const CARRIER_UNICOM = 'unicom';   // 联通
    const CARRIER_TELECOM = 'telecom'; // 电信

    /**
     * 运营商文本映射
     */
    public static $carrierTexts = [
        self::CARRIER_MOBILE => '中国移动',
        self::CARRIER_UNICOM => '中国联通',
        self::CARRIER_TELECOM => '中国电信',
    ];

    /**
     * 获取运营商文本
     */
    public function getCarrierTextAttribute()
    {
        return self::$carrierTexts[$this->carrier] ?? '未知运营商';
    }

    /**
     * 根据手机号获取运营商信息
     */
    public static function getCarrierInfo($mobile): array
    {
        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
            return [
                'carrier' => 'unknown',
                'carrier_text' => '未知运营商',
                'province' => '',
                'city' => '',
                'segment' => '',
            ];
        }

        // 提取号段（前7位）
        $segment = substr($mobile, 0, 7);
        
        // 先从缓存中查找
        $cacheKey = "mobile_segment:{$segment}";
        $info = Cache::remember($cacheKey, 86400, function () use ($segment) {
            return self::where('segment', $segment)->first();
        });

        if ($info) {
            return [
                'carrier' => $info->carrier,
                'carrier_text' => $info->carrier_text,
                'province' => $info->province,
                'city' => $info->city,
                'segment' => $info->segment,
            ];
        }

        // 如果数据库中没有，使用正则表达式判断
        $carrier = self::detectCarrierByRegex($mobile);
        
        return [
            'carrier' => $carrier,
            'carrier_text' => self::$carrierTexts[$carrier] ?? '未知运营商',
            'province' => '',
            'city' => '',
            'segment' => $segment,
        ];
    }

    /**
     * 通过正则表达式检测运营商
     */
    private static function detectCarrierByRegex($mobile): string
    {
        // 中国移动号段
        if (preg_match('/^1(3[4-9]|4[7]|5[0-27-9]|7[8]|8[2-478]|9[8])/', $mobile)) {
            return self::CARRIER_MOBILE;
        }
        
        // 中国联通号段
        if (preg_match('/^1(3[0-2]|4[5]|5[56]|7[6]|8[56]|9[6])/', $mobile)) {
            return self::CARRIER_UNICOM;
        }
        
        // 中国电信号段
        if (preg_match('/^1(33|49|53|7[37]|8[019]|9[139])/', $mobile)) {
            return self::CARRIER_TELECOM;
        }
        
        return 'unknown';
    }

    /**
     * 批量导入号段数据
     */
    public static function importSegments(array $segments): int
    {
        $imported = 0;
        
        foreach ($segments as $segment) {
            try {
                self::updateOrCreate(
                    ['segment' => $segment['segment']],
                    [
                        'carrier' => $segment['carrier'],
                        'province' => $segment['province'],
                        'city' => $segment['city'],
                    ]
                );
                $imported++;
            } catch (\Exception $e) {
                \Log::error('导入号段失败', [
                    'segment' => $segment,
                    'error' => $e->getMessage(),
                ]);
            }
        }
        
        // 清除缓存
        Cache::flush();
        
        return $imported;
    }

    /**
     * 获取运营商统计
     */
    public static function getCarrierStats(): array
    {
        return Cache::remember('carrier_stats', 3600, function () {
            $stats = self::selectRaw('carrier, COUNT(*) as count')
                ->groupBy('carrier')
                ->get()
                ->keyBy('carrier');
                
            $result = [];
            foreach (self::$carrierTexts as $carrier => $text) {
                $result[$carrier] = [
                    'name' => $text,
                    'count' => $stats->get($carrier)->count ?? 0,
                ];
            }
            
            return $result;
        });
    }

    /**
     * 获取省份统计
     */
    public static function getProvinceStats(): array
    {
        return Cache::remember('province_stats', 3600, function () {
            return self::selectRaw('province, COUNT(*) as count')
                ->groupBy('province')
                ->orderBy('count', 'desc')
                ->get()
                ->toArray();
        });
    }

    /**
     * 作用域：按运营商筛选
     */
    public function scopeByCarrier($query, $carrier)
    {
        return $query->where('carrier', $carrier);
    }

    /**
     * 作用域：按省份筛选
     */
    public function scopeByProvince($query, $province)
    {
        return $query->where('province', $province);
    }

    /**
     * 作用域：按城市筛选
     */
    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }
}
