<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class AuthController extends BaseApiController
{
    /**
     * 用户注册
     */
    public function register(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'password' => ['required', 'confirmed', Password::min(8)],
                'mobile' => 'nullable|regex:/^1[3-9]\d{9}$/|unique:users,mobile',
            ], [
                'name.required' => '姓名不能为空',
                'email.required' => '邮箱不能为空',
                'email.email' => '邮箱格式不正确',
                'email.unique' => '邮箱已被注册',
                'password.required' => '密码不能为空',
                'password.confirmed' => '两次密码输入不一致',
                'password.min' => '密码至少8位',
                'mobile.regex' => '手机号格式不正确',
                'mobile.unique' => '手机号已被注册',
            ]);

            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'mobile' => $validated['mobile'] ?? null,
                'status' => 1,
            ]);

            $token = $user->createToken('auth_token')->plainTextToken;

            return $this->success([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'mobile' => $user->mobile,
                    'avatar' => $user->avatar,
                    'status' => $user->status,
                    'created_at' => $user->created_at->toDateTimeString(),
                ],
                'token' => $token,
                'token_type' => 'Bearer',
            ], '注册成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('注册失败');
        }
    }

    /**
     * 用户登录
     */
    public function login(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'email' => 'required|email',
                'password' => 'required',
            ], [
                'email.required' => '邮箱不能为空',
                'email.email' => '邮箱格式不正确',
                'password.required' => '密码不能为空',
            ]);

            if (!Auth::attempt($validated)) {
                return $this->error('邮箱或密码错误', 'INVALID_CREDENTIALS', 401);
            }

            $user = Auth::user();

            if (!$user->isActive()) {
                return $this->error('账户已被禁用', 'ACCOUNT_DISABLED', 403);
            }

            // 删除旧的token
            $user->tokens()->delete();

            $token = $user->createToken('auth_token')->plainTextToken;

            return $this->success([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'mobile' => $user->mobile,
                    'avatar' => $user->avatar,
                    'status' => $user->status,
                    'created_at' => $user->created_at->toDateTimeString(),
                ],
                'token' => $token,
                'token_type' => 'Bearer',
            ], '登录成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('登录失败');
        }
    }

    /**
     * 用户登出
     */
    public function logout(Request $request)
    {
        try {
            $request->user()->currentAccessToken()->delete();

            return $this->success(null, '登出成功');

        } catch (\Exception $e) {
            return $this->serverError('登出失败');
        }
    }

    /**
     * 刷新token
     */
    public function refresh(Request $request)
    {
        try {
            $user = $request->user();

            // 删除当前token
            $request->user()->currentAccessToken()->delete();

            // 创建新token
            $token = $user->createToken('auth_token')->plainTextToken;

            return $this->success([
                'token' => $token,
                'token_type' => 'Bearer',
            ], 'Token刷新成功');

        } catch (\Exception $e) {
            return $this->serverError('Token刷新失败');
        }
    }

    /**
     * 获取当前用户信息
     */
    public function me(Request $request)
    {
        try {
            $user = $request->user();

            return $this->success([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'mobile' => $user->mobile,
                'avatar' => $user->avatar,
                'status' => $user->status,
                'created_at' => $user->created_at->toDateTimeString(),
            ]);

        } catch (\Exception $e) {
            return $this->serverError('获取用户信息失败');
        }
    }

    /**
     * 手机号登录
     */
    public function loginByMobile(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'mobile' => 'required|regex:/^1[3-9]\d{9}$/',
                'code' => 'required|string|size:6',
            ], [
                'mobile.required' => '手机号不能为空',
                'mobile.regex' => '手机号格式不正确',
                'code.required' => '验证码不能为空',
                'code.size' => '验证码必须是6位',
            ]);

            // 这里应该验证短信验证码
            // 目前简化处理，直接通过手机号查找用户
            $user = User::where('mobile', $validated['mobile'])->first();

            if (!$user) {
                return $this->error('手机号未注册', 'MOBILE_NOT_REGISTERED', 404);
            }

            if (!$user->isActive()) {
                return $this->error('账户已被禁用', 'ACCOUNT_DISABLED', 403);
            }

            // 删除旧的token
            $user->tokens()->delete();

            $token = $user->createToken('auth_token')->plainTextToken;

            return $this->success([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'mobile' => $user->mobile,
                    'avatar' => $user->avatar,
                    'status' => $user->status,
                    'created_at' => $user->created_at->toDateTimeString(),
                ],
                'token' => $token,
                'token_type' => 'Bearer',
            ], '登录成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('登录失败');
        }
    }

    /**
     * 发送短信验证码
     */
    public function sendSmsCode(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'mobile' => 'required|regex:/^1[3-9]\d{9}$/',
                'type' => 'required|in:login,register,reset',
            ], [
                'mobile.required' => '手机号不能为空',
                'mobile.regex' => '手机号格式不正确',
                'type.required' => '验证码类型不能为空',
                'type.in' => '验证码类型不正确',
            ]);

            // 这里应该调用短信服务发送验证码
            // 目前只是返回成功消息
            
            return $this->success(null, '验证码发送成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('发送验证码失败');
        }
    }
}
