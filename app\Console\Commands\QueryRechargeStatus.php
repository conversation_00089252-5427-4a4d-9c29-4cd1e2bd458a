<?php

namespace App\Console\Commands;

use App\Models\RechargeRecord;
use App\Services\RechargeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class QueryRechargeStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recharge:query-status 
                            {--limit=50 : 每次处理的记录数量}
                            {--max-age=24 : 最大查询时间（小时）}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '查询充值状态，更新未完成的充值记录';

    protected $rechargeService;

    /**
     * Create a new command instance.
     */
    public function __construct(RechargeService $rechargeService)
    {
        parent::__construct();
        $this->rechargeService = $rechargeService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $limit = $this->option('limit');
        $maxAge = $this->option('max-age');

        $this->info("开始查询充值状态，限制：{$limit} 条，最大时间：{$maxAge} 小时");

        try {
            // 查询需要更新状态的充值记录
            $records = RechargeRecord::whereIn('status', [
                    RechargeRecord::STATUS_PENDING,
                    RechargeRecord::STATUS_SUBMITTING,
                    RechargeRecord::STATUS_SUBMITTED,
                    RechargeRecord::STATUS_PROCESSING,
                ])
                ->where('created_at', '>=', now()->subHours($maxAge))
                ->orderBy('created_at', 'asc')
                ->limit($limit)
                ->get();

            if ($records->isEmpty()) {
                $this->info('没有需要查询的充值记录');
                return 0;
            }

            $this->info("找到 {$records->count()} 条需要查询的记录");

            $successCount = 0;
            $failedCount = 0;
            $unchangedCount = 0;

            foreach ($records as $record) {
                $this->line("查询记录 ID: {$record->id}, 订单号: {$record->provider_order_no}");

                try {
                    $result = $this->rechargeService->queryRechargeStatus($record);

                    if ($result['success']) {
                        // 检查是否需要更新状态
                        if (isset($result['order_success']) && $result['order_success']) {
                            // 充值成功
                            $record->update([
                                'status' => RechargeRecord::STATUS_SUCCESS,
                                'completed_at' => now(),
                            ]);

                            // 更新订单状态
                            $this->rechargeService->updateOrderStatus(
                                $record->order, 
                                \App\Models\Order::STATUS_SUCCESS, 
                                'system', 
                                '定时查询确认充值成功'
                            );

                            $successCount++;
                            $this->info("  ✓ 充值成功");
                        } elseif (isset($result['status']) && in_array($result['status'], ['失败', '充值失败'])) {
                            // 充值失败
                            $record->update([
                                'status' => RechargeRecord::STATUS_FAILED,
                                'error_msg' => $result['status'],
                                'completed_at' => now(),
                            ]);

                            // 更新订单状态
                            $this->rechargeService->updateOrderStatus(
                                $record->order, 
                                \App\Models\Order::STATUS_FAILED, 
                                'system', 
                                '定时查询确认充值失败'
                            );

                            $failedCount++;
                            $this->warn("  ✗ 充值失败: {$result['status']}");
                        } else {
                            // 状态未变化，仍在处理中
                            $unchangedCount++;
                            $this->line("  - 仍在处理中: {$result['status']}");
                        }
                    } else {
                        $this->error("  ✗ 查询失败: {$result['message']}");
                        $failedCount++;
                    }

                    // 避免请求过于频繁
                    usleep(500000); // 0.5秒延迟

                } catch (\Exception $e) {
                    $this->error("  ✗ 查询异常: {$e->getMessage()}");
                    $failedCount++;

                    Log::error('定时查询充值状态异常', [
                        'record_id' => $record->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $this->info("\n查询完成:");
            $this->info("成功: {$successCount} 条");
            $this->warn("失败: {$failedCount} 条");
            $this->line("未变化: {$unchangedCount} 条");

            Log::info('定时查询充值状态完成', [
                'total' => $records->count(),
                'success' => $successCount,
                'failed' => $failedCount,
                'unchanged' => $unchangedCount,
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->error("查询充值状态失败: {$e->getMessage()}");
            
            Log::error('定时查询充值状态失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }
}
