<?php

namespace App\Transformers;

use App\Models\ProductVoucher;
use League\Fractal\TransformerAbstract;

class ProductVoucherTransformer extends TransformerAbstract
{
    /**
     * 转换产品券配置数据
     */
    public function transform(ProductVoucher $voucher): array
    {
        return [
            'id' => $voucher->id,
            'voucher_value' => (float) $voucher->voucher_value,
            'voucher_quantity' => $voucher->voucher_quantity,
            'total_value' => (float) ($voucher->voucher_value * $voucher->voucher_quantity),
        ];
    }
}
