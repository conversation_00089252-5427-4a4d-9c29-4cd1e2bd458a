<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 优化 orders 表索引
        Schema::table('orders', function (Blueprint $table) {
            // 添加复合索引用于常见查询
            $table->index(['merchant_id', 'status', 'created_at'], 'idx_merchant_status_created');
            $table->index(['user_id', 'status', 'created_at'], 'idx_user_status_created');
            $table->index(['mobile', 'created_at'], 'idx_mobile_created');
            $table->index(['order_type', 'status'], 'idx_type_status');
            $table->index(['status', 'paid_at'], 'idx_status_paid');
            $table->index(['status', 'completed_at'], 'idx_status_completed');
            $table->index(['notify_status', 'created_at'], 'idx_notify_status_created');
        });

        // 优化 user_vouchers 表索引
        Schema::table('user_vouchers', function (Blueprint $table) {
            // 添加复合索引用于券查询
            $table->index(['user_id', 'status', 'expired_at'], 'idx_user_status_expired');
            $table->index(['user_id', 'status', 'voucher_value'], 'idx_user_status_value');
            $table->index(['product_id', 'status'], 'idx_product_status');
            $table->index(['order_id', 'status'], 'idx_order_status');
            $table->index(['used_order_id', 'used_at'], 'idx_used_order_time');
            $table->index(['status', 'created_at'], 'idx_status_created');
        });

        // 优化 merchants 表索引
        Schema::table('merchants', function (Blueprint $table) {
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['api_key'], 'idx_api_key');
            $table->index(['balance'], 'idx_balance');
        });

        // 优化 products 表索引
        Schema::table('products', function (Blueprint $table) {
            $table->index(['status', 'sort'], 'idx_status_sort');
            $table->index(['category', 'status'], 'idx_category_status');
            $table->index(['price', 'status'], 'idx_price_status');
            $table->index(['sales_count'], 'idx_sales_count');
        });

        // 优化 recharge_records 表索引
        Schema::table('recharge_records', function (Blueprint $table) {
            $table->index(['order_id', 'status'], 'idx_order_status');
            $table->index(['mobile', 'created_at'], 'idx_mobile_created');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['provider_order_no'], 'idx_provider_order');
            $table->index(['amount', 'status'], 'idx_amount_status');
        });

        // 优化 order_logs 表索引
        Schema::table('order_logs', function (Blueprint $table) {
            $table->index(['order_id', 'created_at'], 'idx_order_created');
            $table->index(['operator', 'created_at'], 'idx_operator_created');
            $table->index(['action', 'created_at'], 'idx_action_created');
        });

        // 优化 order_payments 表索引
        Schema::table('order_payments', function (Blueprint $table) {
            $table->index(['order_id', 'status'], 'idx_order_status');
            $table->index(['payment_method', 'status'], 'idx_method_status');
            $table->index(['status', 'paid_at'], 'idx_status_paid');
            $table->index(['payment_no'], 'idx_payment_no');
        });

        // 优化 merchant_balance_logs 表索引
        Schema::table('merchant_balance_logs', function (Blueprint $table) {
            $table->index(['merchant_id', 'created_at'], 'idx_merchant_created');
            $table->index(['type', 'created_at'], 'idx_type_created');
            $table->index(['order_id'], 'idx_order_id');
        });

        // 优化 users 表索引
        Schema::table('users', function (Blueprint $table) {
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['mobile'], 'idx_mobile');
        });

        // 优化 notification_logs 表索引
        Schema::table('notification_logs', function (Blueprint $table) {
            $table->index(['order_id', 'created_at'], 'idx_order_created');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['type', 'status'], 'idx_type_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 删除 orders 表索引
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_merchant_status_created');
            $table->dropIndex('idx_user_status_created');
            $table->dropIndex('idx_mobile_created');
            $table->dropIndex('idx_type_status');
            $table->dropIndex('idx_status_paid');
            $table->dropIndex('idx_status_completed');
            $table->dropIndex('idx_notify_status_created');
        });

        // 删除 user_vouchers 表索引
        Schema::table('user_vouchers', function (Blueprint $table) {
            $table->dropIndex('idx_user_status_expired');
            $table->dropIndex('idx_user_status_value');
            $table->dropIndex('idx_product_status');
            $table->dropIndex('idx_order_status');
            $table->dropIndex('idx_used_order_time');
            $table->dropIndex('idx_status_created');
        });

        // 删除 merchants 表索引
        Schema::table('merchants', function (Blueprint $table) {
            $table->dropIndex('idx_status_created');
            $table->dropIndex('idx_api_key');
            $table->dropIndex('idx_balance');
        });

        // 删除 products 表索引
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_status_sort');
            $table->dropIndex('idx_category_status');
            $table->dropIndex('idx_price_status');
            $table->dropIndex('idx_sales_count');
        });

        // 删除 recharge_records 表索引
        Schema::table('recharge_records', function (Blueprint $table) {
            $table->dropIndex('idx_order_status');
            $table->dropIndex('idx_mobile_created');
            $table->dropIndex('idx_status_created');
            $table->dropIndex('idx_provider_order');
            $table->dropIndex('idx_amount_status');
        });

        // 删除 order_logs 表索引
        Schema::table('order_logs', function (Blueprint $table) {
            $table->dropIndex('idx_order_created');
            $table->dropIndex('idx_operator_created');
            $table->dropIndex('idx_action_created');
        });

        // 删除 order_payments 表索引
        Schema::table('order_payments', function (Blueprint $table) {
            $table->dropIndex('idx_order_status');
            $table->dropIndex('idx_method_status');
            $table->dropIndex('idx_status_paid');
            $table->dropIndex('idx_payment_no');
        });

        // 删除 merchant_balance_logs 表索引
        Schema::table('merchant_balance_logs', function (Blueprint $table) {
            $table->dropIndex('idx_merchant_created');
            $table->dropIndex('idx_type_created');
            $table->dropIndex('idx_order_id');
        });

        // 删除 users 表索引
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_status_created');
            $table->dropIndex('idx_mobile');
        });

        // 删除 notification_logs 表索引
        Schema::table('notification_logs', function (Blueprint $table) {
            $table->dropIndex('idx_order_created');
            $table->dropIndex('idx_status_created');
            $table->dropIndex('idx_type_status');
        });
    }
};
