<?php

namespace App\Services;

use App\Models\SystemConfig;
use Illuminate\Support\Facades\Cache;

class ConfigService
{
    /**
     * 获取配置值
     */
    public function get(string $key, $default = null, string $group = null)
    {
        return SystemConfig::get($key, $default, $group);
    }

    /**
     * 设置配置值
     */
    public function set(string $key, $value, string $group = null): void
    {
        SystemConfig::set($key, $value, $group);
    }

    /**
     * 获取分组配置
     */
    public function getGroup(string $group): array
    {
        return SystemConfig::getGroup($group);
    }

    /**
     * 获取公开配置
     */
    public function getPublicConfigs(): array
    {
        return SystemConfig::getPublicConfigs();
    }

    /**
     * 批量设置配置
     */
    public function setBatch(array $configs, string $group = null): void
    {
        SystemConfig::setBatch($configs, $group);
    }

    /**
     * 清除配置缓存
     */
    public function clearCache(): void
    {
        SystemConfig::clearCache();
    }

    /**
     * 获取系统配置
     */
    public function getSystemConfigs(): array
    {
        return $this->getGroup(SystemConfig::GROUP_SYSTEM);
    }

    /**
     * 获取支付配置
     */
    public function getPaymentConfigs(): array
    {
        return $this->getGroup(SystemConfig::GROUP_PAYMENT);
    }

    /**
     * 获取充值配置
     */
    public function getRechargeConfigs(): array
    {
        return $this->getGroup(SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 获取通知配置
     */
    public function getNotificationConfigs(): array
    {
        return $this->getGroup(SystemConfig::GROUP_NOTIFICATION);
    }

    /**
     * 检查系统是否处于维护模式
     */
    public function isMaintenanceMode(): bool
    {
        return $this->get('maintenance_mode', false, SystemConfig::GROUP_SYSTEM);
    }

    /**
     * 获取最大订单金额
     */
    public function getMaxOrderAmount(): float
    {
        return $this->get('max_order_amount', 1000, SystemConfig::GROUP_SYSTEM);
    }

    /**
     * 获取支付超时时间
     */
    public function getPaymentTimeout(): int
    {
        return $this->get('payment_timeout', 1800, SystemConfig::GROUP_PAYMENT);
    }

    /**
     * 获取充值超时时间
     */
    public function getRechargeTimeout(): int
    {
        return $this->get('recharge_timeout', 60, SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 获取最大重试次数
     */
    public function getMaxRetryTimes(): int
    {
        return $this->get('max_retry_times', 3, SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 是否启用自动重试
     */
    public function isAutoRetryEnabled(): bool
    {
        return $this->get('auto_retry_enabled', true, SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 获取支持的充值金额
     */
    public function getSupportedAmounts(): array
    {
        return $this->get('supported_amounts', [5, 10, 20, 30, 50, 100, 200, 300, 500], SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 检查金额是否支持
     */
    public function isAmountSupported(float $amount): bool
    {
        return in_array($amount, $this->getSupportedAmounts());
    }

    /**
     * 获取最小充值金额
     */
    public function getMinRechargeAmount(): float
    {
        return $this->get('min_recharge_amount', 5, SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 获取最大充值金额
     */
    public function getMaxRechargeAmount(): float
    {
        return $this->get('max_recharge_amount', 500, SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 获取充值手续费率
     */
    public function getRechargeFeeRate(): float
    {
        return $this->get('recharge_fee_rate', 0, SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 是否允许游客充值
     */
    public function isGuestRechargeAllowed(): bool
    {
        return $this->get('guest_recharge_allowed', true, SystemConfig::GROUP_RECHARGE);
    }

    /**
     * 是否启用支付宝
     */
    public function isAlipayEnabled(): bool
    {
        return $this->get('alipay_enabled', true, SystemConfig::GROUP_PAYMENT);
    }

    /**
     * 是否启用微信支付
     */
    public function isWechatEnabled(): bool
    {
        return $this->get('wechat_enabled', true, SystemConfig::GROUP_PAYMENT);
    }

    /**
     * 是否启用订单通知
     */
    public function isOrderNotifyEnabled(): bool
    {
        return $this->get('order_notify_enabled', true, SystemConfig::GROUP_NOTIFICATION);
    }

    /**
     * 获取通知最大重试次数
     */
    public function getNotifyMaxRetry(): int
    {
        return $this->get('notify_max_retry', 5, SystemConfig::GROUP_NOTIFICATION);
    }

    /**
     * 获取通知重试间隔
     */
    public function getNotifyRetryInterval(): int
    {
        return $this->get('notify_retry_interval', 300, SystemConfig::GROUP_NOTIFICATION);
    }

    /**
     * 验证配置值
     */
    public function validateConfig(string $key, $value, string $group = null): bool
    {
        $config = SystemConfig::where('key', $key);
        
        if ($group) {
            $config->where('group', $group);
        }
        
        $config = $config->first();
        
        if (!$config) {
            return false;
        }
        
        return $config->validateValue($value);
    }

    /**
     * 获取配置选项
     */
    public function getConfigOptions(string $key, string $group = null): array
    {
        $config = SystemConfig::where('key', $key);
        
        if ($group) {
            $config->where('group', $group);
        }
        
        $config = $config->first();
        
        return $config ? ($config->options ?? []) : [];
    }

    /**
     * 重置配置为默认值
     */
    public function resetToDefault(string $key, string $group = null): void
    {
        // 这里可以定义默认配置值
        $defaults = [
            'system' => [
                'site_name' => '话费充值系统',
                'maintenance_mode' => false,
                'max_order_amount' => 1000,
            ],
            'payment' => [
                'alipay_enabled' => true,
                'wechat_enabled' => true,
                'payment_timeout' => 1800,
            ],
            'recharge' => [
                'recharge_timeout' => 60,
                'max_retry_times' => 3,
                'auto_retry_enabled' => true,
            ],
        ];
        
        $defaultValue = $defaults[$group][$key] ?? null;
        
        if ($defaultValue !== null) {
            $this->set($key, $defaultValue, $group);
        }
    }

    /**
     * 导出配置
     */
    public function exportConfigs(string $group = null): array
    {
        $query = SystemConfig::query();
        
        if ($group) {
            $query->where('group', $group);
        }
        
        return $query->get()->map(function ($config) {
            return [
                'group' => $config->group,
                'key' => $config->key,
                'value' => $config->value,
                'type' => $config->type,
                'name' => $config->name,
                'description' => $config->description,
            ];
        })->toArray();
    }

    /**
     * 导入配置
     */
    public function importConfigs(array $configs): int
    {
        $imported = 0;
        
        foreach ($configs as $config) {
            try {
                SystemConfig::updateOrCreate(
                    [
                        'group' => $config['group'],
                        'key' => $config['key'],
                    ],
                    [
                        'value' => $config['value'],
                        'type' => $config['type'],
                        'name' => $config['name'],
                        'description' => $config['description'] ?? '',
                    ]
                );
                $imported++;
            } catch (\Exception $e) {
                \Log::error('导入配置失败', [
                    'config' => $config,
                    'error' => $e->getMessage(),
                ]);
            }
        }
        
        $this->clearCache();
        
        return $imported;
    }
}
