# 订单管理模块开发文档

## 模块概述

订单管理模块负责处理用户购买产品和充值的完整生命周期，包括订单创建、支付、状态流转、通知等核心功能。该模块需要处理高并发场景，确保数据一致性和可靠性。

## 数据库设计

### 1. 订单表（orders）

```sql
CREATE TABLE `orders` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `system_order_no` varchar(32) NOT NULL COMMENT '系统订单号',
  `merchant_id` bigint(20) UNSIGNED NOT NULL COMMENT '商户ID',
  `merchant_order_no` varchar(64) NOT NULL COMMENT '商户订单号',
  `user_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '用户ID',
  `product_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '产品ID',
  `order_type` tinyint(1) NOT NULL COMMENT '订单类型：1-产品购买，2-话费充值',
  `mobile` varchar(11) NOT NULL COMMENT '手机号',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `cost` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '成本金额',
  `status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '订单状态',
  `notify_url` varchar(255) DEFAULT NULL COMMENT '回调地址',
  `notify_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '通知状态：0-未通知，1-已通知',
  `notify_times` int(11) NOT NULL DEFAULT 0 COMMENT '通知次数',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_order_no` (`system_order_no`),
  UNIQUE KEY `uk_merchant_order` (`merchant_id`, `merchant_order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

### 2. 订单日志表（order_logs）

```sql
CREATE TABLE `order_logs` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `operator_type` varchar(20) NOT NULL COMMENT '操作者类型：system,admin,user',
  `operator_id` bigint(20) UNSIGNED DEFAULT NULL COMMENT '操作者ID',
  `action` varchar(50) NOT NULL COMMENT '操作动作',
  `from_status` tinyint(2) DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(2) DEFAULT NULL COMMENT '新状态',
  `content` text COMMENT '日志内容',
  `extra_data` json DEFAULT NULL COMMENT '额外数据',
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单日志表';
```

### 3. 订单支付表（order_payments）

```sql
CREATE TABLE `order_payments` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `payment_no` varchar(64) NOT NULL COMMENT '支付流水号',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式：balance,alipay,wechat',
  `payment_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `voucher_deduct` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '券抵扣金额',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付状态：0-待支付，1-已支付，2-已退款',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `refunded_at` timestamp NULL DEFAULT NULL COMMENT '退款时间',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单支付表';
```

## 模型设计

### 1. Order 模型

```php
// app/Models/Order.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $fillable = [
        'system_order_no', 'merchant_id', 'merchant_order_no',
        'user_id', 'product_id', 'order_type', 'mobile',
        'amount', 'cost', 'status', 'notify_url',
        'notify_status', 'notify_times', 'remark',
        'paid_at', 'completed_at'
    ];
    
    protected $casts = [
        'amount' => 'decimal:2',
        'cost' => 'decimal:2',
        'paid_at' => 'datetime',
        'completed_at' => 'datetime',
    ];
    
    // 订单类型
    const TYPE_PRODUCT = 1;  // 产品购买
    const TYPE_RECHARGE = 2; // 话费充值
    
    // 订单状态
    const STATUS_PENDING = 0;        // 待支付
    const STATUS_PAID = 1;           // 已支付
    const STATUS_PROCESSING = 2;     // 处理中
    const STATUS_RECHARGING = 6;     // 充值中
    const STATUS_SUCCESS = 16;       // 成功
    const STATUS_FAILED = 26;        // 失败
    const STATUS_CANCELLED = 30;     // 已取消
    const STATUS_REFUNDED = 40;      // 已退款
    
    // 通知状态
    const NOTIFY_PENDING = 0;
    const NOTIFY_SUCCESS = 1;
    
    /**
     * 状态文本映射
     */
    public static $statusTexts = [
        self::STATUS_PENDING => '待支付',
        self::STATUS_PAID => '已支付',
        self::STATUS_PROCESSING => '处理中',
        self::STATUS_RECHARGING => '充值中',
        self::STATUS_SUCCESS => '成功',
        self::STATUS_FAILED => '失败',
        self::STATUS_CANCELLED => '已取消',
        self::STATUS_REFUNDED => '已退款',
    ];
    
    /**
     * 关联商户
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    /**
     * 订单日志
     */
    public function logs()
    {
        return $this->hasMany(OrderLog::class);
    }
    
    /**
     * 支付记录
     */
    public function payments()
    {
        return $this->hasMany(OrderPayment::class);
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::$statusTexts[$this->status] ?? '未知';
    }
    
    /**
     * 是否可以取消
     */
    public function canCancel(): bool
    {
        return in_array($this->status, [
            self::STATUS_PENDING,
            self::STATUS_PAID,
            self::STATUS_PROCESSING
        ]);
    }
    
    /**
     * 是否可以退款
     */
    public function canRefund(): bool
    {
        return in_array($this->status, [
            self::STATUS_PAID,
            self::STATUS_FAILED
        ]) && $this->paid_at;
    }
    
    /**
     * 记录日志
     */
    public function log($action, $content = null, $extra = null)
    {
        return $this->logs()->create([
            'operator_type' => 'system',
            'operator_id' => auth()->id(),
            'action' => $action,
            'from_status' => $this->getOriginal('status'),
            'to_status' => $this->status,
            'content' => $content,
            'extra_data' => $extra,
        ]);
    }
}
```

### 2. OrderLog 模型

```php
// app/Models/OrderLog.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderLog extends Model
{
    protected $fillable = [
        'order_id', 'operator_type', 'operator_id',
        'action', 'from_status', 'to_status',
        'content', 'extra_data'
    ];
    
    protected $casts = [
        'extra_data' => 'json',
    ];
    
    public $timestamps = false;
    
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($model) {
            $model->created_at = now();
        });
    }
    
    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
```

## 服务层实现

### 1. 订单服务

```php
// app/Services/OrderService.php
namespace App\Services;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Events\OrderCreated;
use App\Events\OrderPaid;
use App\Exceptions\BusinessException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OrderService
{
    /**
     * 创建产品购买订单
     */
    public function createProductOrder(User $user, Product $product, array $data): Order
    {
        // 验证产品可购买性
        app(ProductValidationService::class)->validatePurchase($product, $user->id);
        
        DB::beginTransaction();
        try {
            $order = new Order();
            $order->system_order_no = $this->generateOrderNo();
            $order->merchant_id = $data['merchant_id'] ?? 0;
            $order->merchant_order_no = $data['merchant_order_no'] ?? $order->system_order_no;
            $order->user_id = $user->id;
            $order->product_id = $product->id;
            $order->order_type = Order::TYPE_PRODUCT;
            $order->mobile = $data['mobile'] ?? $user->mobile;
            $order->amount = $product->price;
            $order->cost = $product->cost;
            $order->status = Order::STATUS_PENDING;
            $order->notify_url = $data['notify_url'] ?? null;
            $order->save();
            
            // 记录日志
            $order->log('create', '创建产品购买订单');
            
            DB::commit();
            
            // 触发事件
            event(new OrderCreated($order));
            
            return $order;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 创建充值订单
     */
    public function createRechargeOrder(array $data): Order
    {
        DB::beginTransaction();
        try {
            $order = new Order();
            $order->system_order_no = $this->generateOrderNo();
            $order->merchant_id = $data['merchant_id'];
            $order->merchant_order_no = $data['merchant_order_no'];
            $order->user_id = $data['user_id'] ?? null;
            $order->order_type = Order::TYPE_RECHARGE;
            $order->mobile = $data['mobile'];
            $order->amount = $data['amount'];
            $order->cost = $this->calculateRechargeCost($data['amount']);
            $order->status = Order::STATUS_PENDING;
            $order->notify_url = $data['notify_url'] ?? null;
            $order->save();
            
            // 记录日志
            $order->log('create', '创建充值订单');
            
            DB::commit();
            
            return $order;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 支付订单
     */
    public function payOrder(Order $order, array $paymentData): bool
    {
        if ($order->status !== Order::STATUS_PENDING) {
            throw new BusinessException('订单状态不允许支付');
        }
        
        DB::beginTransaction();
        try {
            // 创建支付记录
            $payment = $order->payments()->create([
                'payment_no' => $paymentData['payment_no'] ?? $this->generatePaymentNo(),
                'payment_method' => $paymentData['payment_method'],
                'payment_amount' => $paymentData['payment_amount'],
                'voucher_deduct' => $paymentData['voucher_deduct'] ?? 0,
                'status' => 1,
                'paid_at' => now(),
            ]);
            
            // 更新订单状态
            $order->status = Order::STATUS_PAID;
            $order->paid_at = now();
            $order->save();
            
            // 记录日志
            $order->log('pay', '订单支付成功', [
                'payment_method' => $paymentData['payment_method'],
                'payment_amount' => $paymentData['payment_amount'],
            ]);
            
            DB::commit();
            
            // 触发支付成功事件
            event(new OrderPaid($order));
            
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 更新订单状态
     */
    public function updateStatus(Order $order, int $status, string $remark = null): bool
    {
        $oldStatus = $order->status;
        
        // 验证状态流转合法性
        if (!$this->isValidStatusTransition($oldStatus, $status)) {
            throw new BusinessException('无效的状态流转');
        }
        
        $order->status = $status;
        
        // 特殊状态处理
        if ($status === Order::STATUS_SUCCESS) {
            $order->completed_at = now();
        }
        
        $order->save();
        
        // 记录日志
        $order->log('status_change', $remark ?? '订单状态变更', [
            'from_status' => $oldStatus,
            'to_status' => $status,
        ]);
        
        return true;
    }
    
    /**
     * 取消订单
     */
    public function cancelOrder(Order $order, string $reason = null): bool
    {
        if (!$order->canCancel()) {
            throw new BusinessException('当前订单状态不允许取消');
        }
        
        DB::beginTransaction();
        try {
            // 如果已支付，需要退款
            if ($order->paid_at) {
                $this->refundOrder($order, $reason);
            } else {
                $this->updateStatus($order, Order::STATUS_CANCELLED, $reason);
            }
            
            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 退款订单
     */
    public function refundOrder(Order $order, string $reason = null): bool
    {
        if (!$order->canRefund()) {
            throw new BusinessException('当前订单状态不允许退款');
        }
        
        DB::beginTransaction();
        try {
            // 更新支付记录
            $payment = $order->payments()->where('status', 1)->first();
            if ($payment) {
                $payment->status = 2;
                $payment->refunded_at = now();
                $payment->save();
            }
            
            // 退还余额或原路退回
            $this->processRefund($order, $payment);
            
            // 更新订单状态
            $this->updateStatus($order, Order::STATUS_REFUNDED, $reason);
            
            DB::commit();
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 生成订单号
     */
    private function generateOrderNo(): string
    {
        return date('YmdHis') . rand(100000, 999999);
    }
    
    /**
     * 生成支付流水号
     */
    private function generatePaymentNo(): string
    {
        return 'PAY' . date('YmdHis') . rand(100000, 999999);
    }
    
    /**
     * 计算充值成本
     */
    private function calculateRechargeCost($amount): float
    {
        // 根据充值金额计算成本，这里简化为固定折扣
        return $amount * 0.95;
    }
    
    /**
     * 验证状态流转合法性
     */
    private function isValidStatusTransition($fromStatus, $toStatus): bool
    {
        $validTransitions = [
            Order::STATUS_PENDING => [Order::STATUS_PAID, Order::STATUS_CANCELLED],
            Order::STATUS_PAID => [Order::STATUS_PROCESSING, Order::STATUS_CANCELLED, Order::STATUS_REFUNDED],
            Order::STATUS_PROCESSING => [Order::STATUS_RECHARGING, Order::STATUS_SUCCESS, Order::STATUS_FAILED],
            Order::STATUS_RECHARGING => [Order::STATUS_SUCCESS, Order::STATUS_FAILED],
            Order::STATUS_FAILED => [Order::STATUS_REFUNDED],
        ];
        
        return isset($validTransitions[$fromStatus]) && 
               in_array($toStatus, $validTransitions[$fromStatus]);
    }
    
    /**
     * 处理退款
     */
    private function processRefund(Order $order, $payment = null): void
    {
        if (!$payment) return;
        
        switch ($payment->payment_method) {
            case 'balance':
                // 退还到余额
                if ($order->user) {
                    $order->user->increment('balance', $payment->payment_amount);
                }
                break;
                
            case 'alipay':
            case 'wechat':
                // 调用第三方退款接口
                // TODO: 实现第三方退款
                break;
        }
    }
}
```

### 2. 订单查询服务

```php
// app/Services/OrderQueryService.php
namespace App\Services;

use App\Models\Order;
use Illuminate\Database\Eloquent\Builder;

class OrderQueryService
{
    /**
     * 构建查询
     */
    public function query(): Builder
    {
        return Order::query();
    }
    
    /**
     * 按商户查询
     */
    public function byMerchant($merchantId): Builder
    {
        return $this->query()->where('merchant_id', $merchantId);
    }
    
    /**
     * 按用户查询
     */
    public function byUser($userId): Builder
    {
        return $this->query()->where('user_id', $userId);
    }
    
    /**
     * 按状态查询
     */
    public function byStatus($status): Builder
    {
        if (is_array($status)) {
            return $this->query()->whereIn('status', $status);
        }
        return $this->query()->where('status', $status);
    }
    
    /**
     * 按时间范围查询
     */
    public function byDateRange($startDate, $endDate, $field = 'created_at'): Builder
    {
        return $this->query()
            ->whereBetween($field, [$startDate, $endDate]);
    }
    
    /**
     * 待处理订单
     */
    public function pending(): Builder
    {
        return $this->query()->whereIn('status', [
            Order::STATUS_PAID,
            Order::STATUS_PROCESSING,
            Order::STATUS_RECHARGING
        ]);
    }
    
    /**
     * 需要通知的订单
     */
    public function needNotify(): Builder
    {
        return $this->query()
            ->where('notify_status', Order::NOTIFY_PENDING)
            ->whereNotNull('notify_url')
            ->where('notify_times', '<', 5)
            ->whereIn('status', [
                Order::STATUS_SUCCESS,
                Order::STATUS_FAILED
            ]);
    }
    
    /**
     * 超时未支付订单
     */
    public function paymentTimeout($minutes = 30): Builder
    {
        return $this->query()
            ->where('status', Order::STATUS_PENDING)
            ->where('created_at', '<', now()->subMinutes($minutes));
    }
}
```

## 后台管理实现

### 1. 订单管理控制器

```php
// app/Admin/Controllers/OrderController.php
namespace App\Admin\Controllers;

use App\Admin\Repositories\Order;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class OrderController extends AdminController
{
    protected function grid()
    {
        return Grid::make(new Order(['merchant', 'user', 'product']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            
            $grid->column('id')->sortable();
            $grid->column('system_order_no', '系统订单号');
            $grid->column('merchant.name', '商户');
            $grid->column('merchant_order_no', '商户订单号');
            $grid->column('order_type', '类型')->using([
                1 => '产品购买',
                2 => '话费充值',
            ])->label([
                1 => 'primary',
                2 => 'success',
            ]);
            $grid->column('mobile', '手机号');
            $grid->column('amount', '金额')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('status', '状态')->using(Order::$statusTexts)
                ->dot([
                    0 => 'warning',
                    1 => 'info',
                    2 => 'primary',
                    6 => 'primary',
                    16 => 'success',
                    26 => 'danger',
                    30 => 'default',
                    40 => 'warning',
                ]);
            $grid->column('created_at', '创建时间');
            
            // 禁用创建按钮
            $grid->disableCreateButton();
            
            // 行操作
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableDelete();
                
                // 查看详情
                $actions->append(new ViewOrderAction());
                
                // 根据状态显示不同操作
                if ($actions->row->canCancel()) {
                    $actions->append(new CancelOrderAction());
                }
                if ($actions->row->canRefund()) {
                    $actions->append(new RefundOrderAction());
                }
            });
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('system_order_no', '系统订单号');
                $filter->equal('merchant_order_no', '商户订单号');
                $filter->equal('mobile', '手机号');
                $filter->equal('order_type', '类型')->select([
                    1 => '产品购买',
                    2 => '话费充值',
                ]);
                $filter->equal('status', '状态')->select(Order::$statusTexts);
                $filter->between('created_at', '创建时间')->datetime();
            });
            
            // 快速搜索
            $grid->quickSearch(['system_order_no', 'merchant_order_no', 'mobile']);
        });
    }
    
    protected function detail($id)
    {
        return Show::make($id, new Order(['merchant', 'user', 'product', 'logs', 'payments']), 
            function (Show $show) {
                $show->field('id');
                $show->field('system_order_no', '系统订单号');
                $show->field('merchant.name', '商户');
                $show->field('merchant_order_no', '商户订单号');
                $show->field('order_type', '订单类型')->using([
                    1 => '产品购买',
                    2 => '话费充值',
                ]);
                $show->field('user.name', '用户');
                $show->field('mobile', '手机号');
                $show->field('amount', '订单金额');
                $show->field('cost', '成本金额');
                $show->field('status', '订单状态')->using(Order::$statusTexts);
                $show->field('notify_url', '回调地址');
                $show->field('notify_status', '通知状态')->using([
                    0 => '未通知',
                    1 => '已通知',
                ]);
                $show->field('notify_times', '通知次数');
                $show->field('paid_at', '支付时间');
                $show->field('completed_at', '完成时间');
                $show->field('created_at', '创建时间');
                $show->field('updated_at', '更新时间');
                
                // 支付记录
                $show->payments('支付记录', function ($payments) {
                    $payments->payment_no('支付流水号');
                    $payments->payment_method('支付方式');
                    $payments->payment_amount('支付金额');
                    $payments->voucher_deduct('券抵扣');
                    $payments->status('状态')->using([
                        0 => '待支付',
                        1 => '已支付',
                        2 => '已退款',
                    ]);
                    $payments->paid_at('支付时间');
                    
                    $payments->disableActions();
                    $payments->disableCreateButton();
                });
                
                // 订单日志
                $show->logs('订单日志', function ($logs) {
                    $logs->resource('/admin/order-logs');
                    
                    $logs->created_at('时间')->sortable();
                    $logs->operator_type('操作者');
                    $logs->action('操作');
                    $logs->content('内容');
                    $logs->from_status('原状态')->using(Order::$statusTexts);
                    $logs->to_status('新状态')->using(Order::$statusTexts);
                    
                    $logs->disableActions();
                    $logs->disableCreateButton();
                    $logs->orderBy('created_at', 'desc');
                });
                
                $show->disableEditButton();
                $show->disableDeleteButton();
            });
    }
}
```

### 2. 订单操作

```php
// app/Admin/Actions/CancelOrderAction.php
namespace App\Admin\Actions;

use App\Services\OrderService;
use Dcat\Admin\Actions\RowAction;
use Dcat\Admin\Actions\Response;

class CancelOrderAction extends RowAction
{
    protected $title = '取消订单';
    
    public function confirm()
    {
        return '确定要取消此订单吗？';
    }
    
    public function handle()
    {
        $order = $this->getRow();
        $orderService = app(OrderService::class);
        
        try {
            $orderService->cancelOrder($order, '管理员取消');
            return $this->response()->success('订单已取消')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }
}
```

## 任务调度

### 1. 订单超时取消

```php
// app/Console/Commands/CancelTimeoutOrders.php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OrderService;
use App\Services\OrderQueryService;

class CancelTimeoutOrders extends Command
{
    protected $signature = 'orders:cancel-timeout';
    protected $description = '取消超时未支付的订单';
    
    public function handle()
    {
        $orderService = app(OrderService::class);
        $queryService = app(OrderQueryService::class);
        
        $orders = $queryService->paymentTimeout(30)->get();
        
        foreach ($orders as $order) {
            try {
                $orderService->cancelOrder($order, '支付超时自动取消');
                $this->info("订单 {$order->system_order_no} 已取消");
            } catch (\Exception $e) {
                $this->error("订单 {$order->system_order_no} 取消失败：" . $e->getMessage());
            }
        }
        
        $this->info("共处理 {$orders->count()} 个超时订单");
    }
}

// 在 app/Console/Kernel.php 中注册
protected function schedule(Schedule $schedule)
{
    $schedule->command('orders:cancel-timeout')->everyFiveMinutes();
}
```

### 2. 订单状态通知

```php
// app/Jobs/NotifyOrderStatus.php
namespace App\Jobs;

use App\Models\Order;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NotifyOrderStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    protected $order;
    
    public function __construct(Order $order)
    {
        $this->order = $order;
    }
    
    public function handle()
    {
        $notificationService = app(NotificationService::class);
        
        if ($notificationService->notifyOrderStatus($this->order)) {
            $this->order->update([
                'notify_status' => Order::NOTIFY_SUCCESS,
                'notify_times' => $this->order->notify_times + 1,
            ]);
        } else {
            $this->order->increment('notify_times');
            
            // 如果失败次数未达上限，重新投递任务
            if ($this->order->notify_times < 5) {
                $delay = pow(2, $this->order->notify_times) * 60; // 指数退避
                NotifyOrderStatus::dispatch($this->order)->delay($delay);
            }
        }
    }
}
```

## 事件处理

### 1. 订单创建事件

```php
// app/Events/OrderCreated.php
namespace App\Events;

use App\Models\Order;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderCreated
{
    use Dispatchable, SerializesModels;
    
    public $order;
    
    public function __construct(Order $order)
    {
        $this->order = $order;
    }
}

// app/Listeners/SendOrderCreatedNotification.php
namespace App\Listeners;

use App\Events\OrderCreated;
use App\Notifications\OrderCreatedNotification;

class SendOrderCreatedNotification
{
    public function handle(OrderCreated $event)
    {
        if ($event->order->user) {
            $event->order->user->notify(
                new OrderCreatedNotification($event->order)
            );
        }
    }
}
```

## 性能优化

### 1. 查询优化
```php
// 使用索引优化查询
Order::where('merchant_id', $merchantId)
    ->where('status', Order::STATUS_SUCCESS)
    ->where('created_at', '>=', $startDate)
    ->select(['id', 'system_order_no', 'amount', 'created_at'])
    ->get();

// 避免N+1查询
Order::with(['user:id,name', 'product:id,name'])
    ->limit(100)
    ->get();
```

### 2. 缓存策略
```php
// 缓存订单统计
Cache::remember("order_stats_{$merchantId}", 3600, function () use ($merchantId) {
    return [
        'total' => Order::where('merchant_id', $merchantId)->count(),
        'success' => Order::where('merchant_id', $merchantId)
            ->where('status', Order::STATUS_SUCCESS)->count(),
        'revenue' => Order::where('merchant_id', $merchantId)
            ->where('status', Order::STATUS_SUCCESS)->sum('amount'),
    ];
});
```

## 监控告警

### 1. 订单异常监控
```php
// 监控充值失败率
$failureRate = Order::where('order_type', Order::TYPE_RECHARGE)
    ->where('created_at', '>=', now()->subHour())
    ->where('status', Order::STATUS_FAILED)
    ->count() / Order::where('order_type', Order::TYPE_RECHARGE)
    ->where('created_at', '>=', now()->subHour())
    ->count();

if ($failureRate > 0.1) {
    // 发送告警
}
```

## 下一步计划

1. 实现订单批量操作
2. 添加订单导出功能
3. 优化订单查询性能
4. 实现订单数据分析

---

最后更新：2024-01-20