# 话费充值系统开发文档总览

## 项目概述

### 项目背景
本项目是一个话费充值系统，主要提供话费券销售和充值服务。系统采用敏捷开发方法，使用 Laravel 9 + Dcat Admin 构建，实现前后端分离的现代化架构。

### 核心功能
1. **产品管理**：话费券产品套餐的创建和管理
2. **订单处理**：用户购买产品和充值订单的处理
3. **充值履约**：对接运营商完成话费充值
4. **财务结算**：账户余额和交易结算管理
5. **API服务**：为第三方商户提供标准化API接口

### 技术栈
- **后端框架**：Laravel 9.x
- **管理后台**：Dcat Admin 2.2.3-beta
- **数据库**：MySQL 8.0
- **缓存**：Redis
- **队列**：Laravel Queue
- **API文档**：OpenAPI 3.0

## 系统架构

### 分层架构
```
┌─────────────────────────────────────────┐
│          前端应用层（H5/小程序）          │
├─────────────────────────────────────────┤
│            API网关层                     │
├─────────────────────────────────────────┤
│            业务逻辑层                    │
│  ┌────────┐ ┌────────┐ ┌────────┐     │
│  │产品服务│ │订单服务│ │充值服务│     │
│  └────────┘ └────────┘ └────────┘     │
├─────────────────────────────────────────┤
│            数据访问层                    │
├─────────────────────────────────────────┤
│         数据库 / 缓存 / 消息队列         │
└─────────────────────────────────────────┘
```

### 模块划分
1. **后台管理模块** - 基于 Dcat Admin 的管理后台
2. **API接口模块** - RESTful API 服务
3. **产品管理模块** - 产品和券的管理
4. **订单管理模块** - 订单生命周期管理
5. **充值履约模块** - 充值业务处理
6. **用户券管理模块** - 用户持有券的管理
7. **财务结算模块** - 余额和结算管理

## 开发规范

### 敏捷开发流程
1. **Sprint周期**：2周一个迭代
2. **每日站会**：同步进度和问题
3. **代码评审**：每个PR必须经过评审
4. **持续集成**：自动化测试和部署

### 代码规范
- 遵循 PSR-12 编码标准
- 使用 Laravel Pint 进行代码格式化
- 所有公共方法必须有注释
- 控制器方法不超过30行
- 服务类遵循单一职责原则

### Git工作流
- 主分支：`main`（生产环境）
- 开发分支：`develop`（开发环境）
- 功能分支：`feature/xxx`
- 修复分支：`hotfix/xxx`

### 数据库规范
- 表名使用复数形式
- 字段名使用蛇形命名法
- 必须有 `created_at` 和 `updated_at`
- 软删除使用 `deleted_at`
- 外键命名：`表名_字段名_foreign`

## 模块文档索引

### 核心模块
1. [后台管理模块](./dev-admin-module.md) - Dcat Admin 配置和扩展
2. [API接口模块](./dev-api-module.md) - API设计和实现
3. [产品管理模块](./dev-product-module.md) - 产品和券的业务逻辑
4. [订单管理模块](./dev-order-module.md) - 订单处理流程
5. [充值履约模块](./dev-recharge-module.md) - 充值业务实现
6. [用户券管理模块](./dev-voucher-module.md) - 券的分配和使用
7. [财务结算模块](./dev-finance-module.md) - 账户和结算逻辑

### 通用组件
- [认证授权](./dev-auth.md) - JWT和权限管理
- [异常处理](./dev-exception.md) - 统一异常处理
- [日志监控](./dev-logging.md) - 日志和监控方案
- [任务队列](./dev-queue.md) - 异步任务处理

## 开发环境

### 本地环境要求
- PHP >= 8.0.2
- MySQL >= 8.0
- Redis >= 6.0
- Composer >= 2.0
- Node.js >= 16.0

### 快速开始
```bash
# 克隆项目
git clone [repository_url]

# 安装依赖
composer install
npm install

# 配置环境
cp .env.example .env
php artisan key:generate

# 数据库迁移
php artisan migrate
php artisan db:seed

# 启动服务
php artisan serve
npm run dev
```

### 开发工具推荐
- IDE：PHPStorm / VS Code
- API测试：Postman / Insomnia
- 数据库管理：TablePlus / DBeaver
- Git客户端：SourceTree / GitKraken

## 部署说明

### 环境配置
- 生产环境使用 Nginx + PHP-FPM
- 配置 Supervisor 管理队列进程
- 使用 Redis 进行缓存和会话管理
- 配置定时任务执行 Laravel Schedule

### 部署流程
1. 代码推送到仓库
2. CI/CD 自动构建
3. 运行测试用例
4. 部署到测试环境
5. 人工验收后部署生产

## 项目进度跟踪

### 当前Sprint（Sprint 1）
- [ ] 基础框架搭建
- [ ] 用户认证模块
- [ ] 产品管理CRUD
- [ ] 订单创建流程

### 待办事项
- 充值接口对接
- 支付网关集成
- 报表统计功能
- 性能优化

## 联系方式

- 项目经理：[PM Name]
- 技术负责人：[Tech Lead]
- 产品经理：[Product Manager]

## 更新日志

### 2024-01-20
- 初始化项目结构
- 安装 Dcat Admin
- 创建开发文档框架

---

最后更新：2024-01-20