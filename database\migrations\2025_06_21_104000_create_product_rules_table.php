<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade')->comment('产品ID');
            $table->string('rule_type', 50)->comment('规则类型：limit_per_user,stock_limit,sale_period等');
            $table->json('rule_value')->comment('规则值');
            $table->timestamps();
            
            $table->index(['product_id', 'rule_type']);
            $table->index('rule_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_rules');
    }
};
