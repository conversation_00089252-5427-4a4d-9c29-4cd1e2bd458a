# 账户余额查询接口

## 接口信息
- **请求方式**: POST
- **请求路径**: /api
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sign | string | 是 | 签名 |
| agentAccount | string | 是 | 账号 |
| busiBody | object | 是 | 业务参数体 |
| └─ action | string | 是 | 指令，默认值: YE |

## 请求示例

```json
{
  "sign": "0712dcadafe594fe543c66b50ae7ed58",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "YE"
  }
}
```

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| action | string | 指令 |
| agentAccount | string | 商户账号 |
| agentBalance | number | 预存余额 |
| agentProfit | number | 佣金余额 |
| agentName | string/null | 账户名称 |
| errorCode | integer | 错误码 |
| errorDesc | string/null | 错误描述 |

## 响应示例

```json
{
  "action": "YE",
  "agentAccount": "api_test",
  "agentBalance": 100,
  "agentProfit": 0,
  "agentName": "测试账号",
  "errorCode": 1,
  "errorDesc": "操作完成"
}
```

## CURL请求示例

```bash
curl --location --request POST '/api' \
--header 'Content-Type: application/json' \
--data-raw '{
  "sign": "0712dcadafe594fe543c66b50ae7ed58",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "YE"
  }
}'
```