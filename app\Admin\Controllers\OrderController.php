<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Order;
use App\Models\Order as OrderModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class OrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Order(['merchant', 'user', 'product']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            
            $grid->column('id')->sortable();
            $grid->column('system_order_no', '系统订单号');
            $grid->column('merchant.name', '商户');
            $grid->column('merchant_order_no', '商户订单号');
            $grid->column('order_type', '类型')->using([
                1 => '产品购买',
                2 => '话费充值',
            ])->label([
                1 => 'primary',
                2 => 'success',
            ]);
            $grid->column('mobile', '手机号');
            $grid->column('amount', '金额')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('status', '状态')->using(OrderModel::$statusTexts)
                ->dot([
                    0 => 'warning',
                    1 => 'info',
                    2 => 'primary',
                    6 => 'primary',
                    16 => 'success',
                    26 => 'danger',
                    30 => 'default',
                    40 => 'warning',
                ]);
            $grid->column('created_at', '创建时间');
            
            // 禁用创建按钮
            $grid->disableCreateButton();
            $grid->disableEditButton();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('system_order_no', '系统订单号');
                $filter->equal('merchant_order_no', '商户订单号');
                $filter->equal('mobile', '手机号');
                $filter->equal('merchant_id', '商户')->select(\App\Models\Merchant::pluck('name', 'id'));
                $filter->equal('order_type', '类型')->select([
                    1 => '产品购买',
                    2 => '话费充值',
                ]);
                $filter->equal('status', '状态')->select(OrderModel::$statusTexts);
                $filter->between('created_at', '创建时间')->datetime();
            });
            
            // 快速搜索
            $grid->quickSearch(['system_order_no', 'merchant_order_no', 'mobile']);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Order(['merchant', 'user', 'product', 'logs', 'rechargeRecord']), 
            function (Show $show) {
                $show->field('id');
                $show->field('system_order_no', '系统订单号');
                $show->field('merchant.name', '商户');
                $show->field('merchant_order_no', '商户订单号');
                $show->field('order_type', '订单类型')->using([
                    1 => '产品购买',
                    2 => '话费充值',
                ]);
                $show->field('user.name', '用户');
                $show->field('product.name', '产品');
                $show->field('mobile', '手机号');
                $show->field('amount', '订单金额');
                $show->field('cost', '成本金额');
                $show->field('status', '订单状态')->using(OrderModel::$statusTexts);
                $show->field('notify_url', '回调地址');
                $show->field('notify_status', '通知状态')->using([
                    0 => '未通知',
                    1 => '已通知',
                ]);
                $show->field('notify_times', '通知次数');
                $show->field('remark', '备注');
                $show->field('paid_at', '支付时间');
                $show->field('completed_at', '完成时间');
                $show->field('created_at', '创建时间');
                $show->field('updated_at', '更新时间');
                
                // 充值记录
                $show->relation('rechargeRecord', '充值记录', function ($model) {
                    if (!$model->rechargeRecord) {
                        return '暂无充值记录';
                    }
                    
                    $show = new Show($model->rechargeRecord->id, new \App\Admin\Repositories\RechargeRecord);
                    $show->field('provider', '充值渠道');
                    $show->field('provider_order_no', '渠道订单号');
                    $show->field('status', '充值状态');
                    $show->field('retry_times', '重试次数');
                    $show->field('error_code', '错误码');
                    $show->field('error_msg', '错误信息');
                    $show->field('submitted_at', '提交时间');
                    $show->field('completed_at', '完成时间');
                    
                    $show->disableEditButton();
                    $show->disableDeleteButton();
                    $show->disableListButton();
                    
                    return $show;
                });
                
                // 订单日志
                $show->relation('logs', '订单日志', function ($model) {
                    $grid = new Grid(new \App\Admin\Repositories\OrderLog);
                    $grid->model()->where('order_id', $model->id)->orderBy('created_at', 'desc');
                    
                    $grid->created_at('时间')->sortable();
                    $grid->operator_type('操作者');
                    $grid->action('操作');
                    $grid->content('内容');
                    $grid->from_status('原状态')->using(OrderModel::$statusTexts);
                    $grid->to_status('新状态')->using(OrderModel::$statusTexts);
                    
                    $grid->disableActions();
                    $grid->disableCreateButton();
                    $grid->disableRefreshButton();
                    $grid->disableFilterButton();
                    
                    return $grid;
                });
                
                $show->disableEditButton();
                $show->disableDeleteButton();
            });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Order(), function (Form $form) {
            $form->display('id');
            $form->text('system_order_no', '系统订单号');
            $form->text('merchant_order_no', '商户订单号');
            $form->text('amount', '订单金额');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}