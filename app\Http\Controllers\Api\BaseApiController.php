<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use <PERSON><PERSON>\Fractal\Fractal;
use League\Fractal\TransformerAbstract;
use Illuminate\Http\JsonResponse;

class BaseApiController extends Controller
{
    /**
     * 成功响应
     */
    protected function success($data = null, string $message = 'success', int $code = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'code' => 'SUCCESS',
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toDateTimeString(),
        ], $code);
    }

    /**
     * 错误响应
     */
    protected function error(string $message, string $code = 'ERROR', int $httpCode = 400, $data = null): JsonResponse
    {
        return response()->json([
            'success' => false,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toDateTimeString(),
        ], $httpCode);
    }

    /**
     * 分页响应
     */
    protected function paginated($paginator, TransformerAbstract $transformer, array $includes = []): JsonResponse
    {
        $fractal = Fractal::create()
            ->collection($paginator->items(), $transformer)
            ->parseIncludes($includes);

        $data = $fractal->toArray();
        
        // 添加分页信息
        $data['pagination'] = [
            'current_page' => $paginator->currentPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'last_page' => $paginator->lastPage(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
        ];

        return $this->success($data);
    }

    /**
     * 单个资源响应
     */
    protected function item($item, TransformerAbstract $transformer, array $includes = []): JsonResponse
    {
        $fractal = Fractal::create()
            ->item($item, $transformer)
            ->parseIncludes($includes);

        return $this->success($fractal->toArray());
    }

    /**
     * 集合资源响应
     */
    protected function collection($collection, TransformerAbstract $transformer, array $includes = []): JsonResponse
    {
        $fractal = Fractal::create()
            ->collection($collection, $transformer)
            ->parseIncludes($includes);

        return $this->success($fractal->toArray());
    }

    /**
     * 验证失败响应
     */
    protected function validationError($errors): JsonResponse
    {
        return $this->error('参数验证失败', 'VALIDATION_ERROR', 422, $errors);
    }

    /**
     * 未找到资源响应
     */
    protected function notFound(string $message = '资源不存在'): JsonResponse
    {
        return $this->error($message, 'NOT_FOUND', 404);
    }

    /**
     * 未授权响应
     */
    protected function unauthorized(string $message = '未授权访问'): JsonResponse
    {
        return $this->error($message, 'UNAUTHORIZED', 401);
    }

    /**
     * 禁止访问响应
     */
    protected function forbidden(string $message = '禁止访问'): JsonResponse
    {
        return $this->error($message, 'FORBIDDEN', 403);
    }

    /**
     * 服务器错误响应
     */
    protected function serverError(string $message = '服务器内部错误'): JsonResponse
    {
        return $this->error($message, 'SERVER_ERROR', 500);
    }

    /**
     * 获取当前认证的商户
     */
    protected function getMerchant()
    {
        return request()->get('merchant');
    }

    /**
     * 获取请求参数并验证
     */
    protected function validateRequest(array $rules, array $messages = []): array
    {
        return request()->validate($rules, $messages);
    }

    /**
     * 记录API调用日志
     */
    protected function logApiCall(string $action, array $params = [], $result = null): void
    {
        \Log::info('API调用', [
            'merchant_id' => $this->getMerchant()?->id,
            'action' => $action,
            'params' => $params,
            'result' => $result,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toDateTimeString(),
        ]);
    }
}
