<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchants', function (Blueprint $table) {
            $table->id();
            $table->string('account', 50)->unique()->comment('商户账号');
            $table->string('name', 100)->comment('商户名称');
            $table->string('contact_name', 50)->nullable()->comment('联系人');
            $table->string('contact_phone', 20)->nullable()->comment('联系电话');
            $table->string('email', 100)->nullable()->comment('邮箱');
            $table->string('md5_key', 255)->comment('MD5密钥');
            $table->decimal('balance', 15, 2)->default(0)->comment('余额');
            $table->decimal('frozen_balance', 15, 2)->default(0)->comment('冻结余额');
            $table->boolean('status')->default(true)->comment('状态：0-禁用，1-启用');
            $table->json('ip_whitelist')->nullable()->comment('IP白名单');
            $table->string('remark', 500)->nullable()->comment('备注');
            $table->timestamps();
            
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchants');
    }
};