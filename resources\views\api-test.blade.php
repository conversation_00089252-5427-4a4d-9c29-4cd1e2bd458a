<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试工具</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .response-success {
            border-left: 4px solid #28a745;
        }
        .response-error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">API测试工具</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API请求配置</h5>
                    </div>
                    <div class="card-body">
                        <form id="apiForm">
                            <div class="mb-3">
                                <label for="merchant_id" class="form-label">选择商户</label>
                                <select class="form-select" id="merchant_id" name="merchant_id" required>
                                    <option value="">请选择商户</option>
                                    @foreach($merchants as $merchant)
                                        <option value="{{ $merchant->id }}">{{ $merchant->name }} (ID: {{ $merchant->id }})</option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="api_type" class="form-label">API类型</label>
                                <select class="form-select" id="api_type" name="api_type" required>
                                    <option value="">请选择API</option>
                                    <option value="create_recharge_order">创建充值订单</option>
                                    <option value="query_order">查询订单状态</option>
                                    <option value="get_balance">查询余额</option>
                                    <option value="get_orders">查询订单列表</option>
                                </select>
                            </div>
                            
                            <button type="button" class="btn btn-primary" onclick="generateExample()">生成请求示例</button>
                            <button type="button" class="btn btn-success" onclick="testApi()">测试API</button>
                        </form>
                    </div>
                </div>
                
                <div class="card mt-3" id="exampleCard" style="display: none;">
                    <div class="card-header">
                        <h5>请求示例</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">请求URL</label>
                            <input type="text" class="form-control" id="requestUrl" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">请求方法</label>
                            <input type="text" class="form-control" id="requestMethod" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">请求参数 (JSON)</label>
                            <textarea class="form-control" id="requestParams" rows="8" readonly></textarea>
                        </div>
                        
                        <ul class="nav nav-tabs" id="exampleTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="curl-tab" data-bs-toggle="tab" data-bs-target="#curl" type="button">CURL</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="php-tab" data-bs-toggle="tab" data-bs-target="#php" type="button">PHP</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="exampleTabsContent">
                            <div class="tab-pane fade show active" id="curl" role="tabpanel">
                                <div class="code-block mt-3" id="curlExample"></div>
                            </div>
                            <div class="tab-pane fade" id="php" role="tabpanel">
                                <div class="code-block mt-3" id="phpExample"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card" id="responseCard" style="display: none;">
                    <div class="card-header">
                        <h5>API响应</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">HTTP状态码</label>
                            <span class="badge" id="statusCode"></span>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">响应内容</label>
                            <div class="code-block" id="responseBody"></div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>API文档</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-info" onclick="loadDocumentation()">查看API文档</button>
                        <div id="documentation" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 设置CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        function generateExample() {
            const merchantId = document.getElementById('merchant_id').value;
            const apiType = document.getElementById('api_type').value;
            
            if (!merchantId || !apiType) {
                alert('请选择商户和API类型');
                return;
            }
            
            fetch('/api-test/generate-example', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    merchant_id: merchantId,
                    api_type: apiType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('错误: ' + data.error);
                    return;
                }
                
                document.getElementById('requestUrl').value = data.url;
                document.getElementById('requestMethod').value = data.method;
                document.getElementById('requestParams').value = JSON.stringify(data.params, null, 2);
                document.getElementById('curlExample').textContent = data.curl_example;
                document.getElementById('phpExample').textContent = data.php_example;
                
                document.getElementById('exampleCard').style.display = 'block';
            })
            .catch(error => {
                console.error('Error:', error);
                alert('生成示例失败');
            });
        }
        
        function testApi() {
            const url = document.getElementById('requestUrl').value;
            const method = document.getElementById('requestMethod').value;
            const params = document.getElementById('requestParams').value;
            
            if (!url || !method || !params) {
                alert('请先生成请求示例');
                return;
            }
            
            let parsedParams;
            try {
                parsedParams = JSON.parse(params);
            } catch (e) {
                alert('请求参数格式错误');
                return;
            }
            
            fetch('/api-test/test-api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    url: url,
                    method: method,
                    params: parsedParams
                })
            })
            .then(response => response.json())
            .then(data => {
                const statusCode = document.getElementById('statusCode');
                const responseBody = document.getElementById('responseBody');
                const responseCard = document.getElementById('responseCard');
                
                if (data.success) {
                    statusCode.textContent = data.status_code;
                    statusCode.className = 'badge ' + (data.status_code < 400 ? 'bg-success' : 'bg-danger');
                    responseBody.textContent = JSON.stringify(data.body, null, 2);
                    responseBody.className = 'code-block response-success';
                } else {
                    statusCode.textContent = 'ERROR';
                    statusCode.className = 'badge bg-danger';
                    responseBody.textContent = 'Error: ' + data.error;
                    responseBody.className = 'code-block response-error';
                }
                
                responseCard.style.display = 'block';
            })
            .catch(error => {
                console.error('Error:', error);
                alert('API测试失败');
            });
        }
        
        function loadDocumentation() {
            fetch('/api-test/documentation')
            .then(response => response.json())
            .then(data => {
                let html = '<h6>API列表</h6>';
                data.apis.forEach(api => {
                    html += `
                        <div class="border rounded p-3 mb-3">
                            <h6>${api.name}</h6>
                            <p><strong>URL:</strong> ${api.url}</p>
                            <p><strong>方法:</strong> ${api.method}</p>
                            <p><strong>描述:</strong> ${api.description}</p>
                            <p><strong>参数:</strong></p>
                            <ul>
                    `;
                    Object.entries(api.params).forEach(([key, desc]) => {
                        html += `<li><code>${key}</code>: ${desc}</li>`;
                    });
                    html += '</ul></div>';
                });
                
                html += '<h6>签名规则</h6><ol>';
                data.signature_rules.forEach(rule => {
                    html += `<li>${rule}</li>`;
                });
                html += '</ol>';
                
                document.getElementById('documentation').innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                alert('加载文档失败');
            });
        }
    </script>
</body>
</html>
