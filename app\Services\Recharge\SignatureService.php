<?php

namespace App\Services\Recharge;

/**
 * 签名服务类
 * 
 * 负责生成和验证API请求签名
 */
class SignatureService
{
    /**
     * 生成签名
     * 
     * @param array $busiBody 业务参数数组
     * @param string $md5Key MD5密钥
     * @return string 32位小写MD5签名
     */
    public static function generateSign(array $busiBody, string $md5Key): string
    {
        // 将业务参数转换为JSON字符串（不含空格）
        $jsonStr = json_encode($busiBody, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        
        // 拼接密钥
        $signStr = $jsonStr . $md5Key;
        
        // 生成MD5签名（32位小写）
        return md5($signStr);
    }

    /**
     * 验证签名
     * 
     * @param string $sign 待验证的签名
     * @param array $busiBody 业务参数数组
     * @param string $md5Key MD5密钥
     * @return bool 验证是否通过
     */
    public static function verifySign(string $sign, array $busiBody, string $md5Key): bool
    {
        $expectedSign = self::generateSign($busiBody, $md5Key);
        
        // 使用安全的字符串比较函数防止时序攻击
        return hash_equals($expectedSign, $sign);
    }

    /**
     * 构建完整的请求数据
     * 
     * @param string $agentAccount 商户账号
     * @param array $busiBody 业务参数
     * @param string $md5Key MD5密钥
     * @return array 包含签名的完整请求数据
     */
    public static function buildRequestData(string $agentAccount, array $busiBody, string $md5Key): array
    {
        return [
            'sign' => self::generateSign($busiBody, $md5Key),
            'agentAccount' => $agentAccount,
            'busiBody' => $busiBody
        ];
    }
}