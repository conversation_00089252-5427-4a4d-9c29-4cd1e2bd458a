<?php

namespace App\Transformers;

use App\Models\OrderPayment;
use League\Fractal\TransformerAbstract;

class PaymentTransformer extends TransformerAbstract
{
    /**
     * 转换支付数据
     */
    public function transform(OrderPayment $payment): array
    {
        return [
            'id' => $payment->id,
            'payment_no' => $payment->payment_no,
            'payment_method' => $payment->payment_method,
            'method_text' => $payment->method_text,
            'payment_amount' => (float) $payment->payment_amount,
            'voucher_deduct' => (float) $payment->voucher_deduct,
            'actual_amount' => (float) $payment->actual_amount,
            'status' => $payment->status,
            'status_text' => $payment->status_text,
            'paid_at' => $payment->paid_at?->toDateTimeString(),
            'refunded_at' => $payment->refunded_at?->toDateTimeString(),
            'created_at' => $payment->created_at->toDateTimeString(),
        ];
    }
}
