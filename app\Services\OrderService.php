<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderLog;
use App\Models\OrderPayment;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OrderService
{
    protected $productService;
    protected $voucherService;
    protected $balanceService;

    public function __construct(
        ProductService $productService,
        VoucherService $voucherService,
        BalanceService $balanceService
    ) {
        $this->productService = $productService;
        $this->voucherService = $voucherService;
        $this->balanceService = $balanceService;
    }

    /**
     * 创建产品购买订单
     */
    public function createProductOrder(array $data): array
    {
        try {
            DB::beginTransaction();

            // 验证产品
            $validation = $this->productService->validatePurchase(
                $data['product_id'],
                $data['user_id'],
                $data['quantity'] ?? 1
            );

            if (!$validation['success']) {
                return $validation;
            }

            $product = $validation['product'];
            $quantity = $data['quantity'] ?? 1;

            // 计算订单金额
            $priceCalculation = $this->productService->calculateTotalPrice(
                $data['product_id'],
                $quantity
            );

            if (!$priceCalculation['success']) {
                return $priceCalculation;
            }

            $priceData = $priceCalculation['data'];

            // 创建订单
            $order = Order::create([
                'order_no' => $this->generateOrderNo(),
                'merchant_order_no' => $data['merchant_order_no'] ?? null,
                'user_id' => $data['user_id'],
                'merchant_id' => $data['merchant_id'] ?? null,
                'type' => Order::TYPE_PRODUCT,
                'product_id' => $product->id,
                'quantity' => $quantity,
                'amount' => $priceData['total_price'],
                'cost' => $priceData['total_cost'],
                'status' => Order::STATUS_PENDING,
                'notify_url' => $data['notify_url'] ?? null,
                'remark' => $data['remark'] ?? '',
            ]);

            // 记录订单日志
            $this->addOrderLog($order, 'create', '订单创建', $data['operator'] ?? 'system');

            DB::commit();

            Log::info('产品订单创建成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $data['user_id'],
                'product_id' => $product->id,
                'amount' => $priceData['total_price'],
            ]);

            return [
                'success' => true,
                'order' => $order,
                'price_data' => $priceData,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建产品订单失败', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '订单创建失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 创建充值订单
     */
    public function createRechargeOrder(array $data): array
    {
        try {
            DB::beginTransaction();

            // 验证充值金额
            if (!$this->isValidRechargeAmount($data['amount'])) {
                return ['success' => false, 'message' => '充值金额不支持'];
            }

            // 验证手机号
            if (!$this->isValidMobile($data['mobile'])) {
                return ['success' => false, 'message' => '手机号格式不正确'];
            }

            // 计算券抵扣
            $voucherDeduction = 0;
            $usedVouchers = [];
            if (!empty($data['voucher_ids'])) {
                $voucherResult = $this->voucherService->useVouchers(
                    $data['voucher_ids'],
                    $data['user_id']
                );

                if (!$voucherResult['success']) {
                    return $voucherResult;
                }

                $voucherDeduction = $voucherResult['total_deduction'];
                $usedVouchers = $voucherResult['used_vouchers'];
            }

            // 计算实际支付金额
            $actualAmount = $data['amount'] - $voucherDeduction;
            if ($actualAmount < 0) {
                $actualAmount = 0;
            }

            // 创建订单
            $order = Order::create([
                'order_no' => $this->generateOrderNo(),
                'merchant_order_no' => $data['merchant_order_no'] ?? null,
                'user_id' => $data['user_id'],
                'merchant_id' => $data['merchant_id'] ?? null,
                'type' => Order::TYPE_RECHARGE,
                'mobile' => $data['mobile'],
                'amount' => $data['amount'],
                'voucher_deduct' => $voucherDeduction,
                'actual_amount' => $actualAmount,
                'status' => Order::STATUS_PENDING,
                'notify_url' => $data['notify_url'] ?? null,
                'remark' => $data['remark'] ?? '',
            ]);

            // 更新券的使用订单ID
            if (!empty($usedVouchers)) {
                foreach ($usedVouchers as $voucher) {
                    \App\Models\UserVoucher::where('id', $voucher['id'])
                        ->update(['used_order_id' => $order->id]);
                }
            }

            // 记录订单日志
            $this->addOrderLog($order, 'create', '充值订单创建', $data['operator'] ?? 'system');

            DB::commit();

            Log::info('充值订单创建成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'mobile' => $data['mobile'],
                'amount' => $data['amount'],
                'voucher_deduct' => $voucherDeduction,
                'actual_amount' => $actualAmount,
            ]);

            return [
                'success' => true,
                'order' => $order,
                'voucher_deduction' => $voucherDeduction,
                'actual_amount' => $actualAmount,
                'used_vouchers' => $usedVouchers,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建充值订单失败', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '订单创建失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 处理订单支付
     */
    public function processOrderPayment(Order $order, array $paymentData): array
    {
        try {
            DB::beginTransaction();

            // 检查订单状态
            if ($order->status !== Order::STATUS_PENDING) {
                return ['success' => false, 'message' => '订单状态不允许支付'];
            }

            // 创建支付记录
            $payment = OrderPayment::create([
                'order_id' => $order->id,
                'payment_no' => $this->generatePaymentNo(),
                'payment_method' => $paymentData['method'],
                'payment_amount' => $order->actual_amount ?? $order->amount,
                'voucher_deduct' => $order->voucher_deduct ?? 0,
                'status' => OrderPayment::STATUS_PENDING,
            ]);

            // 根据支付方式处理
            $paymentResult = match ($paymentData['method']) {
                OrderPayment::METHOD_BALANCE => $this->processBalancePayment($order, $payment, $paymentData),
                default => ['success' => false, 'message' => '不支持的支付方式'],
            };

            if (!$paymentResult['success']) {
                DB::rollBack();
                return $paymentResult;
            }

            // 更新订单状态
            $order->update([
                'status' => Order::STATUS_PAID,
                'paid_at' => now(),
            ]);

            // 记录订单日志
            $this->addOrderLog($order, 'paid', '订单支付成功', $paymentData['operator'] ?? 'system');

            // 如果是产品订单，发放券
            if ($order->type === Order::TYPE_PRODUCT) {
                $this->voucherService->issueVouchersForOrder($order);
                $this->productService->updateSalesCount($order->product_id, $order->quantity);
            }

            DB::commit();

            Log::info('订单支付成功', [
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'method' => $paymentData['method'],
                'amount' => $payment->payment_amount,
            ]);

            return [
                'success' => true,
                'order' => $order->fresh(),
                'payment' => $payment->fresh(),
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('订单支付失败', [
                'order_id' => $order->id,
                'payment_data' => $paymentData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '支付失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 处理余额支付
     */
    private function processBalancePayment(Order $order, OrderPayment $payment, array $paymentData): array
    {
        if (!isset($paymentData['merchant_id'])) {
            return ['success' => false, 'message' => '缺少商户信息'];
        }

        // 扣除商户余额
        $deductResult = $this->balanceService->deductMerchantBalance(
            $paymentData['merchant_id'],
            $payment->payment_amount,
            'order',
            $order->id,
            '订单支付'
        );

        if (!$deductResult['success']) {
            return $deductResult;
        }

        // 更新支付状态
        $payment->update([
            'status' => OrderPayment::STATUS_PAID,
            'paid_at' => now(),
        ]);

        return ['success' => true];
    }

    /**
     * 更新订单状态
     */
    public function updateOrderStatus(Order $order, $status, $operator = 'system', $remark = ''): bool
    {
        try {
            $oldStatus = $order->status;
            $order->update(['status' => $status]);

            $this->addOrderLog($order, 'status_change', "状态从 {$oldStatus} 变更为 {$status}", $operator, [
                'old_status' => $oldStatus,
                'new_status' => $status,
                'remark' => $remark,
            ]);

            Log::info('订单状态更新', [
                'order_id' => $order->id,
                'old_status' => $oldStatus,
                'new_status' => $status,
                'operator' => $operator,
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('订单状态更新失败', [
                'order_id' => $order->id,
                'status' => $status,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 添加订单日志
     */
    public function addOrderLog(Order $order, $action, $content, $operator = 'system', $extraData = null): void
    {
        OrderLog::create([
            'order_id' => $order->id,
            'operator' => $operator,
            'action' => $action,
            'content' => $content,
            'extra_data' => $extraData,
        ]);
    }

    /**
     * 生成订单号
     */
    private function generateOrderNo(): string
    {
        return 'ORD' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 生成支付流水号
     */
    private function generatePaymentNo(): string
    {
        return 'PAY' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 验证充值金额
     */
    private function isValidRechargeAmount($amount): bool
    {
        $configService = app(ConfigService::class);
        $supportedAmounts = $configService->getSupportedAmounts();
        return in_array($amount, $supportedAmounts);
    }

    /**
     * 验证手机号
     */
    private function isValidMobile($mobile): bool
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile);
    }

    /**
     * 获取订单详情
     */
    public function getOrderDetail($orderId, $userId = null): ?array
    {
        $query = Order::with(['product', 'payments', 'logs']);
        
        if ($userId) {
            $query->where('user_id', $userId);
        }
        
        $order = $query->find($orderId);
        
        if (!$order) {
            return null;
        }

        return [
            'id' => $order->id,
            'order_no' => $order->order_no,
            'type' => $order->type,
            'type_text' => $order->type_text,
            'status' => $order->status,
            'status_text' => $order->status_text,
            'amount' => $order->amount,
            'voucher_deduct' => $order->voucher_deduct,
            'actual_amount' => $order->actual_amount,
            'mobile' => $order->mobile,
            'product' => $order->product ? [
                'id' => $order->product->id,
                'name' => $order->product->name,
                'price' => $order->product->price,
            ] : null,
            'quantity' => $order->quantity,
            'created_at' => $order->created_at->format('Y-m-d H:i:s'),
            'paid_at' => $order->paid_at?->format('Y-m-d H:i:s'),
            'completed_at' => $order->completed_at?->format('Y-m-d H:i:s'),
        ];
    }
}
