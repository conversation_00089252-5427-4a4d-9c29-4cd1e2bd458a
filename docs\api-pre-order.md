# 预下单接口

## 接口信息
- **请求方式**: POST
- **请求路径**: /api
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sign | string | 是 | 签名 |
| agentAccount | string | 是 | 账号 |
| busiBody | object | 是 | 业务参数体 |
| └─ action | string | 是 | 指令，固定值: PRE |
| └─ productId | string | 是 | 产品编码 |
| └─ phone | string | 是 | 充值号码 |
| └─ type | string | 是 | 类型 |

## 请求示例

```json
{
  "sign": "10c9d1ce99cf3a3bae20ae219621ad70",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "PRE",
    "productId": "YD031111001001",
    "phone": "***********",
    "type": "0"
  }
}
```

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| errorCode | integer | 错误码 |
| errorDesc | string | 错误描述 |
| orderPayment | number | 订单支付金额 |

## 响应示例

```json
{
  "errorCode": 1,
  "errorDesc": "",
  "orderPayment": 49.6
}
```

## CURL请求示例

```bash
curl --location --request POST '/api' \
--header 'Content-Type: application/json' \
--data-raw '{
  "sign": "10c9d1ce99cf3a3bae20ae219621ad70",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "PRE",
    "productId": "YD031111001001",
    "phone": "***********",
    "type": "0"
  }
}'
```