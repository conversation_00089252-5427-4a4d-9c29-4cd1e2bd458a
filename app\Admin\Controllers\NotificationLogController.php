<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\NotificationLog;
use App\Models\NotificationLog as NotificationLogModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Table;

class NotificationLogController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new NotificationLog(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('type', '通知类型')->using(NotificationLogModel::$typeTexts);
            $grid->column('channel', '通知渠道')->using(NotificationLogModel::$channelTexts)
                ->label([
                    'http' => 'primary',
                    'email' => 'success',
                    'sms' => 'warning',
                    'webhook' => 'info',
                ]);
            $grid->column('recipient', '接收者')->limit(30);
            $grid->column('subject', '主题')->limit(20);
            $grid->column('status', '状态')->using(NotificationLogModel::$statusTexts)
                ->label([
                    NotificationLogModel::STATUS_PENDING => 'secondary',
                    NotificationLogModel::STATUS_SENDING => 'primary',
                    NotificationLogModel::STATUS_SUCCESS => 'success',
                    NotificationLogModel::STATUS_FAILED => 'danger',
                ]);
            $grid->column('retry_times', '重试次数');
            $grid->column('sent_at', '发送时间');
            $grid->column('created_at', '创建时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('type', '通知类型')->select(NotificationLogModel::$typeTexts);
                $filter->equal('channel', '通知渠道')->select(NotificationLogModel::$channelTexts);
                $filter->equal('status', '状态')->select(NotificationLogModel::$statusTexts);
                $filter->like('recipient', '接收者');
                $filter->between('created_at', '创建时间')->datetime();
            });

            $grid->quickSearch(['recipient', 'subject']);

            // 添加统计信息
            $grid->header(function () {
                $stats = NotificationLogModel::getSendStats(7);

                $html = '<div class="row mb-3" style="margin-top: 1rem !important; margin-bottom: 0rem !important;display: flex;justify-content: space-between;">';
                $html .= '<div class="col-md-2"><div class="info-box" style="margin-bottom: 0rem !important;"><div class="info-box-content"><span class="info-box-text">总数</span><span class="info-box-number">' . $stats['total'] . '</span></div></div></div>';

                foreach ($stats['by_status'] as $status => $stat) {
                    $color = $status == NotificationLogModel::STATUS_SUCCESS ? 'success' :
                            ($status == NotificationLogModel::STATUS_FAILED ? 'danger' : 'primary');
                    $html .= '<div class="col-md-2"><div class="info-box" style="margin-bottom: 0rem !important;"><div class="info-box-content"><span class="info-box-text">' . $stat['name'] . '</span><span class="info-box-number text-' . $color . '">' . $stat['count'] . '</span></div></div></div>';
                }

                $html .= '</div>';

                return $html;
            });

            // 默认按创建时间倒序
            $grid->model()->orderBy('created_at', 'desc');

            // 禁用创建和编辑
            $grid->disableCreateButton();
            $grid->disableEditButton();

            // 添加重试按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $log = $actions->row;
                if ($log->canRetry()) {
                    $actions->append('<a href="javascript:void(0)" class="btn btn-sm btn-warning retry-btn" data-id="' . $log->id . '">重试</a>');
                }
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new NotificationLog(), function (Show $show) {
            $show->field('id');
            $show->field('type', '通知类型')->using(NotificationLogModel::$typeTexts);
            $show->field('channel', '通知渠道')->using(NotificationLogModel::$channelTexts);
            $show->field('recipient', '接收者');
            $show->field('subject', '主题');
            $show->field('content', '内容')->textarea();
            $show->field('headers', '请求头')->json();
            $show->field('params', '请求参数')->json();
            $show->field('status', '状态')->using(NotificationLogModel::$statusTexts);
            $show->field('retry_times', '重试次数');
            $show->field('max_retry', '最大重试次数');
            $show->field('next_retry_at', '下次重试时间');
            $show->field('sent_at', '发送时间');
            $show->field('response', '响应内容')->textarea();
            $show->field('error_msg', '错误信息');
            $show->field('related_type', '关联类型');
            $show->field('related_id', '关联ID');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 通知统计页面
     */
    public function stats(Content $content)
    {
        return $content
            ->title('通知统计')
            ->description('通知发送统计分析')
            ->body(function () {
                $stats = NotificationLogModel::getSendStats(30);

                // 状态统计表
                $statusTable = new Table(['状态', '数量', '占比'], []);
                foreach ($stats['by_status'] as $status => $stat) {
                    $percentage = $stats['total'] > 0 ? round(($stat['count'] / $stats['total']) * 100, 2) : 0;
                    $statusTable->rows[] = [
                        $stat['name'],
                        $stat['count'],
                        $percentage . '%'
                    ];
                }

                // 渠道统计表
                $channelTable = new Table(['渠道', '数量', '占比'], []);
                foreach ($stats['by_channel'] as $channel => $stat) {
                    $percentage = $stats['total'] > 0 ? round(($stat['count'] / $stats['total']) * 100, 2) : 0;
                    $channelTable->rows[] = [
                        $stat['name'],
                        $stat['count'],
                        $percentage . '%'
                    ];
                }

                // 待重试通知
                $retryLogs = NotificationLogModel::getPendingRetries();
                $retryTable = new Table(['ID', '类型', '渠道', '接收者', '重试次数', '下次重试时间'], []);
                foreach ($retryLogs->take(10) as $log) {
                    $retryTable->rows[] = [
                        $log->id,
                        $log->type_text,
                        $log->channel_text,
                        $log->recipient,
                        $log->retry_times . '/' . $log->max_retry,
                        $log->next_retry_at ? $log->next_retry_at->format('Y-m-d H:i:s') : '-',
                    ];
                }

                return [
                    new Card('发送状态统计（近30天）', $statusTable),
                    new Card('渠道分布统计（近30天）', $channelTable),
                    new Card('待重试通知（前10条）', $retryTable),
                ];
            });
    }

    /**
     * 重试通知
     */
    public function retry($id)
    {
        $log = NotificationLogModel::find($id);

        if (!$log) {
            return response()->json(['success' => false, 'message' => '通知记录不存在']);
        }

        if (!$log->canRetry()) {
            return response()->json(['success' => false, 'message' => '该通知不能重试']);
        }

        // 这里应该调用通知发送服务
        // NotificationService::send($log);

        return response()->json(['success' => true, 'message' => '重试请求已提交']);
    }
}
