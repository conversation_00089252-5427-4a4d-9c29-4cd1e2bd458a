<?php

namespace App\Http\Controllers;

use App\Services\SignatureService;
use App\Models\Merchant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ApiTestController extends Controller
{
    protected $signatureService;

    public function __construct(SignatureService $signatureService)
    {
        $this->signatureService = $signatureService;
    }

    /**
     * API测试页面
     */
    public function index()
    {
        $merchants = Merchant::where('status', 1)->get();
        
        return view('api-test', compact('merchants'));
    }

    /**
     * 生成API请求示例
     */
    public function generateExample(Request $request)
    {
        $merchantId = $request->input('merchant_id');
        $apiType = $request->input('api_type');
        
        $merchant = Merchant::find($merchantId);
        if (!$merchant) {
            return response()->json(['error' => '商户不存在']);
        }

        $examples = [
            'create_recharge_order' => [
                'url' => '/api/v1/recharge/create-order',
                'method' => 'POST',
                'params' => [
                    'merchant_id' => $merchantId,
                    'mobile' => '13800138000',
                    'amount' => 10,
                    'merchant_order_no' => 'TEST' . time(),
                    'notify_url' => 'https://your-domain.com/notify',
                    'remark' => '测试充值',
                ],
            ],
            'query_order' => [
                'url' => '/api/v1/recharge/query-order',
                'method' => 'POST',
                'params' => [
                    'merchant_id' => $merchantId,
                    'merchant_order_no' => 'TEST123456789',
                ],
            ],
            'get_balance' => [
                'url' => '/api/v1/balance',
                'method' => 'GET',
                'params' => [
                    'merchant_id' => $merchantId,
                ],
            ],
            'get_orders' => [
                'url' => '/api/v1/orders',
                'method' => 'GET',
                'params' => [
                    'merchant_id' => $merchantId,
                    'page' => 1,
                    'per_page' => 10,
                ],
            ],
        ];

        if (!isset($examples[$apiType])) {
            return response()->json(['error' => '不支持的API类型']);
        }

        $example = $examples[$apiType];
        
        // 生成签名
        $signedParams = $this->signatureService->createSignedParams(
            $example['params'],
            $merchant->md5_key
        );

        return response()->json([
            'url' => config('app.url') . $example['url'],
            'method' => $example['method'],
            'params' => $signedParams,
            'curl_example' => $this->generateCurlExample(
                config('app.url') . $example['url'],
                $example['method'],
                $signedParams
            ),
            'php_example' => $this->generatePhpExample(
                config('app.url') . $example['url'],
                $example['method'],
                $signedParams
            ),
        ]);
    }

    /**
     * 测试API调用
     */
    public function testApi(Request $request)
    {
        try {
            $url = $request->input('url');
            $method = $request->input('method');
            $params = $request->input('params', []);

            $response = Http::timeout(30)->{strtolower($method)}($url, $params);

            return response()->json([
                'success' => true,
                'status_code' => $response->status(),
                'headers' => $response->headers(),
                'body' => $response->json() ?: $response->body(),
                'request' => [
                    'url' => $url,
                    'method' => $method,
                    'params' => $params,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'request' => [
                    'url' => $request->input('url'),
                    'method' => $request->input('method'),
                    'params' => $request->input('params', []),
                ],
            ]);
        }
    }

    /**
     * 生成CURL示例
     */
    private function generateCurlExample($url, $method, $params)
    {
        if (strtoupper($method) === 'GET') {
            $queryString = http_build_query($params);
            return "curl -X GET '{$url}?{$queryString}'";
        } else {
            $jsonParams = json_encode($params, JSON_UNESCAPED_UNICODE);
            return "curl -X {$method} '{$url}' \\\n" .
                   "  -H 'Content-Type: application/json' \\\n" .
                   "  -d '{$jsonParams}'";
        }
    }

    /**
     * 生成PHP示例
     */
    private function generatePhpExample($url, $method, $params)
    {
        $code = "<?php\n\n";
        $code .= "// 请求参数\n";
        $code .= '$params = ' . var_export($params, true) . ";\n\n";
        
        if (strtoupper($method) === 'GET') {
            $code .= "// GET请求\n";
            $code .= '$url = "' . $url . '?" . http_build_query($params);' . "\n";
            $code .= '$response = file_get_contents($url);' . "\n";
        } else {
            $code .= "// POST请求\n";
            $code .= '$url = "' . $url . '";' . "\n";
            $code .= '$postData = json_encode($params);' . "\n\n";
            $code .= '$context = stream_context_create([' . "\n";
            $code .= '    "http" => [' . "\n";
            $code .= '        "method" => "' . $method . '",' . "\n";
            $code .= '        "header" => "Content-Type: application/json",' . "\n";
            $code .= '        "content" => $postData,' . "\n";
            $code .= '    ],' . "\n";
            $code .= ']);' . "\n\n";
            $code .= '$response = file_get_contents($url, false, $context);' . "\n";
        }
        
        $code .= '$result = json_decode($response, true);' . "\n";
        $code .= 'var_dump($result);' . "\n";

        return $code;
    }

    /**
     * 获取API文档
     */
    public function documentation()
    {
        $apis = [
            [
                'name' => '创建充值订单',
                'url' => '/api/v1/recharge/create-order',
                'method' => 'POST',
                'description' => '创建话费充值订单',
                'params' => [
                    'merchant_id' => '商户ID（必填）',
                    'mobile' => '手机号（必填，11位数字）',
                    'amount' => '充值金额（必填，数字）',
                    'merchant_order_no' => '商户订单号（必填，最长64位）',
                    'notify_url' => '回调地址（可选）',
                    'remark' => '备注（可选）',
                    'timestamp' => '时间戳（必填）',
                    'nonce' => '随机字符串（必填）',
                    'sign' => 'MD5签名（必填）',
                ],
            ],
            [
                'name' => '查询订单状态',
                'url' => '/api/v1/recharge/query-order',
                'method' => 'POST',
                'description' => '查询充值订单状态',
                'params' => [
                    'merchant_id' => '商户ID（必填）',
                    'order_no' => '系统订单号（与merchant_order_no二选一）',
                    'merchant_order_no' => '商户订单号（与order_no二选一）',
                    'timestamp' => '时间戳（必填）',
                    'nonce' => '随机字符串（必填）',
                    'sign' => 'MD5签名（必填）',
                ],
            ],
            [
                'name' => '查询余额',
                'url' => '/api/v1/balance',
                'method' => 'GET',
                'description' => '查询商户余额信息',
                'params' => [
                    'merchant_id' => '商户ID（必填）',
                    'timestamp' => '时间戳（必填）',
                    'nonce' => '随机字符串（必填）',
                    'sign' => 'MD5签名（必填）',
                ],
            ],
            [
                'name' => '查询订单列表',
                'url' => '/api/v1/orders',
                'method' => 'GET',
                'description' => '查询订单列表',
                'params' => [
                    'merchant_id' => '商户ID（必填）',
                    'page' => '页码（可选，默认1）',
                    'per_page' => '每页数量（可选，默认20，最大100）',
                    'type' => '订单类型（可选：product,recharge）',
                    'status' => '订单状态（可选：0-5）',
                    'mobile' => '手机号（可选）',
                    'start_date' => '开始日期（可选）',
                    'end_date' => '结束日期（可选）',
                    'timestamp' => '时间戳（必填）',
                    'nonce' => '随机字符串（必填）',
                    'sign' => 'MD5签名（必填）',
                ],
            ],
        ];

        return response()->json([
            'apis' => $apis,
            'signature_rules' => [
                '1. 将所有参数（除sign外）按键名排序',
                '2. 拼接成 key1=value1&key2=value2 格式',
                '3. 在末尾添加 &key=商户密钥',
                '4. 对整个字符串进行MD5加密并转大写',
            ],
            'response_format' => [
                'success' => '是否成功（boolean）',
                'code' => '响应码（string）',
                'message' => '响应消息（string）',
                'data' => '响应数据（object|array）',
                'timestamp' => '响应时间（string）',
            ],
        ]);
    }
}
