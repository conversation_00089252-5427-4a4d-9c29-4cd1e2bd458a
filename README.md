# 充值系统 (Charge System)

基于 Laravel 9 + Dcat Admin 的话费充值管理系统，提供完整的商户管理、产品管理、订单处理和充值API接口。

## 项目简介

本系统是一个B2B话费充值平台，支持多商户管理，提供标准化的充值API接口，实现话费自动充值功能。系统采用前后端分离架构，后台管理使用 Dcat Admin，API接口遵循RESTful规范。

### 核心功能

- **商户管理**: 多商户账号体系，支持独立密钥和IP白名单
- **产品管理**: 灵活的产品配置，支持多种面额和优惠券
- **订单系统**: 完整的订单生命周期管理，支持状态追踪
- **充值接口**: 标准化API接口，支持话费充值和订单查询
- **统计报表**: 实时数据统计，包括销售报表和充值统计
- **安全机制**: MD5签名验证、IP白名单、请求频率限制

## 技术栈

- **后端框架**: Laravel 9.x
- **管理后台**: Dcat Admin 2.2.3-beta
- **数据库**: MySQL 5.7+
- **PHP版本**: PHP 8.0+
- **其他**: Composer, Redis(可选)

## 安装部署

### 1. 环境要求

- PHP >= 8.0
- MySQL >= 5.7
- Composer
- 以下PHP扩展:
  - BCMath PHP Extension
  - Ctype PHP Extension
  - cURL PHP Extension
  - DOM PHP Extension
  - Fileinfo PHP Extension
  - JSON PHP Extension
  - Mbstring PHP Extension
  - OpenSSL PHP Extension
  - PCRE PHP Extension
  - PDO PHP Extension
  - Tokenizer PHP Extension
  - XML PHP Extension

### 2. 安装步骤

克隆项目
```bash
git clone https://github.com/your-repo/charge.git
cd charge
```

安装依赖
```bash
composer install
```

复制环境配置文件
```bash
cp .env.example .env
```

生成应用密钥
```bash
php artisan key:generate
```

配置数据库连接，编辑 `.env` 文件
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=charge
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

配置充值API参数
```env
# 充值API配置
RECHARGE_API_URL=http://api.example.com/api
RECHARGE_AGENT_ACCOUNT=your_agent_account
RECHARGE_MD5_KEY=your_md5_key
RECHARGE_TIMEOUT=30
RECHARGE_ENABLE_LOG=true
```

执行数据库迁移
```bash
php artisan migrate
```

发布 Dcat Admin 资源
```bash
php artisan admin:publish
```

创建管理员账号
```bash
php artisan admin:create-user
```

设置存储目录权限
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

启动开发服务器
```bash
php artisan serve
```

访问后台管理系统: http://localhost:8000/admin

## 使用说明

### 后台管理

1. **商户管理**
   - 创建商户账号，设置API密钥
   - 配置IP白名单确保安全
   - 管理商户余额和状态

2. **产品管理**
   - 配置充值产品（话费面额）
   - 设置产品价格和状态
   - 管理产品优惠券

3. **订单管理**
   - 查看所有订单记录
   - 跟踪订单状态变化
   - 查看订单详细信息

4. **充值记录**
   - 查看实际充值记录
   - 监控充值成功率
   - 分析充值失败原因

5. **统计报表**
   - 销售统计：查看销售趋势、热销产品
   - 充值统计：分析充值成功率、运营商分布

### API接口使用

#### 1. 话费充值

**请求地址**: `POST /api`

**请求示例**:
```json
{
    "sign": "32位MD5签名",
    "agentAccount": "商户账号",
    "busiBody": {
        "action": "CZ",
        "orderId": "商户订单号",
        "chargeAcct": "***********",
        "chargeCash": "100",
        "chargeType": "0",
        "retUrl": "http%3A%2F%2Fyour-callback.com"
    }
}
```

**响应示例**:
```json
{
    "chargeId": "系统流水号",
    "errorCode": 1,
    "errorDesc": "操作成功"
}
```

#### 2. 订单查询

**请求地址**: `POST /api`

**请求示例**:
```json
{
    "sign": "32位MD5签名",
    "agentAccount": "商户账号",
    "busiBody": {
        "action": "CX",
        "orderId": "商户订单号"
    }
}
```

**响应示例**:
```json
{
    "chargeId": "系统流水号",
    "agentBalance": "1000.00",
    "orderStatuText": "缴费成功",
    "orderStatuInt": "16",
    "finishTime": "2025-06-20 10:30:00",
    "orderPayment": "98.00",
    "errorCode": 1,
    "errorDesc": "操作成功"
}
```

### 签名算法

```php
// 1. 准备业务参数
$busiBody = [
    "action" => "CZ",
    "orderId" => "TEST001",
    "chargeAcct" => "***********",
    "chargeCash" => "100",
    "chargeType" => "0"
];

// 2. 转换为JSON字符串
$jsonStr = json_encode($busiBody, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

// 3. 拼接密钥
$signStr = $jsonStr . $md5Key;

// 4. 生成MD5签名
$sign = md5($signStr);
```

### 错误码说明

| 错误码 | 说明 |
|-------|------|
| 1 | 操作成功 |
| -1 | 账户不存在 |
| -2 | 签名错误 |
| -3 | 账户被暂停 |
| -4 | 无API权限 |
| -7 | IP鉴权失败 |
| -11 | 余额不足 |
| -12 | 订单号重复 |

完整错误码请参考 `config/recharge.php` 配置文件。

## 开发指南

### 目录结构

```
charge/
├── app/
│   ├── Admin/              # Dcat Admin 后台代码
│   │   ├── Controllers/    # 后台控制器
│   │   ├── routes.php      # 后台路由
│   │   └── bootstrap.php   # 后台启动文件
│   ├── Http/
│   │   └── Controllers/    # API控制器
│   ├── Models/             # 数据模型
│   └── Services/           # 业务服务
│       └── Recharge/       # 充值服务封装
├── config/
│   ├── admin.php           # Dcat Admin 配置
│   └── recharge.php        # 充值服务配置
├── database/
│   └── migrations/         # 数据库迁移文件
├── docs/                   # API文档
└── routes/
    └── api.php             # API路由
```

### 扩展开发

1. **添加新的充值渠道**
   - 在 `app/Services/Recharge/` 创建新的服务类
   - 实现 `RechargeServiceInterface` 接口
   - 在配置文件中注册新渠道

2. **自定义统计报表**
   - 在 `app/Admin/Controllers/` 创建报表控制器
   - 使用 Dcat Admin 的 Card 和 Box 组件
   - 集成 Chart.js 实现数据可视化

3. **API接口扩展**
   - 在 `app/Http/Controllers/Api/` 添加控制器
   - 在 `routes/api.php` 注册路由
   - 遵循已有的签名验证机制

## 部署建议

1. **性能优化**
   - 启用 OPcache
   - 配置 Redis 缓存
   - 使用队列处理充值任务

2. **安全加固**
   - 配置 HTTPS
   - 设置防火墙规则
   - 定期更新依赖包
   - 启用访问日志

3. **监控告警**
   - 监控充值成功率
   - 设置余额告警
   - 记录异常日志

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请提交 Issue 或 Pull Request。



尊敬的山东鸿泰网络科技有限公司,以下是您的开户信息,请注意查收:

账号: 18615322483

账号名: 山东鸿泰-无票话费

密钥: 2F64A50755517D72

查单网址: https://csr.sohan.cn/#/home

商务经理: 李雅兰

联系电话: 18777433521

请妥善保管开户信息，如有疑问请及时联系我们!

山东鸿泰网络科技有限公司