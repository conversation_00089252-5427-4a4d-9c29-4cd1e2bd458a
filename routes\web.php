<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TestController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// 测试路由
Route::prefix('test')->group(function () {
    Route::get('product-service', [TestController::class, 'testProductService']);
    Route::get('create-product-order', [TestController::class, 'testCreateProductOrder']);
    Route::get('balance-service', [TestController::class, 'testBalanceService']);
    Route::get('signature-service', [TestController::class, 'testSignatureService']);
    Route::get('voucher-service', [TestController::class, 'testVoucherService']);
    Route::get('full-purchase-flow', [TestController::class, 'testFullPurchaseFlow']);
    Route::get('all-services', [TestController::class, 'testAllServices']);
});

// API测试路由
Route::prefix('api-test')->group(function () {
    Route::get('/', [\App\Http\Controllers\ApiTestController::class, 'index']);
    Route::post('generate-example', [\App\Http\Controllers\ApiTestController::class, 'generateExample']);
    Route::post('test-api', [\App\Http\Controllers\ApiTestController::class, 'testApi']);
    Route::get('documentation', [\App\Http\Controllers\ApiTestController::class, 'documentation']);
});
