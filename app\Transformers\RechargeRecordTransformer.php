<?php

namespace App\Transformers;

use App\Models\RechargeRecord;
use League\Fractal\TransformerAbstract;

class RechargeRecordTransformer extends TransformerAbstract
{
    /**
     * 转换充值记录数据
     */
    public function transform(RechargeRecord $record): array
    {
        return [
            'id' => $record->id,
            'mobile' => $record->mobile,
            'amount' => (float) $record->amount,
            'provider' => $record->provider,
            'status' => $record->status,
            'status_text' => $record->status_text,
            'retry_times' => $record->retry_times,
            'error_code' => $record->error_code,
            'error_msg' => $record->error_msg,
            'submitted_at' => $record->submitted_at?->toDateTimeString(),
            'completed_at' => $record->completed_at?->toDateTimeString(),
            'created_at' => $record->created_at->toDateTimeString(),
        ];
    }
}
