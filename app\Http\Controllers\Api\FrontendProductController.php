<?php

namespace App\Http\Controllers\Api;

use App\Services\ProductService;
use App\Services\OrderService;
use App\Transformers\ProductTransformer;
use App\Transformers\OrderTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FrontendProductController extends BaseApiController
{
    protected $productService;
    protected $orderService;

    public function __construct(ProductService $productService, OrderService $orderService)
    {
        $this->productService = $productService;
        $this->orderService = $orderService;
    }

    /**
     * 获取产品列表
     */
    public function index(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:100',
                'sort' => 'nullable|in:price_asc,price_desc,sales_desc,created_desc',
                'search' => 'nullable|string|max:50',
            ]);

            $products = $this->productService->getAvailableProducts();
            
            // 搜索过滤
            if (!empty($validated['search'])) {
                $search = $validated['search'];
                $products = array_filter($products, function($product) use ($search) {
                    return stripos($product['name'], $search) !== false || 
                           stripos($product['description'], $search) !== false;
                });
            }

            // 应用排序
            $sort = $validated['sort'] ?? 'created_desc';
            $products = $this->sortProducts($products, $sort);

            // 分页处理
            $perPage = $validated['per_page'] ?? 20;
            $page = $validated['page'] ?? 1;
            $total = count($products);
            $offset = ($page - 1) * $perPage;
            $items = array_slice($products, $offset, $perPage);

            return $this->success([
                'data' => $items,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('获取产品列表失败');
        }
    }

    /**
     * 获取产品详情
     */
    public function show($id)
    {
        try {
            $product = $this->productService->getProductDetail($id);
            
            if (!$product) {
                return $this->notFound('产品不存在');
            }

            // 如果用户已登录，检查购买限制
            $purchaseLimit = null;
            if (Auth::check()) {
                $purchaseLimit = $this->productService->checkProductLimits($id, Auth::id());
                $product['purchase_limit'] = $purchaseLimit;
            }

            return $this->success($product);

        } catch (\Exception $e) {
            return $this->serverError('获取产品详情失败');
        }
    }

    /**
     * 获取热门产品
     */
    public function popular(Request $request)
    {
        try {
            $limit = min($request->get('limit', 6), 20);
            $products = $this->productService->getPopularProducts($limit);

            return $this->success($products);

        } catch (\Exception $e) {
            return $this->serverError('获取热门产品失败');
        }
    }

    /**
     * 购买产品
     */
    public function purchase(Request $request, $id)
    {
        try {
            if (!Auth::check()) {
                return $this->unauthorized('请先登录');
            }

            $validated = $this->validateRequest([
                'quantity' => 'required|integer|min:1|max:10',
            ]);

            $user = Auth::user();
            $quantity = $validated['quantity'];

            // 验证产品购买
            $validation = $this->productService->validatePurchase($id, $user->id, $quantity);
            if (!$validation['success']) {
                return $this->error($validation['message'], 'PURCHASE_VALIDATION_FAILED');
            }

            // 创建订单
            $orderData = [
                'user_id' => $user->id,
                'product_id' => $id,
                'quantity' => $quantity,
                'operator' => 'frontend_user',
            ];

            $result = $this->orderService->createProductOrder($orderData);
            
            if (!$result['success']) {
                return $this->error($result['message'], 'ORDER_CREATE_FAILED');
            }

            $order = $result['order'];

            return $this->success([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'amount' => $order->amount,
                'quantity' => $order->quantity,
                'product' => $validation['product'],
                'price_data' => $result['price_data'],
                'status' => $order->status,
                'status_text' => $order->status_text,
                'created_at' => $order->created_at->toDateTimeString(),
            ], '订单创建成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            \Log::error('产品购买失败', [
                'user_id' => Auth::id(),
                'product_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('购买失败');
        }
    }

    /**
     * 检查产品购买限制
     */
    public function checkPurchaseLimit($id)
    {
        try {
            if (!Auth::check()) {
                return $this->unauthorized('请先登录');
            }

            $result = $this->productService->checkProductLimits($id, Auth::id());

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->serverError('检查购买限制失败');
        }
    }

    /**
     * 获取产品分类
     */
    public function categories()
    {
        try {
            // 这里可以从数据库获取产品分类
            // 目前返回模拟数据
            $categories = [
                ['id' => 1, 'name' => '话费券', 'description' => '购买获得话费券'],
                ['id' => 2, 'name' => '流量包', 'description' => '购买获得流量券'],
                ['id' => 3, 'name' => '套餐券', 'description' => '购买获得套餐券'],
            ];

            return $this->success($categories);

        } catch (\Exception $e) {
            return $this->serverError('获取产品分类失败');
        }
    }

    /**
     * 获取产品统计信息
     */
    public function statistics()
    {
        try {
            $stats = [
                'total_products' => count($this->productService->getAvailableProducts()),
                'total_sales' => \App\Models\Order::where('type', \App\Models\Order::TYPE_PRODUCT)
                    ->where('status', \App\Models\Order::STATUS_SUCCESS)
                    ->sum('quantity'),
                'total_amount' => \App\Models\Order::where('type', \App\Models\Order::TYPE_PRODUCT)
                    ->where('status', \App\Models\Order::STATUS_SUCCESS)
                    ->sum('amount'),
                'popular_products' => $this->productService->getPopularProducts(5),
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->serverError('获取产品统计失败');
        }
    }

    /**
     * 产品排序
     */
    private function sortProducts(array $products, string $sort): array
    {
        switch ($sort) {
            case 'price_asc':
                usort($products, fn($a, $b) => $a['price'] <=> $b['price']);
                break;
            case 'price_desc':
                usort($products, fn($a, $b) => $b['price'] <=> $a['price']);
                break;
            case 'sales_desc':
                usort($products, fn($a, $b) => $b['sales_count'] <=> $a['sales_count']);
                break;
            case 'created_desc':
            default:
                usort($products, fn($a, $b) => $b['id'] <=> $a['id']);
                break;
        }

        return $products;
    }
}
