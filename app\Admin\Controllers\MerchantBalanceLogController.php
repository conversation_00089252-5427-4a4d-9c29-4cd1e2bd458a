<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\MerchantBalanceLog;
use App\Models\Merchant;
use App\Models\MerchantBalanceLog as MerchantBalanceLogModel;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Table;

class MerchantBalanceLogController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MerchantBalanceLog(['merchant']), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('merchant.name', '商户名称');
            $grid->column('type', '变动类型')->using(MerchantBalanceLogModel::$typeTexts)
                ->label([
                    'recharge' => 'success',
                    'consume' => 'danger',
                    'refund' => 'warning',
                    'adjust' => 'info',
                    'freeze' => 'secondary',
                    'unfreeze' => 'primary',
                ]);
            $grid->column('amount', '变动金额')->display(function ($amount) {
                $color = $amount > 0 ? 'text-success' : 'text-danger';
                $symbol = $amount > 0 ? '+' : '';
                return '<span class="' . $color . '">' . $symbol . $amount . '</span>';
            });
            $grid->column('balance_before', '变动前余额');
            $grid->column('balance_after', '变动后余额');
            $grid->column('operator', '操作者');
            $grid->column('remark', '备注')->limit(30);
            $grid->column('created_at', '创建时间');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('merchant_id', '商户')->select(
                    Merchant::pluck('name', 'id')->toArray()
                );
                $filter->equal('type', '变动类型')->select(MerchantBalanceLogModel::$typeTexts);
                $filter->like('operator', '操作者');
                $filter->between('amount', '变动金额');
                $filter->between('created_at', '创建时间')->datetime();
            });
            
            $grid->quickSearch(['merchant.name', 'operator', 'remark']);
            
            // 添加统计信息
            $grid->header(function () {
                $today = now()->startOfDay();
                $todayStats = MerchantBalanceLogModel::where('created_at', '>=', $today)
                    ->selectRaw('
                        COUNT(*) as total,
                        SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_in,
                        SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_out
                    ')
                    ->first();
                
                $html = '<div class="row mb-3">';
                $html .= '<div class="col-md-3"><div class="info-box"><div class="info-box-content"><span class="info-box-text">今日记录数</span><span class="info-box-number">' . ($todayStats->total ?? 0) . '</span></div></div></div>';
                $html .= '<div class="col-md-3"><div class="info-box"><div class="info-box-content"><span class="info-box-text">今日收入</span><span class="info-box-number text-success">+' . ($todayStats->total_in ?? 0) . '</span></div></div></div>';
                $html .= '<div class="col-md-3"><div class="info-box"><div class="info-box-content"><span class="info-box-text">今日支出</span><span class="info-box-number text-danger">-' . ($todayStats->total_out ?? 0) . '</span></div></div></div>';
                $html .= '<div class="col-md-3"><div class="info-box"><div class="info-box-content"><span class="info-box-text">今日净额</span><span class="info-box-number">' . (($todayStats->total_in ?? 0) - ($todayStats->total_out ?? 0)) . '</span></div></div></div>';
                $html .= '</div>';
                
                return $html;
            });
            
            // 默认按创建时间倒序
            $grid->model()->orderBy('created_at', 'desc');
            
            // 禁用创建、编辑、删除
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new MerchantBalanceLog(['merchant']), function (Show $show) {
            $show->field('id');
            $show->field('merchant.name', '商户名称');
            $show->field('type', '变动类型')->using(MerchantBalanceLogModel::$typeTexts);
            $show->field('amount', '变动金额');
            $show->field('balance_before', '变动前余额');
            $show->field('balance_after', '变动后余额');
            $show->field('related_type', '关联类型');
            $show->field('related_id', '关联ID');
            $show->field('operator', '操作者');
            $show->field('remark', '备注');
            $show->field('extra_data', '额外数据')->json();
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 余额统计页面
     */
    public function stats(Content $content)
    {
        return $content
            ->title('余额统计')
            ->description('商户余额变动统计分析')
            ->body(function () {
                // 获取所有商户的余额统计
                $merchants = Merchant::with(['balanceLogs' => function ($query) {
                    $query->where('created_at', '>=', now()->subDays(30));
                }])->get();
                
                // 商户余额统计表
                $merchantTable = new Table(['商户', '当前余额', '近30天收入', '近30天支出', '净变动'], []);
                foreach ($merchants as $merchant) {
                    $stats = MerchantBalanceLogModel::getBalanceStats($merchant->id, 30);
                    $totalIn = array_sum(array_column($stats, 'total_in'));
                    $totalOut = array_sum(array_column($stats, 'total_out'));
                    $net = $totalIn - $totalOut;
                    
                    $merchantTable->rows[] = [
                        $merchant->name,
                        $merchant->balance,
                        $totalIn,
                        $totalOut,
                        $net >= 0 ? '+' . $net : $net,
                    ];
                }
                
                // 变动类型统计
                $typeStats = MerchantBalanceLogModel::where('created_at', '>=', now()->subDays(30))
                    ->selectRaw('
                        type,
                        COUNT(*) as count,
                        SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_in,
                        SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_out
                    ')
                    ->groupBy('type')
                    ->get()
                    ->keyBy('type');
                
                $typeTable = new Table(['类型', '记录数', '收入金额', '支出金额'], []);
                foreach (MerchantBalanceLogModel::$typeTexts as $type => $text) {
                    $stat = $typeStats->get($type);
                    $typeTable->rows[] = [
                        $text,
                        $stat->count ?? 0,
                        $stat->total_in ?? 0,
                        $stat->total_out ?? 0,
                    ];
                }
                
                // 每日统计
                $dailyStats = MerchantBalanceLogModel::where('created_at', '>=', now()->subDays(7))
                    ->selectRaw('
                        DATE(created_at) as date,
                        COUNT(*) as count,
                        SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_in,
                        SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_out
                    ')
                    ->groupBy('date')
                    ->orderBy('date', 'desc')
                    ->get();
                
                $dailyTable = new Table(['日期', '记录数', '收入', '支出', '净额'], []);
                foreach ($dailyStats as $daily) {
                    $net = $daily->total_in - $daily->total_out;
                    $dailyTable->rows[] = [
                        $daily->date,
                        $daily->count,
                        $daily->total_in,
                        $daily->total_out,
                        $net >= 0 ? '+' . $net : $net,
                    ];
                }
                
                return [
                    new Card('商户余额统计（近30天）', $merchantTable),
                    new Card('变动类型统计（近30天）', $typeTable),
                    new Card('每日统计（近7天）', $dailyTable),
                ];
            });
    }
}
