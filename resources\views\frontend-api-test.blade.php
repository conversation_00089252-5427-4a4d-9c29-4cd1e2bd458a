<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试工具</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .response-success {
            border-left: 4px solid #28a745;
        }
        .response-error {
            border-left: 4px solid #dc3545;
        }
        .api-section {
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }
        .token-display {
            background-color: #e3f2fd;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
            font-size: 0.875rem;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">前端API测试工具</h1>
        
        <!-- 用户认证区域 -->
        <div class="api-section">
            <h3>用户认证</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>用户注册</h5>
                    <form id="registerForm">
                        <div class="mb-2">
                            <input type="text" class="form-control" name="name" placeholder="姓名" required>
                        </div>
                        <div class="mb-2">
                            <input type="email" class="form-control" name="email" placeholder="邮箱" required>
                        </div>
                        <div class="mb-2">
                            <input type="password" class="form-control" name="password" placeholder="密码" required>
                        </div>
                        <div class="mb-2">
                            <input type="password" class="form-control" name="password_confirmation" placeholder="确认密码" required>
                        </div>
                        <div class="mb-2">
                            <input type="text" class="form-control" name="mobile" placeholder="手机号（可选）">
                        </div>
                        <button type="submit" class="btn btn-primary">注册</button>
                    </form>
                </div>
                <div class="col-md-6">
                    <h5>用户登录</h5>
                    <form id="loginForm">
                        <div class="mb-2">
                            <input type="email" class="form-control" name="email" placeholder="邮箱" required>
                        </div>
                        <div class="mb-2">
                            <input type="password" class="form-control" name="password" placeholder="密码" required>
                        </div>
                        <button type="submit" class="btn btn-success">登录</button>
                        <button type="button" class="btn btn-info" onclick="getUserInfo()">获取用户信息</button>
                        <button type="button" class="btn btn-warning" onclick="logout()">登出</button>
                    </form>
                </div>
            </div>
            <div class="mt-3">
                <label>当前Token:</label>
                <div class="token-display" id="currentToken">未登录</div>
            </div>
        </div>

        <!-- 产品相关API -->
        <div class="api-section">
            <h3>产品相关API</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testApi('/api/v1/frontend/products', 'GET')">获取产品列表</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testApi('/api/v1/frontend/products/popular', 'GET')">获取热门产品</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testApi('/api/v1/frontend/products/categories', 'GET')">获取产品分类</button>
                </div>
                <div class="col-md-4">
                    <div class="mb-2">
                        <input type="number" id="productId" class="form-control" placeholder="产品ID" value="1">
                    </div>
                    <button class="btn btn-outline-info w-100 mb-2" onclick="getProductDetail()">获取产品详情</button>
                    <button class="btn btn-outline-success w-100 mb-2" onclick="purchaseProduct()">购买产品</button>
                </div>
                <div class="col-md-4">
                    <div class="mb-2">
                        <input type="number" id="quantity" class="form-control" placeholder="购买数量" value="1">
                    </div>
                    <button class="btn btn-outline-warning w-100 mb-2" onclick="checkPurchaseLimit()">检查购买限制</button>
                </div>
            </div>
        </div>

        <!-- 充值相关API -->
        <div class="api-section">
            <h3>充值相关API</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testApi('/api/v1/frontend/recharge/config', 'GET')">获取充值配置</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="getUserVouchers()">获取用户券</button>
                </div>
                <div class="col-md-4">
                    <div class="mb-2">
                        <input type="text" id="mobile" class="form-control" placeholder="手机号" value="13800138000">
                    </div>
                    <button class="btn btn-outline-info w-100 mb-2" onclick="getCarrierInfo()">查询运营商</button>
                </div>
                <div class="col-md-4">
                    <div class="mb-2">
                        <input type="number" id="amount" class="form-control" placeholder="充值金额" value="10">
                    </div>
                    <button class="btn btn-outline-success w-100 mb-2" onclick="createRechargeOrder()">创建充值订单</button>
                </div>
            </div>
        </div>

        <!-- 用户相关API -->
        <div class="api-section">
            <h3>用户相关API（需要登录）</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testAuthApi('/api/v1/user/profile', 'GET')">获取用户资料</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testAuthApi('/api/v1/user/statistics', 'GET')">获取用户统计</button>
                    <button class="btn btn-outline-primary w-100 mb-2" onclick="testAuthApi('/api/v1/user/vouchers', 'GET')">获取用户券</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testAuthApi('/api/v1/user/orders', 'GET')">获取用户订单</button>
                    <button class="btn btn-outline-info w-100 mb-2" onclick="testAuthApi('/api/v1/user/voucher-stats', 'GET')">获取券统计</button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-outline-success w-100 mb-2" onclick="calculateVouchers()">计算券使用</button>
                </div>
            </div>
        </div>

        <!-- 响应显示区域 -->
        <div class="api-section">
            <h3>API响应</h3>
            <div class="row">
                <div class="col-md-6">
                    <label>请求信息:</label>
                    <div class="code-block" id="requestInfo">等待请求...</div>
                </div>
                <div class="col-md-6">
                    <label>响应结果:</label>
                    <div class="code-block" id="responseResult">等待响应...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let currentToken = null;

        // 设置CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // 用户注册
        $('#registerForm').on('submit', function(e) {
            e.preventDefault();
            const formData = $(this).serialize();
            
            $.post('/api/v1/auth/register', formData)
                .done(function(response) {
                    showResponse('POST /api/v1/auth/register', response);
                    if (response.success) {
                        currentToken = response.data.token;
                        updateTokenDisplay();
                        alert('注册成功！');
                    }
                })
                .fail(function(xhr) {
                    showResponse('POST /api/v1/auth/register', xhr.responseJSON);
                });
        });

        // 用户登录
        $('#loginForm').on('submit', function(e) {
            e.preventDefault();
            const formData = $(this).serialize();
            
            $.post('/api/v1/auth/login', formData)
                .done(function(response) {
                    showResponse('POST /api/v1/auth/login', response);
                    if (response.success) {
                        currentToken = response.data.token;
                        updateTokenDisplay();
                        alert('登录成功！');
                    }
                })
                .fail(function(xhr) {
                    showResponse('POST /api/v1/auth/login', xhr.responseJSON);
                });
        });

        // 获取用户信息
        function getUserInfo() {
            testAuthApi('/api/v1/auth/me', 'GET');
        }

        // 用户登出
        function logout() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            $.ajax({
                url: '/api/v1/auth/logout',
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + currentToken
                }
            }).done(function(response) {
                showResponse('POST /api/v1/auth/logout', response);
                currentToken = null;
                updateTokenDisplay();
                alert('登出成功！');
            }).fail(function(xhr) {
                showResponse('POST /api/v1/auth/logout', xhr.responseJSON);
            });
        }

        // 更新Token显示
        function updateTokenDisplay() {
            $('#currentToken').text(currentToken || '未登录');
        }

        // 测试公开API
        function testApi(url, method, data = null) {
            const requestInfo = `${method} ${url}` + (data ? `\nData: ${JSON.stringify(data)}` : '');
            $('#requestInfo').text(requestInfo);

            $.ajax({
                url: url,
                method: method,
                data: data,
                contentType: 'application/json'
            }).done(function(response) {
                showResponse(requestInfo, response);
            }).fail(function(xhr) {
                showResponse(requestInfo, xhr.responseJSON);
            });
        }

        // 测试需要认证的API
        function testAuthApi(url, method, data = null) {
            if (!currentToken) {
                alert('请先登录');
                return;
            }

            const requestInfo = `${method} ${url}` + (data ? `\nData: ${JSON.stringify(data)}` : '');
            $('#requestInfo').text(requestInfo);

            $.ajax({
                url: url,
                method: method,
                data: data,
                headers: {
                    'Authorization': 'Bearer ' + currentToken
                },
                contentType: 'application/json'
            }).done(function(response) {
                showResponse(requestInfo, response);
            }).fail(function(xhr) {
                showResponse(requestInfo, xhr.responseJSON);
            });
        }

        // 显示响应结果
        function showResponse(request, response) {
            $('#requestInfo').text(request);
            $('#responseResult').text(JSON.stringify(response, null, 2));
            
            if (response && response.success) {
                $('#responseResult').removeClass('response-error').addClass('response-success');
            } else {
                $('#responseResult').removeClass('response-success').addClass('response-error');
            }
        }

        // 具体的API测试函数
        function getProductDetail() {
            const productId = $('#productId').val();
            testApi(`/api/v1/frontend/products/${productId}`, 'GET');
        }

        function purchaseProduct() {
            const productId = $('#productId').val();
            const quantity = $('#quantity').val();
            testAuthApi(`/api/v1/frontend/products/${productId}/purchase`, 'POST', {
                quantity: parseInt(quantity)
            });
        }

        function checkPurchaseLimit() {
            const productId = $('#productId').val();
            testAuthApi(`/api/v1/frontend/products/${productId}/check-limit`, 'GET');
        }

        function getCarrierInfo() {
            const mobile = $('#mobile').val();
            testApi('/api/v1/frontend/recharge/carrier-info', 'POST', {
                mobile: mobile
            });
        }

        function createRechargeOrder() {
            const mobile = $('#mobile').val();
            const amount = $('#amount').val();
            testApi('/api/v1/frontend/recharge/create-order', 'POST', {
                mobile: mobile,
                amount: parseFloat(amount)
            });
        }

        function getUserVouchers() {
            testAuthApi('/api/v1/frontend/recharge/user-vouchers', 'GET');
        }

        function calculateVouchers() {
            const amount = $('#amount').val();
            testAuthApi('/api/v1/user/calculate-vouchers', 'POST', {
                amount: parseFloat(amount)
            });
        }
    </script>
</body>
</html>
