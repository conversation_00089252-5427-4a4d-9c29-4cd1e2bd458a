<?php

namespace App\Http\Controllers\Api;

use App\Models\Order;
use App\Transformers\OrderTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class OrderController extends BaseApiController
{
    /**
     * 查询订单列表
     */
    public function index(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'page' => ['nullable', 'integer', 'min:1'],
                'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
                'type' => ['nullable', 'string', 'in:product,recharge'],
                'status' => ['nullable', 'integer', 'in:0,1,2,3,4,5'],
                'mobile' => ['nullable', 'regex:/^1[3-9]\d{9}$/'],
                'start_date' => ['nullable', 'date'],
                'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
                'merchant_order_no' => ['nullable', 'string'],
            ]);

            $merchant = $this->getMerchant();
            $perPage = $validated['per_page'] ?? 20;

            // 构建查询
            $query = Order::where('merchant_id', $merchant->id)
                ->with(['product', 'rechargeRecord', 'payments']);

            // 应用筛选条件
            if (isset($validated['type'])) {
                $query->where('type', $validated['type']);
            }

            if (isset($validated['status'])) {
                $query->where('status', $validated['status']);
            }

            if (isset($validated['mobile'])) {
                $query->where('mobile', $validated['mobile']);
            }

            if (isset($validated['merchant_order_no'])) {
                $query->where('merchant_order_no', 'like', '%' . $validated['merchant_order_no'] . '%');
            }

            if (isset($validated['start_date'])) {
                $query->whereDate('created_at', '>=', $validated['start_date']);
            }

            if (isset($validated['end_date'])) {
                $query->whereDate('created_at', '<=', $validated['end_date']);
            }

            // 分页查询
            $orders = $query->orderBy('created_at', 'desc')->paginate($perPage);

            // 记录API调用
            $this->logApiCall('get_orders', $validated, [
                'total' => $orders->total(),
                'count' => $orders->count(),
            ]);

            return $this->paginated($orders, new OrderTransformer(), ['product', 'recharge_record', 'payments']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('查询订单列表失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'params' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('查询订单列表失败');
        }
    }

    /**
     * 查询单个订单详情
     */
    public function show(Request $request, $orderNo)
    {
        try {
            $merchant = $this->getMerchant();

            $order = Order::where('merchant_id', $merchant->id)
                ->where(function ($query) use ($orderNo) {
                    $query->where('order_no', $orderNo)
                        ->orWhere('merchant_order_no', $orderNo);
                })
                ->with(['product', 'rechargeRecord', 'payments', 'logs'])
                ->first();

            if (!$order) {
                return $this->notFound('订单不存在');
            }

            // 记录API调用
            $this->logApiCall('get_order_detail', ['order_no' => $orderNo], [
                'order_id' => $order->id,
                'status' => $order->status,
            ]);

            return $this->item($order, new OrderTransformer(), ['product', 'recharge_record', 'payments']);

        } catch (\Exception $e) {
            Log::error('查询订单详情失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('查询订单详情失败');
        }
    }

    /**
     * 获取订单统计信息
     */
    public function statistics(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'start_date' => ['nullable', 'date'],
                'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
            ]);

            $merchant = $this->getMerchant();

            // 构建查询
            $query = Order::where('merchant_id', $merchant->id);

            if (isset($validated['start_date'])) {
                $query->whereDate('created_at', '>=', $validated['start_date']);
            }

            if (isset($validated['end_date'])) {
                $query->whereDate('created_at', '<=', $validated['end_date']);
            }

            // 统计数据
            $stats = $query->selectRaw('
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as success_orders,
                SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as failed_orders,
                SUM(CASE WHEN type = "recharge" THEN amount ELSE 0 END) as total_recharge_amount,
                SUM(CASE WHEN type = "product" THEN amount ELSE 0 END) as total_product_amount,
                AVG(CASE WHEN status = 3 THEN amount ELSE NULL END) as avg_success_amount
            ')->first();

            $result = [
                'total_orders' => $stats->total_orders,
                'success_orders' => $stats->success_orders,
                'failed_orders' => $stats->failed_orders,
                'success_rate' => $stats->total_orders > 0 ? round(($stats->success_orders / $stats->total_orders) * 100, 2) : 0,
                'total_recharge_amount' => (float) $stats->total_recharge_amount,
                'total_product_amount' => (float) $stats->total_product_amount,
                'avg_success_amount' => (float) $stats->avg_success_amount,
            ];

            // 记录API调用
            $this->logApiCall('get_order_statistics', $validated, $result);

            return $this->success($result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('获取订单统计失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'params' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('获取订单统计失败');
        }
    }
}
