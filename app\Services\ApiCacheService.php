<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ApiCacheService
{
    protected $defaultTtl = 300; // 5分钟
    protected $prefix = 'api_cache:';

    /**
     * 缓存配置
     */
    protected $cacheConfig = [
        'products' => [
            'ttl' => 600, // 10分钟
            'tags' => ['products'],
        ],
        'product_detail' => [
            'ttl' => 300, // 5分钟
            'tags' => ['products'],
        ],
        'system_config' => [
            'ttl' => 3600, // 1小时
            'tags' => ['config'],
        ],
        'supported_amounts' => [
            'ttl' => 1800, // 30分钟
            'tags' => ['config'],
        ],
        'supported_carriers' => [
            'ttl' => 3600, // 1小时
            'tags' => ['config'],
        ],
        'user_vouchers' => [
            'ttl' => 60, // 1分钟
            'tags' => ['vouchers'],
        ],
        'user_orders' => [
            'ttl' => 120, // 2分钟
            'tags' => ['orders'],
        ],
        'merchant_balance' => [
            'ttl' => 30, // 30秒
            'tags' => ['balance'],
        ],
    ];

    /**
     * 获取缓存数据
     */
    public function get(string $key, $default = null)
    {
        try {
            return Cache::get($this->prefix . $key, $default);
        } catch (\Exception $e) {
            Log::warning('Cache get failed: ' . $e->getMessage(), ['key' => $key]);
            return $default;
        }
    }

    /**
     * 设置缓存数据
     */
    public function put(string $key, $value, ?int $ttl = null): bool
    {
        try {
            $ttl = $ttl ?? $this->defaultTtl;
            return Cache::put($this->prefix . $key, $value, $ttl);
        } catch (\Exception $e) {
            Log::warning('Cache put failed: ' . $e->getMessage(), ['key' => $key]);
            return false;
        }
    }

    /**
     * 记住缓存（如果不存在则执行回调并缓存结果）
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        try {
            $ttl = $ttl ?? $this->defaultTtl;
            return Cache::remember($this->prefix . $key, $ttl, $callback);
        } catch (\Exception $e) {
            Log::warning('Cache remember failed: ' . $e->getMessage(), ['key' => $key]);
            return $callback();
        }
    }

    /**
     * 删除缓存
     */
    public function forget(string $key): bool
    {
        try {
            return Cache::forget($this->prefix . $key);
        } catch (\Exception $e) {
            Log::warning('Cache forget failed: ' . $e->getMessage(), ['key' => $key]);
            return false;
        }
    }

    /**
     * 清除标签相关的缓存
     */
    public function flushByTags(array $tags): bool
    {
        try {
            if (method_exists(Cache::store(), 'tags')) {
                Cache::tags($tags)->flush();
                return true;
            }
            
            // 如果不支持标签，则清除所有相关缓存
            $this->flushByPattern($tags);
            return true;
        } catch (\Exception $e) {
            Log::warning('Cache flush by tags failed: ' . $e->getMessage(), ['tags' => $tags]);
            return false;
        }
    }

    /**
     * 根据模式清除缓存
     */
    public function flushByPattern(array $patterns): void
    {
        foreach ($patterns as $pattern) {
            $keys = $this->getKeysByPattern($pattern);
            foreach ($keys as $key) {
                $this->forget(str_replace($this->prefix, '', $key));
            }
        }
    }

    /**
     * 获取匹配模式的键
     */
    protected function getKeysByPattern(string $pattern): array
    {
        // 这里需要根据具体的缓存驱动实现
        // Redis可以使用KEYS命令，但生产环境建议使用SCAN
        return [];
    }

    /**
     * 缓存产品列表
     */
    public function cacheProducts(array $params, $data): void
    {
        $key = 'products:' . md5(serialize($params));
        $config = $this->cacheConfig['products'];
        
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags($config['tags'])->put($this->prefix . $key, $data, $config['ttl']);
        } else {
            $this->put($key, $data, $config['ttl']);
        }
    }

    /**
     * 获取缓存的产品列表
     */
    public function getCachedProducts(array $params)
    {
        $key = 'products:' . md5(serialize($params));
        return $this->get($key);
    }

    /**
     * 缓存产品详情
     */
    public function cacheProductDetail(int $productId, $data): void
    {
        $key = "product_detail:{$productId}";
        $config = $this->cacheConfig['product_detail'];
        
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags($config['tags'])->put($this->prefix . $key, $data, $config['ttl']);
        } else {
            $this->put($key, $data, $config['ttl']);
        }
    }

    /**
     * 获取缓存的产品详情
     */
    public function getCachedProductDetail(int $productId)
    {
        $key = "product_detail:{$productId}";
        return $this->get($key);
    }

    /**
     * 缓存系统配置
     */
    public function cacheSystemConfig(string $configType, $data): void
    {
        $key = "system_config:{$configType}";
        $config = $this->cacheConfig['system_config'];
        
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags($config['tags'])->put($this->prefix . $key, $data, $config['ttl']);
        } else {
            $this->put($key, $data, $config['ttl']);
        }
    }

    /**
     * 获取缓存的系统配置
     */
    public function getCachedSystemConfig(string $configType)
    {
        $key = "system_config:{$configType}";
        return $this->get($key);
    }

    /**
     * 缓存用户券列表
     */
    public function cacheUserVouchers(int $userId, array $params, $data): void
    {
        $key = "user_vouchers:{$userId}:" . md5(serialize($params));
        $config = $this->cacheConfig['user_vouchers'];
        
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags($config['tags'])->put($this->prefix . $key, $data, $config['ttl']);
        } else {
            $this->put($key, $data, $config['ttl']);
        }
    }

    /**
     * 获取缓存的用户券列表
     */
    public function getCachedUserVouchers(int $userId, array $params)
    {
        $key = "user_vouchers:{$userId}:" . md5(serialize($params));
        return $this->get($key);
    }

    /**
     * 缓存用户订单列表
     */
    public function cacheUserOrders(int $userId, array $params, $data): void
    {
        $key = "user_orders:{$userId}:" . md5(serialize($params));
        $config = $this->cacheConfig['user_orders'];
        
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags($config['tags'])->put($this->prefix . $key, $data, $config['ttl']);
        } else {
            $this->put($key, $data, $config['ttl']);
        }
    }

    /**
     * 获取缓存的用户订单列表
     */
    public function getCachedUserOrders(int $userId, array $params)
    {
        $key = "user_orders:{$userId}:" . md5(serialize($params));
        return $this->get($key);
    }

    /**
     * 缓存商户余额
     */
    public function cacheMerchantBalance(int $merchantId, $data): void
    {
        $key = "merchant_balance:{$merchantId}";
        $config = $this->cacheConfig['merchant_balance'];
        
        if (method_exists(Cache::store(), 'tags')) {
            Cache::tags($config['tags'])->put($this->prefix . $key, $data, $config['ttl']);
        } else {
            $this->put($key, $data, $config['ttl']);
        }
    }

    /**
     * 获取缓存的商户余额
     */
    public function getCachedMerchantBalance(int $merchantId)
    {
        $key = "merchant_balance:{$merchantId}";
        return $this->get($key);
    }

    /**
     * 清除用户相关缓存
     */
    public function clearUserCache(int $userId): void
    {
        $patterns = [
            "user_vouchers:{$userId}:*",
            "user_orders:{$userId}:*",
        ];
        
        foreach ($patterns as $pattern) {
            $this->flushByPattern([$pattern]);
        }
    }

    /**
     * 清除商户相关缓存
     */
    public function clearMerchantCache(int $merchantId): void
    {
        $this->forget("merchant_balance:{$merchantId}");
    }

    /**
     * 清除产品相关缓存
     */
    public function clearProductCache(?int $productId = null): void
    {
        if ($productId) {
            $this->forget("product_detail:{$productId}");
        }
        
        $this->flushByTags(['products']);
    }

    /**
     * 清除配置相关缓存
     */
    public function clearConfigCache(): void
    {
        $this->flushByTags(['config']);
    }

    /**
     * 获取缓存统计信息
     */
    public function getStats(): array
    {
        try {
            // 这里可以根据不同的缓存驱动获取统计信息
            return [
                'driver' => config('cache.default'),
                'prefix' => $this->prefix,
                'default_ttl' => $this->defaultTtl,
                'config_count' => count($this->cacheConfig),
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to get cache stats: ' . $e->getMessage());
            return [];
        }
    }
}
