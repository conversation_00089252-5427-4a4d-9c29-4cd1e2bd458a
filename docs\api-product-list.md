# 产品列表查询接口

## 接口信息
- **请求方式**: POST
- **请求路径**: /api
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sign | string | 是 | 签名 |
| agentAccount | string | 是 | 账号 |
| busiBody | object | 是 | 业务参数体 |
| └─ action | string | 是 | 指令，默认值: CHECK_SPU |
| └─ chargeCash | integer/null | 否 | 面值 |
| └─ chargeType | integer/null | 否 | 充值类型 |
| └─ ispName | string/null | 否 | 运营商名称 |

## 请求示例

```json
{
  "sign": "0712dcadafe594fe543c66b50ae7ed58",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "CHECK_SPU"
  }
}
```

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| action | string | 指令 |
| agentAccount | string | 账号 |
| errorCode | integer | 错误码 |
| errorDesc | string | 错误描述 |
| dataset | array | 产品列表数据 |
| └─ spuName | string | 商品名称 |
| └─ productSn | integer | 产品编码 |
| └─ province | string | 省份 |
| └─ ispName | string | 运营商 |
| └─ discount | number | 折扣(%) |
| └─ amount | integer | 面值金额 |

## 响应示例

```json
{
  "action": "CHECK_SPU",
  "agentAccount": "api_test",
  "errorCode": 1,
  "errorDesc": "操作成功",
  "dataset": [
    {
      "productSn": 7001001,
      "province": "广东",
      "ispName": "移动",
      "spuName": "广东移动100元",
      "discount": 99.500,
      "amount": 100.000
    },
    {
      "productSn": 7001002,
      "province": "广东",
      "ispName": "移动",
      "spuName": "广东移动50元",
      "discount": 99.000,
      "amount": 50.000
    },
    {
      "productSn": 920288,
      "province": "广东",
      "ispName": "电信",
      "spuName": "广东电信100元",
      "discount": 99.800,
      "amount": 100.000
    },
    {
      "productSn": 0,
      "province": "广东",
      "ispName": "移动",
      "spuName": "广东移动30元",
      "discount": 99.450,
      "amount": 30.000
    }
  ]
}
```

## CURL请求示例

```bash
curl --location --request POST '/api' \
--header 'Content-Type: application/json' \
--data-raw '{
  "sign": "0712dcadafe594fe543c66b50ae7ed58",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "CHECK_SPU"
  }
}'
```