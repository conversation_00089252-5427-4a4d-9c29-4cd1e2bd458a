<?php

namespace App\Services\Recharge\Requests;

/**
 * 充值请求数据传输对象
 * 
 * 封装话费充值请求的所有参数
 */
class RechargeRequest
{
    /**
     * @var string 商户订单号（必须唯一）
     */
    public string $orderId;

    /**
     * @var string 充值手机号码
     */
    public string $chargeAcct;

    /**
     * @var string 充值金额（单位：元）
     */
    public string $chargeCash;

    /**
     * @var string 充值类型（默认："0"）
     */
    public string $chargeType = '0';

    /**
     * @var string|null 运营商名称（可选，需要UrlEncode编码）
     */
    public ?string $ispName = null;

    /**
     * @var string|null 省份（可选）
     */
    public ?string $province = null;

    /**
     * @var string|null 回调地址（可选，需要UrlEncode编码）
     */
    public ?string $retUrl = null;

    /**
     * 构造函数
     * 
     * @param string $orderId 商户订单号
     * @param string $chargeAcct 充值手机号
     * @param string $chargeCash 充值金额
     */
    public function __construct(string $orderId, string $chargeAcct, string $chargeCash)
    {
        $this->orderId = $orderId;
        $this->chargeAcct = $chargeAcct;
        $this->chargeCash = $chargeCash;
    }

    /**
     * 设置充值类型
     * 
     * @param string $chargeType 充值类型
     * @return self
     */
    public function setChargeType(string $chargeType): self
    {
        $this->chargeType = $chargeType;
        return $this;
    }

    /**
     * 设置运营商名称（自动进行URL编码）
     * 
     * @param string $ispName 运营商名称
     * @return self
     */
    public function setIspName(string $ispName): self
    {
        $this->ispName = urlencode($ispName);
        return $this;
    }

    /**
     * 设置省份
     * 
     * @param string $province 省份
     * @return self
     */
    public function setProvince(string $province): self
    {
        $this->province = $province;
        return $this;
    }

    /**
     * 设置回调地址（自动进行URL编码）
     * 
     * @param string $retUrl 回调地址
     * @return self
     */
    public function setRetUrl(string $retUrl): self
    {
        $this->retUrl = urlencode($retUrl);
        return $this;
    }

    /**
     * 转换为业务参数数组
     * 
     * @return array 业务参数数组
     */
    public function toBusiBody(): array
    {
        $busiBody = [
            'action' => 'CZ',
            'orderId' => $this->orderId,
            'chargeAcct' => $this->chargeAcct,
            'chargeCash' => $this->chargeCash,
            'chargeType' => $this->chargeType,
        ];

        // 添加可选参数
        if ($this->ispName !== null) {
            $busiBody['ispName'] = $this->ispName;
        }

        if ($this->province !== null) {
            $busiBody['province'] = $this->province;
        }

        if ($this->retUrl !== null) {
            $busiBody['retUrl'] = $this->retUrl;
        }

        return $busiBody;
    }
}