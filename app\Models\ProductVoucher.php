<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductVoucher extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'voucher_value',
        'voucher_count',
    ];

    // 支持的券面值
    const VALID_VALUES = [5, 10, 20, 30, 50];

    /**
     * 所属产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 获取券总价值
     */
    public function getTotalValueAttribute()
    {
        return $this->voucher_value * $this->voucher_count;
    }
}