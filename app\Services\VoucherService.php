<?php

namespace App\Services;

use App\Models\UserVoucher;
use App\Models\Product;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class VoucherService
{
    /**
     * 为用户发放券（购买产品后）
     */
    public function issueVouchersForOrder(Order $order): bool
    {
        try {
            DB::beginTransaction();

            $product = $order->product;
            if (!$product) {
                throw new \Exception('订单关联的产品不存在');
            }

            // 获取产品的券配置
            $voucherConfigs = $product->vouchers;
            if ($voucherConfigs->isEmpty()) {
                Log::warning('产品没有配置券', ['product_id' => $product->id]);
                DB::commit();
                return true;
            }

            // 计算券的有效期
            $validUntil = $this->calculateVoucherExpiry($product);

            // 为每种券面值发放对应数量的券
            foreach ($voucherConfigs as $config) {
                for ($i = 0; $i < $config->voucher_quantity; $i++) {
                    UserVoucher::create([
                        'user_id' => $order->user_id,
                        'order_id' => $order->id,
                        'voucher_value' => $config->voucher_value,
                        'status' => UserVoucher::STATUS_UNUSED,
                        'valid_until' => $validUntil,
                        'source' => 'purchase',
                        'source_id' => $order->id,
                    ]);
                }
            }

            DB::commit();

            Log::info('券发放成功', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'product_id' => $product->id,
                'voucher_configs' => $voucherConfigs->toArray(),
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('券发放失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 获取用户可用的券
     */
    public function getUserAvailableVouchers($userId, $maxValue = null): array
    {
        $query = UserVoucher::where('user_id', $userId)
            ->where('status', UserVoucher::STATUS_UNUSED)
            ->where('valid_until', '>', now());

        if ($maxValue) {
            $query->where('voucher_value', '<=', $maxValue);
        }

        return $query->orderBy('voucher_value', 'desc')
            ->orderBy('valid_until', 'asc')
            ->get()
            ->map(function ($voucher) {
                return [
                    'id' => $voucher->id,
                    'value' => $voucher->voucher_value,
                    'valid_until' => $voucher->valid_until->format('Y-m-d H:i:s'),
                    'source' => $voucher->source,
                    'days_remaining' => $voucher->valid_until->diffInDays(now()),
                ];
            })
            ->toArray();
    }

    /**
     * 计算最优券使用方案
     */
    public function calculateOptimalVoucherUsage($userId, $targetAmount): array
    {
        $availableVouchers = UserVoucher::where('user_id', $userId)
            ->where('status', UserVoucher::STATUS_UNUSED)
            ->where('valid_until', '>', now())
            ->where('voucher_value', '<=', $targetAmount)
            ->orderBy('voucher_value', 'desc')
            ->get();

        if ($availableVouchers->isEmpty()) {
            return [
                'vouchers' => [],
                'total_deduction' => 0,
                'remaining_amount' => $targetAmount,
            ];
        }

        // 贪心算法：优先使用大面值券
        $selectedVouchers = [];
        $totalDeduction = 0;
        $remainingAmount = $targetAmount;

        foreach ($availableVouchers as $voucher) {
            if ($voucher->voucher_value <= $remainingAmount) {
                $selectedVouchers[] = [
                    'id' => $voucher->id,
                    'value' => $voucher->voucher_value,
                ];
                $totalDeduction += $voucher->voucher_value;
                $remainingAmount -= $voucher->voucher_value;
                
                if ($remainingAmount == 0) {
                    break;
                }
            }
        }

        return [
            'vouchers' => $selectedVouchers,
            'total_deduction' => $totalDeduction,
            'remaining_amount' => $remainingAmount,
        ];
    }

    /**
     * 使用券进行抵扣
     */
    public function useVouchers(array $voucherIds, $userId, $orderId = null): array
    {
        try {
            DB::beginTransaction();

            $vouchers = UserVoucher::whereIn('id', $voucherIds)
                ->where('user_id', $userId)
                ->where('status', UserVoucher::STATUS_UNUSED)
                ->where('valid_until', '>', now())
                ->get();

            if ($vouchers->count() !== count($voucherIds)) {
                throw new \Exception('部分券不可用或已过期');
            }

            $totalDeduction = 0;
            $usedVouchers = [];

            foreach ($vouchers as $voucher) {
                $voucher->update([
                    'status' => UserVoucher::STATUS_USED,
                    'used_at' => now(),
                    'used_order_id' => $orderId,
                ]);

                $totalDeduction += $voucher->voucher_value;
                $usedVouchers[] = [
                    'id' => $voucher->id,
                    'value' => $voucher->voucher_value,
                ];
            }

            DB::commit();

            Log::info('券使用成功', [
                'user_id' => $userId,
                'voucher_ids' => $voucherIds,
                'total_deduction' => $totalDeduction,
                'order_id' => $orderId,
            ]);

            return [
                'success' => true,
                'used_vouchers' => $usedVouchers,
                'total_deduction' => $totalDeduction,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('券使用失败', [
                'user_id' => $userId,
                'voucher_ids' => $voucherIds,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * 获取用户券统计
     */
    public function getUserVoucherStats($userId): array
    {
        $stats = UserVoucher::where('user_id', $userId)
            ->selectRaw('
                status,
                COUNT(*) as count,
                SUM(voucher_value) as total_value
            ')
            ->groupBy('status')
            ->get()
            ->keyBy('status');

        return [
            'unused' => [
                'count' => $stats->get(UserVoucher::STATUS_UNUSED)->count ?? 0,
                'total_value' => $stats->get(UserVoucher::STATUS_UNUSED)->total_value ?? 0,
            ],
            'used' => [
                'count' => $stats->get(UserVoucher::STATUS_USED)->count ?? 0,
                'total_value' => $stats->get(UserVoucher::STATUS_USED)->total_value ?? 0,
            ],
            'expired' => [
                'count' => $stats->get(UserVoucher::STATUS_EXPIRED)->count ?? 0,
                'total_value' => $stats->get(UserVoucher::STATUS_EXPIRED)->total_value ?? 0,
            ],
        ];
    }

    /**
     * 获取用户券列表（分页）
     */
    public function getUserVouchers($userId, $status = null, $page = 1, $perPage = 20): array
    {
        $query = UserVoucher::where('user_id', $userId);

        if ($status !== null) {
            $query->where('status', $status);
        }

        $total = $query->count();
        $vouchers = $query->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->map(function ($voucher) {
                return [
                    'id' => $voucher->id,
                    'value' => $voucher->voucher_value,
                    'status' => $voucher->status,
                    'status_text' => $voucher->status_text,
                    'valid_until' => $voucher->valid_until->format('Y-m-d H:i:s'),
                    'used_at' => $voucher->used_at?->format('Y-m-d H:i:s'),
                    'source' => $voucher->source,
                    'created_at' => $voucher->created_at->format('Y-m-d H:i:s'),
                ];
            });

        return [
            'data' => $vouchers,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'last_page' => ceil($total / $perPage),
        ];
    }

    /**
     * 验证券是否可用
     */
    public function validateVoucher($voucherId, $userId): array
    {
        $voucher = UserVoucher::find($voucherId);

        if (!$voucher) {
            return ['valid' => false, 'message' => '券不存在'];
        }

        if ($voucher->user_id !== $userId) {
            return ['valid' => false, 'message' => '券不属于当前用户'];
        }

        if ($voucher->status !== UserVoucher::STATUS_UNUSED) {
            return ['valid' => false, 'message' => '券已使用或已过期'];
        }

        if ($voucher->valid_until <= now()) {
            return ['valid' => false, 'message' => '券已过期'];
        }

        return ['valid' => true, 'voucher' => $voucher];
    }

    /**
     * 计算券的有效期
     */
    private function calculateVoucherExpiry(Product $product): \Carbon\Carbon
    {
        // 检查产品是否有有效期规则
        $validityRule = $product->rules()
            ->where('rule_type', 'valid_period')
            ->first();

        if ($validityRule) {
            $days = $validityRule->rule_value['days'] ?? 365;
            return now()->addDays($days);
        }

        // 默认有效期1年
        return now()->addYear();
    }

    /**
     * 处理过期券（可以通过定时任务调用）
     */
    public function processExpiredVouchers(): int
    {
        $expiredCount = UserVoucher::where('status', UserVoucher::STATUS_UNUSED)
            ->where('valid_until', '<=', now())
            ->update(['status' => UserVoucher::STATUS_EXPIRED]);

        Log::info('处理过期券', ['expired_count' => $expiredCount]);

        return $expiredCount;
    }
}
