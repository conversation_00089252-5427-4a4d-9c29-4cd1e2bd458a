<?php

namespace App\Services;

use App\Models\Merchant;
use App\Models\MerchantBalanceLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BalanceService
{
    /**
     * 获取商户余额信息
     */
    public function getMerchantBalance($merchantId): array
    {
        $merchant = Merchant::find($merchantId);
        
        if (!$merchant) {
            return ['success' => false, 'message' => '商户不存在'];
        }

        return [
            'success' => true,
            'data' => [
                'merchant_id' => $merchant->id,
                'balance' => $merchant->balance,
                'frozen_balance' => $merchant->frozen_balance,
                'available_balance' => $merchant->available_balance,
            ]
        ];
    }

    /**
     * 商户余额充值
     */
    public function rechargeMerchantBalance($merchantId, $amount, $operator = 'system', $remark = '余额充值'): array
    {
        try {
            DB::beginTransaction();

            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return ['success' => false, 'message' => '商户不存在'];
            }

            if ($amount <= 0) {
                return ['success' => false, 'message' => '充值金额必须大于0'];
            }

            $success = $merchant->recharge($amount, null, null, $operator, $remark);
            
            if (!$success) {
                DB::rollBack();
                return ['success' => false, 'message' => '余额充值失败'];
            }

            DB::commit();

            Log::info('商户余额充值成功', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'new_balance' => $merchant->fresh()->balance,
                'operator' => $operator,
            ]);

            return [
                'success' => true,
                'data' => [
                    'merchant_id' => $merchantId,
                    'amount' => $amount,
                    'new_balance' => $merchant->fresh()->balance,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('商户余额充值失败', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '充值失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 扣除商户余额
     */
    public function deductMerchantBalance($merchantId, $amount, $relatedType = null, $relatedId = null, $remark = '余额扣除', $operator = 'system'): array
    {
        try {
            DB::beginTransaction();

            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return ['success' => false, 'message' => '商户不存在'];
            }

            if ($amount <= 0) {
                return ['success' => false, 'message' => '扣除金额必须大于0'];
            }

            if ($merchant->available_balance < $amount) {
                return ['success' => false, 'message' => '余额不足'];
            }

            $success = $merchant->consume($amount, $relatedType, $relatedId, $operator, $remark);
            
            if (!$success) {
                DB::rollBack();
                return ['success' => false, 'message' => '余额扣除失败'];
            }

            DB::commit();

            Log::info('商户余额扣除成功', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'new_balance' => $merchant->fresh()->balance,
                'related_type' => $relatedType,
                'related_id' => $relatedId,
                'operator' => $operator,
            ]);

            return [
                'success' => true,
                'data' => [
                    'merchant_id' => $merchantId,
                    'amount' => $amount,
                    'new_balance' => $merchant->fresh()->balance,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('商户余额扣除失败', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '扣除失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 退款到商户余额
     */
    public function refundToMerchantBalance($merchantId, $amount, $relatedType = null, $relatedId = null, $remark = '订单退款', $operator = 'system'): array
    {
        try {
            DB::beginTransaction();

            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return ['success' => false, 'message' => '商户不存在'];
            }

            if ($amount <= 0) {
                return ['success' => false, 'message' => '退款金额必须大于0'];
            }

            $success = $merchant->refund($amount, $relatedType, $relatedId, $operator, $remark);
            
            if (!$success) {
                DB::rollBack();
                return ['success' => false, 'message' => '退款失败'];
            }

            DB::commit();

            Log::info('商户余额退款成功', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'new_balance' => $merchant->fresh()->balance,
                'related_type' => $relatedType,
                'related_id' => $relatedId,
                'operator' => $operator,
            ]);

            return [
                'success' => true,
                'data' => [
                    'merchant_id' => $merchantId,
                    'amount' => $amount,
                    'new_balance' => $merchant->fresh()->balance,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('商户余额退款失败', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '退款失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 冻结商户余额
     */
    public function freezeMerchantBalance($merchantId, $amount, $operator = 'system', $remark = '余额冻结'): array
    {
        try {
            DB::beginTransaction();

            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return ['success' => false, 'message' => '商户不存在'];
            }

            if ($amount <= 0) {
                return ['success' => false, 'message' => '冻结金额必须大于0'];
            }

            if ($merchant->available_balance < $amount) {
                return ['success' => false, 'message' => '可用余额不足'];
            }

            // 增加冻结余额
            $merchant->increment('frozen_balance', $amount);

            // 记录日志
            MerchantBalanceLog::createLog([
                'merchant_id' => $merchantId,
                'type' => MerchantBalanceLog::TYPE_FREEZE,
                'amount' => $amount,
                'balance_before' => $merchant->balance,
                'balance_after' => $merchant->balance,
                'operator' => $operator,
                'remark' => $remark,
            ]);

            DB::commit();

            Log::info('商户余额冻结成功', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'frozen_balance' => $merchant->fresh()->frozen_balance,
                'operator' => $operator,
            ]);

            return [
                'success' => true,
                'data' => [
                    'merchant_id' => $merchantId,
                    'frozen_amount' => $amount,
                    'frozen_balance' => $merchant->fresh()->frozen_balance,
                    'available_balance' => $merchant->fresh()->available_balance,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('商户余额冻结失败', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '冻结失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 解冻商户余额
     */
    public function unfreezeMerchantBalance($merchantId, $amount, $operator = 'system', $remark = '余额解冻'): array
    {
        try {
            DB::beginTransaction();

            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return ['success' => false, 'message' => '商户不存在'];
            }

            if ($amount <= 0) {
                return ['success' => false, 'message' => '解冻金额必须大于0'];
            }

            if ($merchant->frozen_balance < $amount) {
                return ['success' => false, 'message' => '冻结余额不足'];
            }

            // 减少冻结余额
            $merchant->decrement('frozen_balance', $amount);

            // 记录日志
            MerchantBalanceLog::createLog([
                'merchant_id' => $merchantId,
                'type' => MerchantBalanceLog::TYPE_UNFREEZE,
                'amount' => $amount,
                'balance_before' => $merchant->balance,
                'balance_after' => $merchant->balance,
                'operator' => $operator,
                'remark' => $remark,
            ]);

            DB::commit();

            Log::info('商户余额解冻成功', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'frozen_balance' => $merchant->fresh()->frozen_balance,
                'operator' => $operator,
            ]);

            return [
                'success' => true,
                'data' => [
                    'merchant_id' => $merchantId,
                    'unfrozen_amount' => $amount,
                    'frozen_balance' => $merchant->fresh()->frozen_balance,
                    'available_balance' => $merchant->fresh()->available_balance,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('商户余额解冻失败', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '解冻失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 调整商户余额
     */
    public function adjustMerchantBalance($merchantId, $amount, $operator = 'admin', $remark = '余额调整'): array
    {
        try {
            DB::beginTransaction();

            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return ['success' => false, 'message' => '商户不存在'];
            }

            if ($amount == 0) {
                return ['success' => false, 'message' => '调整金额不能为0'];
            }

            $balanceBefore = $merchant->balance;
            $balanceAfter = $balanceBefore + $amount;

            if ($balanceAfter < 0) {
                return ['success' => false, 'message' => '调整后余额不能为负数'];
            }

            // 更新余额
            $merchant->update(['balance' => $balanceAfter]);

            // 记录日志
            MerchantBalanceLog::createLog([
                'merchant_id' => $merchantId,
                'type' => MerchantBalanceLog::TYPE_ADJUST,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'operator' => $operator,
                'remark' => $remark,
            ]);

            DB::commit();

            Log::info('商户余额调整成功', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'operator' => $operator,
            ]);

            return [
                'success' => true,
                'data' => [
                    'merchant_id' => $merchantId,
                    'adjust_amount' => $amount,
                    'balance_before' => $balanceBefore,
                    'balance_after' => $balanceAfter,
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('商户余额调整失败', [
                'merchant_id' => $merchantId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '调整失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 获取商户余额变动记录
     */
    public function getMerchantBalanceLogs($merchantId, $page = 1, $perPage = 20, $type = null): array
    {
        $query = MerchantBalanceLog::where('merchant_id', $merchantId);

        if ($type) {
            $query->where('type', $type);
        }

        $total = $query->count();
        $logs = $query->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => $log->id,
                    'type' => $log->type,
                    'type_text' => $log->type_text,
                    'amount' => $log->amount,
                    'balance_before' => $log->balance_before,
                    'balance_after' => $log->balance_after,
                    'operator' => $log->operator,
                    'remark' => $log->remark,
                    'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                ];
            });

        return [
            'data' => $logs,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'last_page' => ceil($total / $perPage),
        ];
    }

    /**
     * 检查商户余额是否足够
     */
    public function checkMerchantBalance($merchantId, $amount): array
    {
        $merchant = Merchant::find($merchantId);
        
        if (!$merchant) {
            return ['sufficient' => false, 'message' => '商户不存在'];
        }

        $sufficient = $merchant->available_balance >= $amount;
        
        return [
            'sufficient' => $sufficient,
            'available_balance' => $merchant->available_balance,
            'required_amount' => $amount,
            'shortage' => $sufficient ? 0 : $amount - $merchant->available_balance,
        ];
    }
}
