<?php

namespace App\Console\Commands;

use App\Models\RechargeRecord;
use App\Services\RechargeService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RetryFailedRecharge extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'recharge:retry-failed 
                            {--limit=20 : 每次处理的记录数量}
                            {--max-age=2 : 最大重试时间（小时）}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '重试失败的充值记录';

    protected $rechargeService;

    /**
     * Create a new command instance.
     */
    public function __construct(RechargeService $rechargeService)
    {
        parent::__construct();
        $this->rechargeService = $rechargeService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $limit = $this->option('limit');
        $maxAge = $this->option('max-age');

        $this->info("开始重试失败的充值记录，限制：{$limit} 条，最大时间：{$maxAge} 小时");

        try {
            // 查询可重试的失败记录
            $records = RechargeRecord::where('status', RechargeRecord::STATUS_FAILED)
                ->where('created_at', '>=', now()->subHours($maxAge))
                ->whereColumn('retry_times', '<', 3)
                ->whereNotIn('error_code', ['INSUFFICIENT_BALANCE', 'INVALID_MOBILE'])
                ->orderBy('created_at', 'asc')
                ->limit($limit)
                ->get();

            if ($records->isEmpty()) {
                $this->info('没有需要重试的充值记录');
                return 0;
            }

            $this->info("找到 {$records->count()} 条需要重试的记录");

            $successCount = 0;
            $failedCount = 0;

            foreach ($records as $record) {
                $this->line("重试记录 ID: {$record->id}, 订单号: {$record->provider_order_no}, 重试次数: {$record->retry_times}");

                try {
                    // 增加重试次数
                    $record->increment('retry_times');

                    // 重新处理充值
                    $result = $this->rechargeService->processRechargeOrder($record->order);

                    if ($result['success']) {
                        $successCount++;
                        $this->info("  ✓ 重试成功");
                    } else {
                        $failedCount++;
                        $this->warn("  ✗ 重试失败: {$result['message']}");
                    }

                    // 避免请求过于频繁
                    sleep(2);

                } catch (\Exception $e) {
                    $this->error("  ✗ 重试异常: {$e->getMessage()}");
                    $failedCount++;

                    Log::error('重试充值异常', [
                        'record_id' => $record->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $this->info("\n重试完成:");
            $this->info("成功: {$successCount} 条");
            $this->warn("失败: {$failedCount} 条");

            Log::info('重试失败充值完成', [
                'total' => $records->count(),
                'success' => $successCount,
                'failed' => $failedCount,
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->error("重试失败充值失败: {$e->getMessage()}");
            
            Log::error('重试失败充值失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }
}
