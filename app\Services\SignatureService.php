<?php

namespace App\Services;

use App\Models\Merchant;
use Illuminate\Support\Facades\Log;

class SignatureService
{
    /**
     * 生成MD5签名
     */
    public function generateSignature(array $params, string $key): string
    {
        // 移除签名字段
        unset($params['sign']);
        
        // 过滤空值
        $params = array_filter($params, function ($value) {
            return $value !== '' && $value !== null;
        });
        
        // 按键名排序
        ksort($params);
        
        // 拼接字符串
        $signString = '';
        foreach ($params as $k => $v) {
            $signString .= $k . '=' . $v . '&';
        }
        
        // 添加密钥
        $signString .= 'key=' . $key;
        
        // 生成MD5签名
        $signature = md5($signString);
        
        Log::debug('生成签名', [
            'params' => $params,
            'sign_string' => $signString,
            'signature' => $signature,
        ]);
        
        return strtoupper($signature);
    }

    /**
     * 验证MD5签名
     */
    public function verifySignature(array $params, string $key, string $signature): bool
    {
        $expectedSignature = $this->generateSignature($params, $key);
        $isValid = $expectedSignature === strtoupper($signature);
        
        Log::debug('验证签名', [
            'params' => $params,
            'expected' => $expectedSignature,
            'received' => strtoupper($signature),
            'valid' => $isValid,
        ]);
        
        return $isValid;
    }

    /**
     * 验证商户签名
     */
    public function verifyMerchantSignature($merchantId, array $params, string $signature): array
    {
        try {
            $merchant = Merchant::find($merchantId);
            
            if (!$merchant) {
                return [
                    'success' => false,
                    'message' => '商户不存在',
                    'code' => 'MERCHANT_NOT_FOUND'
                ];
            }

            if (!$merchant->status) {
                return [
                    'success' => false,
                    'message' => '商户已禁用',
                    'code' => 'MERCHANT_DISABLED'
                ];
            }

            $isValid = $this->verifySignature($params, $merchant->md5_key, $signature);
            
            if (!$isValid) {
                Log::warning('商户签名验证失败', [
                    'merchant_id' => $merchantId,
                    'params' => $params,
                    'signature' => $signature,
                ]);
                
                return [
                    'success' => false,
                    'message' => '签名验证失败',
                    'code' => 'INVALID_SIGNATURE'
                ];
            }

            return [
                'success' => true,
                'merchant' => $merchant,
            ];
        } catch (\Exception $e) {
            Log::error('签名验证异常', [
                'merchant_id' => $merchantId,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'success' => false,
                'message' => '签名验证异常',
                'code' => 'SIGNATURE_ERROR'
            ];
        }
    }

    /**
     * 为响应数据生成签名
     */
    public function signResponse(array $data, string $key): array
    {
        $signature = $this->generateSignature($data, $key);
        $data['sign'] = $signature;
        
        return $data;
    }

    /**
     * 生成通知签名
     */
    public function generateNotifySignature(array $data, string $key): string
    {
        return $this->generateSignature($data, $key);
    }

    /**
     * 验证通知签名
     */
    public function verifyNotifySignature(array $data, string $key, string $signature): bool
    {
        return $this->verifySignature($data, $key, $signature);
    }

    /**
     * 生成随机字符串（用于nonce）
     */
    public function generateNonce($length = 16): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $nonce = '';
        
        for ($i = 0; $i < $length; $i++) {
            $nonce .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $nonce;
    }

    /**
     * 生成时间戳
     */
    public function generateTimestamp(): int
    {
        return time();
    }

    /**
     * 验证时间戳（防重放攻击）
     */
    public function verifyTimestamp(int $timestamp, int $tolerance = 300): bool
    {
        $currentTime = time();
        $timeDiff = abs($currentTime - $timestamp);
        
        return $timeDiff <= $tolerance;
    }

    /**
     * 创建完整的签名参数
     */
    public function createSignedParams(array $params, string $key, bool $includeTimestamp = true, bool $includeNonce = true): array
    {
        if ($includeTimestamp) {
            $params['timestamp'] = $this->generateTimestamp();
        }
        
        if ($includeNonce) {
            $params['nonce'] = $this->generateNonce();
        }
        
        $params['sign'] = $this->generateSignature($params, $key);
        
        return $params;
    }

    /**
     * 验证完整的签名参数
     */
    public function verifySignedParams(array $params, string $key, bool $checkTimestamp = true, int $timestampTolerance = 300): array
    {
        // 检查必要字段
        if (!isset($params['sign'])) {
            return [
                'success' => false,
                'message' => '缺少签名参数',
                'code' => 'MISSING_SIGNATURE'
            ];
        }

        $signature = $params['sign'];

        // 验证时间戳
        if ($checkTimestamp && isset($params['timestamp'])) {
            if (!$this->verifyTimestamp($params['timestamp'], $timestampTolerance)) {
                return [
                    'success' => false,
                    'message' => '请求时间戳无效',
                    'code' => 'INVALID_TIMESTAMP'
                ];
            }
        }

        // 验证签名
        if (!$this->verifySignature($params, $key, $signature)) {
            return [
                'success' => false,
                'message' => '签名验证失败',
                'code' => 'INVALID_SIGNATURE'
            ];
        }

        return ['success' => true];
    }

    /**
     * 生成API请求示例
     */
    public function generateApiExample($merchantId, array $params): array
    {
        $merchant = Merchant::find($merchantId);
        
        if (!$merchant) {
            return ['error' => '商户不存在'];
        }

        // 添加商户ID
        $params['merchant_id'] = $merchantId;
        
        // 创建签名参数
        $signedParams = $this->createSignedParams($params, $merchant->md5_key);
        
        return [
            'url' => config('app.url') . '/api/recharge',
            'method' => 'POST',
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'params' => $signedParams,
            'curl_example' => $this->generateCurlExample($signedParams),
        ];
    }

    /**
     * 生成CURL示例
     */
    private function generateCurlExample(array $params): string
    {
        $url = config('app.url') . '/api/recharge';
        $jsonParams = json_encode($params, JSON_UNESCAPED_UNICODE);
        
        return "curl -X POST '{$url}' \\\n" .
               "  -H 'Content-Type: application/json' \\\n" .
               "  -H 'Accept: application/json' \\\n" .
               "  -d '{$jsonParams}'";
    }

    /**
     * 验证IP白名单
     */
    public function verifyIpWhitelist($merchantId, string $ip): bool
    {
        $merchant = Merchant::find($merchantId);
        
        if (!$merchant) {
            return false;
        }

        return $merchant->isIpAllowed($ip);
    }

    /**
     * 记录签名验证日志
     */
    public function logSignatureVerification($merchantId, array $params, string $signature, bool $success, string $ip = null): void
    {
        Log::info('签名验证记录', [
            'merchant_id' => $merchantId,
            'params' => array_keys($params), // 只记录参数名，不记录值
            'signature' => $signature,
            'success' => $success,
            'ip' => $ip,
            'timestamp' => now()->toDateTimeString(),
        ]);
    }
}
