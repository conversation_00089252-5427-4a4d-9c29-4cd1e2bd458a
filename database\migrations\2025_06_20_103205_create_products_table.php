<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('产品名称');
            $table->string('code', 50)->unique()->comment('产品编码');
            $table->text('description')->nullable()->comment('产品描述');
            $table->decimal('price', 10, 2)->comment('销售价格');
            $table->decimal('cost', 10, 2)->default(0)->comment('成本价格');
            $table->decimal('total_value', 10, 2)->comment('券总面值');
            $table->boolean('status')->default(true)->comment('状态：0-下架，1-上架');
            $table->integer('sort')->default(0)->comment('排序');
            $table->integer('sales_count')->default(0)->comment('销售数量');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['status', 'sort']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};