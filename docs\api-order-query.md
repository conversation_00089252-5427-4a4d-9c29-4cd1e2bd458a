# 订单查询接口

## 接口信息
- **请求方式**: POST
- **请求路径**: /api
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sign | string | 是 | 签名 |
| agentAccount | string | 是 | 账号 |
| busiBody | object | 是 | 业务参数体 |
| └─ action | string | 是 | 指令，默认值: CX |
| └─ orderId | string | 是 | 订单号 |

## 请求示例

```json
{
  "sign": "80c9d8367cee60b11194edfd3000f263",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "CX",
    "orderId": "Test00000007"
  }
}
```

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| action | string | 指令 |
| agentAccount | string | 账号 |
| orderId | string | 订单号 |
| chargeId | string | 服务端交易流水号 |
| agentBalance | number | 代理商余额 |
| orderStatuText | string | 订单状态文本 |
| orderStatuInt | string | 订单状态代码 |
| finishTime | string | 订单完成时间 |
| orderPayment | number | 订单支付金额 |
| errorCode | integer | 错误代码 |
| errorDesc | string | 错误描述 |

## 响应示例

```json
{
  "action": "CX",
  "agentAccount": "api_test",
  "orderId": "Test00000007",
  "chargeId": "146",
  "agentBalance": 200,
  "orderStatuText": "缴费成功",
  "orderStatuInt": "16",
  "finishTime": "2022-08-18 14:11:08",
  "orderPayment": 100,
  "errorCode": 1,
  "errorDesc": "操作成功"
}
```

## CURL请求示例

```bash
curl --location --request POST '/api' \
--header 'Content-Type: application/json' \
--data-raw '{
  "sign": "80c9d8367cee60b11194edfd3000f263",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "CX",
    "orderId": "Test00000007"
  }
}'
```