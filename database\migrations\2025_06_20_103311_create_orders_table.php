<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('system_order_no', 32)->unique()->comment('系统订单号');
            $table->foreignId('merchant_id')->constrained()->comment('商户ID');
            $table->string('merchant_order_no', 64)->comment('商户订单号');
            $table->foreignId('user_id')->nullable()->constrained()->comment('用户ID');
            $table->foreignId('product_id')->nullable()->constrained()->comment('产品ID');
            $table->tinyInteger('order_type')->comment('订单类型：1-产品购买，2-话费充值');
            $table->string('mobile', 11)->comment('手机号');
            $table->decimal('amount', 10, 2)->comment('订单金额');
            $table->decimal('cost', 10, 2)->default(0)->comment('成本金额');
            $table->tinyInteger('status')->default(0)->comment('订单状态');
            $table->string('notify_url', 255)->nullable()->comment('回调地址');
            $table->tinyInteger('notify_status')->default(0)->comment('通知状态：0-未通知，1-已通知');
            $table->integer('notify_times')->default(0)->comment('通知次数');
            $table->string('remark', 500)->nullable()->comment('备注');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamps();
            
            $table->unique(['merchant_id', 'merchant_order_no']);
            $table->index('user_id');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};