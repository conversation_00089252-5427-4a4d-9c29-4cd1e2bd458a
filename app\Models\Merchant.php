<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Merchant extends Model
{
    use HasFactory;

    protected $fillable = [
        'account',
        'name',
        'contact_name',
        'contact_phone',
        'email',
        'md5_key',
        'balance',
        'frozen_balance',
        'status',
        'ip_whitelist',
        'remark',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'frozen_balance' => 'decimal:2',
        'status' => 'boolean',
        'ip_whitelist' => 'array',
    ];

    /**
     * 获取可用余额
     */
    public function getAvailableBalanceAttribute()
    {
        return $this->balance - $this->frozen_balance;
    }

    /**
     * 订单关联
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * 检查IP是否在白名单
     */
    public function isIpAllowed($ip)
    {
        if (empty($this->ip_whitelist)) {
            return true;
        }

        return in_array($ip, $this->ip_whitelist);
    }
}