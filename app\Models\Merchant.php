<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Merchant extends Model
{
    use HasFactory;

    protected $fillable = [
        'account',
        'name',
        'contact_name',
        'contact_phone',
        'email',
        'md5_key',
        'balance',
        'frozen_balance',
        'status',
        'ip_whitelist',
        'remark',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'frozen_balance' => 'decimal:2',
        'status' => 'boolean',
        'ip_whitelist' => 'array',
    ];

    /**
     * 获取可用余额
     */
    public function getAvailableBalanceAttribute()
    {
        return $this->balance - $this->frozen_balance;
    }

    /**
     * 订单关联
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * 关联余额变动日志
     */
    public function balanceLogs()
    {
        return $this->hasMany(MerchantBalanceLog::class);
    }

    /**
     * 检查IP是否在白名单
     */
    public function isIpAllowed($ip)
    {
        if (empty($this->ip_whitelist)) {
            return true;
        }

        return in_array($ip, $this->ip_whitelist);
    }

    /**
     * 更新余额并记录日志
     */
    public function updateBalance($amount, $type, $relatedType = null, $relatedId = null, $operator = 'system', $remark = ''): bool
    {
        $balanceBefore = $this->balance;
        $balanceAfter = $balanceBefore + $amount;

        // 检查余额是否足够（如果是扣款）
        if ($amount < 0 && $balanceAfter < 0) {
            return false;
        }

        // 更新余额
        $this->update(['balance' => $balanceAfter]);

        // 记录日志
        MerchantBalanceLog::createLog([
            'merchant_id' => $this->id,
            'type' => $type,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'related_type' => $relatedType,
            'related_id' => $relatedId,
            'operator' => $operator,
            'remark' => $remark,
        ]);

        return true;
    }

    /**
     * 充值
     */
    public function recharge($amount, $relatedType = null, $relatedId = null, $operator = 'system', $remark = '账户充值'): bool
    {
        return $this->updateBalance($amount, MerchantBalanceLog::TYPE_RECHARGE, $relatedType, $relatedId, $operator, $remark);
    }

    /**
     * 消费
     */
    public function consume($amount, $relatedType = null, $relatedId = null, $operator = 'system', $remark = '账户消费'): bool
    {
        return $this->updateBalance(-abs($amount), MerchantBalanceLog::TYPE_CONSUME, $relatedType, $relatedId, $operator, $remark);
    }

    /**
     * 退款
     */
    public function refund($amount, $relatedType = null, $relatedId = null, $operator = 'system', $remark = '账户退款'): bool
    {
        return $this->updateBalance($amount, MerchantBalanceLog::TYPE_REFUND, $relatedType, $relatedId, $operator, $remark);
    }
}