# 话费充值接口

## 接口信息
- **请求方式**: POST
- **请求路径**: /api
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sign | string | 是 | 签名 |
| agentAccount | string | 是 | 账号 |
| busiBody | object | 是 | 业务报文 |
| └─ action | string | 是 | 交易指令码，默认值: CZ |
| └─ orderId | string | 是 | 商户订单号 |
| └─ chargeAcct | string | 是 | 充值号码 |
| └─ chargeCash | string | 是 | 充值金额 |
| └─ chargeType | string | 是 | 充值类型 |
| └─ ispName | string/null | 否 | 运营商（使用UrlEncode转码，采用UTF-8字符集） |
| └─ province | string/null | 否 | 充值号码所属省份（示例值: 广东、北京、黑龙江） |
| └─ retUrl | string/null | 否 | 回调地址（空值将不触发回调通知；使用UrlEncode转码，采用UTF-8字符集） |

## 请求示例

```json
{
  "sign": "149e22fbd59acd8749336dca0202328e",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "CZ",
    "orderId": "Test00000002",
    "chargeAcct": "***********",
    "chargeCash": "10",
    "chargeType": "0",
    "retUrl": "http://127.0.0.1:8081/retcall"
  }
}
```

## 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| action | string | 交易指令码 |
| agentAccount | string | 账号 |
| orderId | string | 商户定单号 |
| chargeId | string | 交易流水号 |
| errorCode | integer | 错误代码 |
| errorDesc | string | 错误描述 |

## 响应示例

```json
{
  "action": "CZ",
  "agentAccount": "api_test",
  "orderId": "Test00000002",
  "chargeId": "181*****604",
  "errorCode": 1,
  "errorDesc": "操作成功"
}
```

## CURL请求示例

```bash
curl --location --request POST '/api' \
--header 'Content-Type: application/json' \
--data-raw '{
  "sign": "149e22fbd59acd8749336dca0202328e",
  "agentAccount": "api_test",
  "busiBody": {
    "action": "CZ",
    "orderId": "Test00000002",
    "chargeAcct": "***********",
    "chargeCash": "10",
    "chargeType": "0",
    "retUrl": "http://127.0.0.1:8081/retcall"
  }
}'
```