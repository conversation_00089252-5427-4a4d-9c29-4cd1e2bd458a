/node_modules
/public/build
/public/hot
/public/storage
/public/vendor
/storage/*.key
/vendor
.env
.env.backup
.env.production
.env.tmp
.env.*.backup
.phpunit.result.cache
.phpunit.cache
docker-compose.override.yml
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode
.DS_Store
Thumbs.db
*.log
*.cache
*.lock
composer.phar
/bootstrap/cache/*
!/bootstrap/cache/.gitignore
/storage/app/*
!/storage/app/public
!/storage/app/.gitignore
/storage/framework/cache/*
!/storage/framework/cache/.gitignore
!/storage/framework/cache/data
/storage/framework/cache/data/*
!/storage/framework/cache/data/.gitignore
/storage/framework/sessions/*
!/storage/framework/sessions/.gitignore
/storage/framework/testing/*
!/storage/framework/testing/.gitignore
/storage/framework/views/*
!/storage/framework/views/.gitignore
/storage/logs/*
!/storage/logs/.gitignore
/public/packages
/public/mix-manifest.json
/packages
update_env.sh
