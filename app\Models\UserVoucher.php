<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserVoucher extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_id',
        'product_id',
        'voucher_value',
        'voucher_code',
        'status',
        'used_order_id',
        'used_at',
        'expired_at',
    ];

    protected $casts = [
        'used_at' => 'datetime',
        'expired_at' => 'datetime',
    ];

    // 状态常量
    const STATUS_UNUSED = 0;    // 未使用
    const STATUS_USED = 1;      // 已使用
    const STATUS_EXPIRED = 2;   // 已过期

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联来源订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 关联产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 关联使用订单
     */
    public function usedOrder()
    {
        return $this->belongsTo(Order::class, 'used_order_id');
    }

    /**
     * 作用域：可用的券
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', self::STATUS_UNUSED)
            ->where(function ($q) {
                $q->whereNull('expired_at')
                    ->orWhere('expired_at', '>', now());
            });
    }
}
