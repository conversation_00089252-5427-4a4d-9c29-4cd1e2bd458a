<?php

namespace App\Services\Recharge\Requests;

/**
 * 订单查询请求数据传输对象
 * 
 * 封装订单查询请求的参数
 */
class OrderQueryRequest
{
    /**
     * @var string 要查询的订单号
     */
    public string $orderId;

    /**
     * 构造函数
     * 
     * @param string $orderId 订单号
     */
    public function __construct(string $orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * 转换为业务参数数组
     * 
     * @return array 业务参数数组
     */
    public function toBusiBody(): array
    {
        return [
            'action' => 'CX',
            'orderId' => $this->orderId,
        ];
    }
}