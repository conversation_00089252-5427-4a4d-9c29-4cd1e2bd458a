<?php

namespace App\Http\Controllers\Api;

use App\Services\OrderService;
use App\Services\VoucherService;
use App\Services\ConfigService;
use App\Services\MobileCarrierService;
use App\Transformers\OrderTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class FrontendRechargeController extends BaseApiController
{
    protected $orderService;
    protected $voucherService;
    protected $configService;
    protected $mobileCarrierService;

    public function __construct(
        OrderService $orderService,
        VoucherService $voucherService,
        ConfigService $configService,
        MobileCarrierService $mobileCarrierService
    ) {
        $this->orderService = $orderService;
        $this->voucherService = $voucherService;
        $this->configService = $configService;
        $this->mobileCarrierService = $mobileCarrierService;
    }

    /**
     * 获取充值配置信息
     */
    public function getRechargeConfig()
    {
        try {
            $config = [
                'supported_amounts' => $this->configService->getSupportedAmounts(),
                'min_amount' => $this->configService->getMinRechargeAmount(),
                'max_amount' => $this->configService->getMaxRechargeAmount(),
                'fee_rate' => $this->configService->getRechargeFeeRate(),
                'allow_guest' => $this->configService->isGuestRechargeAllowed(),
                'supported_carriers' => $this->mobileCarrierService->getSupportedCarriers(),
            ];

            return $this->success($config);

        } catch (\Exception $e) {
            return $this->serverError('获取充值配置失败');
        }
    }

    /**
     * 获取手机号运营商信息
     */
    public function getCarrierInfo(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'mobile' => 'required|regex:/^1[3-9]\d{9}$/',
            ]);

            $carrierInfo = $this->mobileCarrierService->getCarrierInfo($validated['mobile']);
            
            return $this->success($carrierInfo);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('获取运营商信息失败');
        }
    }

    /**
     * 创建充值订单
     */
    public function createOrder(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'mobile' => 'required|regex:/^1[3-9]\d{9}$/',
                'amount' => 'required|numeric|min:1',
                'voucher_ids' => 'nullable|array',
                'voucher_ids.*' => 'integer|exists:user_vouchers,id',
            ], [
                'mobile.required' => '请输入手机号',
                'mobile.regex' => '手机号格式不正确',
                'amount.required' => '请选择充值金额',
                'amount.numeric' => '充值金额格式不正确',
                'amount.min' => '充值金额必须大于0',
            ]);

            // 验证充值金额是否支持
            if (!$this->configService->isAmountSupported($validated['amount'])) {
                return $this->error('不支持的充值金额', 'UNSUPPORTED_AMOUNT');
            }

            // 验证券的所有权（如果使用了券且用户已登录）
            if ($validated['voucher_ids'] && Auth::check()) {
                $userVouchers = $this->voucherService->getUserAvailableVouchers(Auth::id());
                $userVoucherIds = collect($userVouchers)->pluck('id')->toArray();
                
                foreach ($validated['voucher_ids'] as $voucherId) {
                    if (!in_array($voucherId, $userVoucherIds)) {
                        return $this->error('券不存在或不可用', 'INVALID_VOUCHER');
                    }
                }
            }

            // 创建订单数据
            $orderData = [
                'user_id' => Auth::id(),
                'mobile' => $validated['mobile'],
                'amount' => $validated['amount'],
                'voucher_ids' => $validated['voucher_ids'] ?? [],
                'operator' => Auth::check() ? 'user' : 'guest',
            ];

            $result = $this->orderService->createRechargeOrder($orderData);
            
            if (!$result['success']) {
                return $this->error($result['message'], 'ORDER_CREATE_FAILED');
            }

            $order = $result['order'];

            return $this->success([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'mobile' => $order->mobile,
                'amount' => $order->amount,
                'voucher_deduct' => $result['voucher_deduction'] ?? 0,
                'actual_amount' => $result['actual_amount'] ?? $order->amount,
                'status' => $order->status,
                'status_text' => $order->status_text,
                'created_at' => $order->created_at->toDateTimeString(),
            ], '订单创建成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            \Log::error('创建充值订单失败', [
                'user_id' => Auth::id(),
                'params' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('创建订单失败');
        }
    }

    /**
     * 计算券抵扣
     */
    public function calculateVoucherDeduction(Request $request)
    {
        try {
            if (!Auth::check()) {
                return $this->unauthorized('请先登录');
            }

            $validated = $this->validateRequest([
                'amount' => 'required|numeric|min:1',
                'voucher_ids' => 'nullable|array',
                'voucher_ids.*' => 'integer',
            ]);

            $result = $this->voucherService->calculateOptimalVoucherUsage(
                Auth::id(),
                $validated['amount'],
                $validated['voucher_ids']
            );

            return $this->success($result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('计算抵扣失败');
        }
    }

    /**
     * 获取用户可用券
     */
    public function getUserVouchers()
    {
        try {
            if (!Auth::check()) {
                return $this->unauthorized('请先登录');
            }

            $vouchers = $this->voucherService->getUserAvailableVouchers(Auth::id());

            return $this->success($vouchers);

        } catch (\Exception $e) {
            return $this->serverError('获取可用券失败');
        }
    }

    /**
     * 查询订单状态
     */
    public function queryOrder(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'order_no' => 'required|string',
            ]);

            $query = \App\Models\Order::where('order_no', $validated['order_no']);

            // 如果用户已登录，只查询自己的订单
            if (Auth::check()) {
                $query->where('user_id', Auth::id());
            }

            $order = $query->with(['rechargeRecord', 'payments'])->first();

            if (!$order) {
                return $this->notFound('订单不存在');
            }

            return $this->item($order, new OrderTransformer(), ['recharge_record', 'payments']);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('查询订单失败');
        }
    }

    /**
     * 模拟支付订单
     */
    public function payOrder(Request $request, $orderId)
    {
        try {
            $validated = $this->validateRequest([
                'payment_method' => 'required|in:alipay,wechat,balance',
            ]);

            $query = \App\Models\Order::where('id', $orderId)
                ->where('status', \App\Models\Order::STATUS_PENDING);

            // 如果用户已登录，只能支付自己的订单
            if (Auth::check()) {
                $query->where('user_id', Auth::id());
            }

            $order = $query->first();

            if (!$order) {
                return $this->notFound('订单不存在或无法支付');
            }

            // 模拟支付处理
            $paymentData = [
                'method' => $validated['payment_method'],
                'operator' => Auth::check() ? 'user' : 'guest',
            ];

            // 使用OrderService处理支付，这样会自动触发充值处理
            $result = $this->orderService->processOrderPayment($order, $paymentData);

            if (!$result['success']) {
                return $this->error($result['message'], 'PAYMENT_FAILED');
            }

            $order = $result['order'];
            $payment = $result['payment'];

            return $this->success([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'payment_method' => $validated['payment_method'],
                'amount' => $order->actual_amount ?? $order->amount,
                'status' => 'paid',
            ], '支付成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('订单支付失败', [
                'user_id' => Auth::id(),
                'order_id' => $orderId,
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('支付失败');
        }
    }
}
