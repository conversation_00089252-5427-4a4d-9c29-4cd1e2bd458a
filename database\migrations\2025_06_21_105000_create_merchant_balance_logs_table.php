<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchant_balance_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('merchant_id')->constrained()->comment('商户ID');
            $table->string('type', 20)->comment('变动类型：recharge,consume,refund,adjust');
            $table->decimal('amount', 10, 2)->comment('变动金额');
            $table->decimal('balance_before', 10, 2)->comment('变动前余额');
            $table->decimal('balance_after', 10, 2)->comment('变动后余额');
            $table->string('related_type', 50)->nullable()->comment('关联类型：order,recharge_record等');
            $table->unsignedBigInteger('related_id')->nullable()->comment('关联ID');
            $table->string('operator', 50)->nullable()->comment('操作者');
            $table->string('remark', 255)->nullable()->comment('备注');
            $table->json('extra_data')->nullable()->comment('额外数据');
            $table->timestamps();
            
            $table->index(['merchant_id', 'type']);
            $table->index(['merchant_id', 'created_at']);
            $table->index(['related_type', 'related_id']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchant_balance_logs');
    }
};
