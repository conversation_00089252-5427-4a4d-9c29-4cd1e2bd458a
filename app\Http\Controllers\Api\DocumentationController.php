<?php

namespace App\Http\Controllers\Api;

class DocumentationController extends BaseApiController
{
    /**
     * 获取前端API文档
     */
    public function frontendApiDocumentation()
    {
        try {
            $documentation = [
                'title' => '前端API文档',
                'version' => '1.0',
                'base_url' => config('app.url') . '/api/v1',
                'description' => '话费充值系统前端API接口文档',
                
                'authentication' => [
                    'type' => 'Bearer Token',
                    'description' => '部分接口需要用户登录，使用Bearer Token认证',
                    'header' => 'Authorization: Bearer {token}',
                ],

                'endpoints' => [
                    'auth' => [
                        'title' => '用户认证',
                        'apis' => [
                            [
                                'name' => '用户注册',
                                'method' => 'POST',
                                'path' => '/auth/register',
                                'auth_required' => false,
                                'description' => '用户注册账号',
                                'parameters' => [
                                    'name' => '姓名（必填）',
                                    'email' => '邮箱（必填，唯一）',
                                    'password' => '密码（必填，至少8位）',
                                    'password_confirmation' => '确认密码（必填）',
                                    'mobile' => '手机号（可选，11位数字）',
                                ],
                                'response_example' => [
                                    'success' => true,
                                    'code' => 'SUCCESS',
                                    'message' => '注册成功',
                                    'data' => [
                                        'user' => [
                                            'id' => 1,
                                            'name' => '张三',
                                            'email' => '<EMAIL>',
                                            'mobile' => '13800138000',
                                        ],
                                        'token' => 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
                                        'token_type' => 'Bearer',
                                    ],
                                ],
                            ],
                            [
                                'name' => '用户登录',
                                'method' => 'POST',
                                'path' => '/auth/login',
                                'auth_required' => false,
                                'description' => '用户邮箱密码登录',
                                'parameters' => [
                                    'email' => '邮箱（必填）',
                                    'password' => '密码（必填）',
                                ],
                            ],
                            [
                                'name' => '手机号登录',
                                'method' => 'POST',
                                'path' => '/auth/login-mobile',
                                'auth_required' => false,
                                'description' => '手机号验证码登录',
                                'parameters' => [
                                    'mobile' => '手机号（必填）',
                                    'code' => '验证码（必填，6位）',
                                ],
                            ],
                            [
                                'name' => '发送验证码',
                                'method' => 'POST',
                                'path' => '/auth/send-sms',
                                'auth_required' => false,
                                'description' => '发送短信验证码',
                                'parameters' => [
                                    'mobile' => '手机号（必填）',
                                    'type' => '类型（必填：login,register,reset）',
                                ],
                            ],
                            [
                                'name' => '用户登出',
                                'method' => 'POST',
                                'path' => '/auth/logout',
                                'auth_required' => true,
                                'description' => '用户登出，删除当前token',
                            ],
                            [
                                'name' => '获取用户信息',
                                'method' => 'GET',
                                'path' => '/auth/me',
                                'auth_required' => true,
                                'description' => '获取当前登录用户信息',
                            ],
                        ],
                    ],
                    
                    'products' => [
                        'title' => '产品相关',
                        'apis' => [
                            [
                                'name' => '获取产品列表',
                                'method' => 'GET',
                                'path' => '/frontend/products',
                                'auth_required' => false,
                                'description' => '获取可购买的产品列表',
                                'parameters' => [
                                    'page' => '页码（可选，默认1）',
                                    'per_page' => '每页数量（可选，默认20，最大100）',
                                    'sort' => '排序（可选：price_asc,price_desc,sales_desc,created_desc）',
                                    'search' => '搜索关键词（可选）',
                                ],
                            ],
                            [
                                'name' => '获取产品详情',
                                'method' => 'GET',
                                'path' => '/frontend/products/{id}',
                                'auth_required' => false,
                                'description' => '获取指定产品的详细信息',
                            ],
                            [
                                'name' => '获取热门产品',
                                'method' => 'GET',
                                'path' => '/frontend/products/popular',
                                'auth_required' => false,
                                'description' => '获取热门产品列表',
                                'parameters' => [
                                    'limit' => '数量限制（可选，默认6，最大20）',
                                ],
                            ],
                            [
                                'name' => '购买产品',
                                'method' => 'POST',
                                'path' => '/frontend/products/{id}/purchase',
                                'auth_required' => true,
                                'description' => '购买指定产品',
                                'parameters' => [
                                    'quantity' => '购买数量（必填，1-10）',
                                ],
                            ],
                        ],
                    ],
                    
                    'recharge' => [
                        'title' => '充值相关',
                        'apis' => [
                            [
                                'name' => '获取充值配置',
                                'method' => 'GET',
                                'path' => '/frontend/recharge/config',
                                'auth_required' => false,
                                'description' => '获取充值相关配置信息',
                            ],
                            [
                                'name' => '查询运营商信息',
                                'method' => 'POST',
                                'path' => '/frontend/recharge/carrier-info',
                                'auth_required' => false,
                                'description' => '根据手机号查询运营商信息',
                                'parameters' => [
                                    'mobile' => '手机号（必填，11位数字）',
                                ],
                            ],
                            [
                                'name' => '创建充值订单',
                                'method' => 'POST',
                                'path' => '/frontend/recharge/create-order',
                                'auth_required' => false,
                                'description' => '创建话费充值订单',
                                'parameters' => [
                                    'mobile' => '手机号（必填）',
                                    'amount' => '充值金额（必填）',
                                    'voucher_ids' => '使用的券ID数组（可选，需要登录）',
                                ],
                            ],
                            [
                                'name' => '查询订单状态',
                                'method' => 'POST',
                                'path' => '/frontend/recharge/query-order',
                                'auth_required' => false,
                                'description' => '查询充值订单状态',
                                'parameters' => [
                                    'order_no' => '订单号（必填）',
                                ],
                            ],
                            [
                                'name' => '支付订单',
                                'method' => 'POST',
                                'path' => '/frontend/orders/{id}/pay',
                                'auth_required' => false,
                                'description' => '支付订单（模拟支付）',
                                'parameters' => [
                                    'payment_method' => '支付方式（必填：alipay,wechat,balance）',
                                ],
                            ],
                        ],
                    ],
                    
                    'user' => [
                        'title' => '用户管理',
                        'apis' => [
                            [
                                'name' => '获取用户资料',
                                'method' => 'GET',
                                'path' => '/user/profile',
                                'auth_required' => true,
                                'description' => '获取用户个人资料',
                            ],
                            [
                                'name' => '更新用户资料',
                                'method' => 'PUT',
                                'path' => '/user/profile',
                                'auth_required' => true,
                                'description' => '更新用户个人资料',
                                'parameters' => [
                                    'name' => '姓名（必填）',
                                    'email' => '邮箱（必填）',
                                    'mobile' => '手机号（可选）',
                                    'avatar' => '头像URL（可选）',
                                ],
                            ],
                            [
                                'name' => '修改密码',
                                'method' => 'POST',
                                'path' => '/user/change-password',
                                'auth_required' => true,
                                'description' => '修改用户密码',
                                'parameters' => [
                                    'current_password' => '当前密码（必填）',
                                    'password' => '新密码（必填）',
                                    'password_confirmation' => '确认新密码（必填）',
                                ],
                            ],
                            [
                                'name' => '获取用户统计',
                                'method' => 'GET',
                                'path' => '/user/statistics',
                                'auth_required' => true,
                                'description' => '获取用户统计信息',
                            ],
                            [
                                'name' => '获取用户订单',
                                'method' => 'GET',
                                'path' => '/user/orders',
                                'auth_required' => true,
                                'description' => '获取用户订单列表',
                                'parameters' => [
                                    'type' => '订单类型（可选：product,recharge）',
                                    'status' => '订单状态（可选：0-5）',
                                    'page' => '页码（可选）',
                                    'per_page' => '每页数量（可选）',
                                ],
                            ],
                            [
                                'name' => '获取用户券',
                                'method' => 'GET',
                                'path' => '/user/vouchers',
                                'auth_required' => true,
                                'description' => '获取用户券列表',
                                'parameters' => [
                                    'status' => '券状态（可选：available,used,expired）',
                                    'page' => '页码（可选）',
                                    'per_page' => '每页数量（可选）',
                                ],
                            ],
                        ],
                    ],
                ],

                'response_format' => [
                    'success' => '是否成功（boolean）',
                    'code' => '响应码（string）',
                    'message' => '响应消息（string）',
                    'data' => '响应数据（object|array）',
                    'timestamp' => '响应时间（string）',
                ],

                'error_codes' => [
                    'SUCCESS' => '操作成功',
                    'VALIDATION_ERROR' => '参数验证失败',
                    'INVALID_CREDENTIALS' => '邮箱或密码错误',
                    'ACCOUNT_DISABLED' => '账户已被禁用',
                    'UNAUTHORIZED' => '未授权访问',
                    'NOT_FOUND' => '资源不存在',
                    'UNSUPPORTED_AMOUNT' => '不支持的充值金额',
                    'INVALID_VOUCHER' => '券不存在或不可用',
                    'ORDER_CREATE_FAILED' => '订单创建失败',
                    'PURCHASE_VALIDATION_FAILED' => '购买验证失败',
                    'SERVER_ERROR' => '服务器内部错误',
                ],
            ];

            return $this->success($documentation);

        } catch (\Exception $e) {
            return $this->serverError('获取API文档失败');
        }
    }
}
