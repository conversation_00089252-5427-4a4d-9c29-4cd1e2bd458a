# 话费充值系统 - 产品化设计方案

## 一、核心概念

### 1.1 系统定位
- **本质**：话费券销售平台
- **核心**：产品管理 + 订单处理 + 充值履约

### 1.2 基础概念
- **产品**：可销售的套餐（如"充100送200元话费券"）
- **话费券**：产品包含的权益（5元、10元、20元、30元、50元面值）
- **订单**：用户购买产品的记录
- **履约**：用户使用话费券进行充值

## 二、数据模型设计

### 2.1 产品表 (products)
```sql
CREATE TABLE products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '产品名称',
    price DECIMAL(10,2) NOT NULL COMMENT '售价',
    status TINYINT DEFAULT 1 COMMENT '状态：0-下架，1-上架',
    url_slug VARCHAR(50) UNIQUE COMMENT 'URL标识',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 产品配置表 (product_vouchers)
```sql
CREATE TABLE product_vouchers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    voucher_value INT NOT NULL COMMENT '券面值：5/10/20/30/50',
    quantity INT NOT NULL COMMENT '数量',
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

### 2.3 订单表 (orders)
```sql
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    product_id BIGINT NOT NULL,
    phone VARCHAR(11) NOT NULL COMMENT '手机号',
    price DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    pay_status TINYINT DEFAULT 0 COMMENT '0-未支付，1-已支付',
    pay_time DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

### 2.4 用户话费券表 (user_vouchers)
```sql
CREATE TABLE user_vouchers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(11) NOT NULL,
    voucher_value INT NOT NULL COMMENT '券面值',
    quantity INT DEFAULT 0 COMMENT '剩余数量',
    UNIQUE KEY idx_phone_value (phone, voucher_value)
);
```

### 2.5 充值记录表 (recharge_records)
```sql
CREATE TABLE recharge_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(11) NOT NULL,
    recharge_amount INT NOT NULL COMMENT '充值金额',
    pay_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    voucher_used JSON COMMENT '使用的券：[{value:20,quantity:1}]',
    api_order_id VARCHAR(50) COMMENT 'API订单号',
    status TINYINT DEFAULT 0 COMMENT '0-处理中，1-成功，2-失败',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 三、功能设计

### 3.1 产品管理（后台）
```
1. 创建产品
   - 输入产品名称
   - 设置价格
   - 配置包含的话费券（面值和数量）
   - 生成产品URL

2. 产品列表
   - 查看所有产品
   - 上下架管理
   - 编辑产品信息
```

### 3.2 产品页面（前台）
```
访问：/product/{url_slug}
展示：
- 产品名称
- 价格
- 包含的话费券明细
- 购买按钮
```

### 3.3 购买流程
```
1. 用户访问产品页面
2. 输入手机号
3. 点击购买 → 创建订单
4. 跳转支付
5. 支付成功 → 发放话费券到用户账户
```

### 3.4 充值中心
```
1. 用户输入手机号
2. 显示可用话费券
3. 选择充值金额（100元）
4. 选择使用的话费券
5. 计算实付金额
6. 确认充值 → 调用API
```

## 四、API设计

### 4.1 产品管理API
```
POST   /api/admin/products          # 创建产品
GET    /api/admin/products          # 产品列表
PUT    /api/admin/products/{id}     # 更新产品
DELETE /api/admin/products/{id}     # 删除产品
```

### 4.2 前台API
```
GET    /api/products/{slug}         # 获取产品详情
POST   /api/orders                  # 创建订单
GET    /api/vouchers/{phone}        # 查询可用话费券
POST   /api/recharge                # 话费充值
```

## 五、示例流程

### 5.1 创建产品示例
```json
POST /api/admin/products
{
    "name": "充100送200话费券",
    "price": 100,
    "url_slug": "recharge-100-get-200",
    "vouchers": [
        {"value": 50, "quantity": 2},
        {"value": 30, "quantity": 2},
        {"value": 20, "quantity": 5}
    ]
}
```

### 5.2 用户购买流程
```
1. 访问: https://example.com/product/recharge-100-get-200
2. 输入手机号: 13800138000
3. 支付100元
4. 获得话费券:
   - 50元券 × 2张
   - 30元券 × 2张  
   - 20元券 × 5张
```

### 5.3 用户充值流程
```
1. 进入充值中心
2. 输入手机号查询
3. 显示可用券：50元×2, 30元×2, 20元×5
4. 选择充值100元
5. 选择使用20元券×1
6. 实付: 80元
7. 完成充值
```

## 六、技术实现要点

### 6.1 产品页面生成
- 每个产品对应唯一URL
- 支持自定义落地页模板
- 移动端适配

### 6.2 支付集成
- 支持微信/支付宝
- 支付回调处理
- 订单超时自动关闭

### 6.3 话费充值对接
- 调用充值API
- 异步回调 + 主动查询
- 失败重试机制

### 6.4 数据统计
- 产品销售统计
- 话费券使用统计
- 用户行为分析

## 七、优势

1. **灵活性高**：可以创建任意价格和券组合的产品
2. **扩展性强**：未来可以加入流量券、视频会员等
3. **管理简单**：产品化思维，业务逻辑清晰
4. **技术解耦**：技术只关注产品和订单，不关心具体业务