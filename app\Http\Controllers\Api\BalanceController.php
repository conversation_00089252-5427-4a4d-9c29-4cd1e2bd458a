<?php

namespace App\Http\Controllers\Api;

use App\Services\BalanceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BalanceController extends BaseApiController
{
    protected $balanceService;

    public function __construct(BalanceService $balanceService)
    {
        $this->balanceService = $balanceService;
    }

    /**
     * 查询商户余额
     */
    public function getBalance()
    {
        try {
            $merchant = $this->getMerchant();

            $result = $this->balanceService->getMerchantBalance($merchant->id);

            if (!$result['success']) {
                return $this->error($result['message'], 'BALANCE_QUERY_FAILED');
            }

            // 记录API调用
            $this->logApiCall('get_balance', [], $result['data']);

            return $this->success($result['data']);

        } catch (\Exception $e) {
            Log::error('查询余额失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('查询余额失败');
        }
    }

    /**
     * 查询余额变动记录
     */
    public function getBalanceLogs(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'page' => ['nullable', 'integer', 'min:1'],
                'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
                'type' => ['nullable', 'string', 'in:recharge,consume,refund,adjust,freeze,unfreeze'],
                'start_date' => ['nullable', 'date'],
                'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
            ]);

            $merchant = $this->getMerchant();
            $page = $validated['page'] ?? 1;
            $perPage = $validated['per_page'] ?? 20;
            $type = $validated['type'] ?? null;

            $result = $this->balanceService->getMerchantBalanceLogs(
                $merchant->id,
                $page,
                $perPage,
                $type
            );

            // 记录API调用
            $this->logApiCall('get_balance_logs', $validated, [
                'total' => $result['total'],
                'page' => $result['page'],
            ]);

            return $this->success($result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('查询余额记录失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'params' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('查询余额记录失败');
        }
    }

    /**
     * 检查余额是否足够
     */
    public function checkBalance(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'amount' => ['required', 'numeric', 'min:0.01'],
            ], [
                'amount.required' => '金额不能为空',
                'amount.numeric' => '金额必须是数字',
                'amount.min' => '金额必须大于0',
            ]);

            $merchant = $this->getMerchant();

            $result = $this->balanceService->checkMerchantBalance(
                $merchant->id,
                $validated['amount']
            );

            // 记录API调用
            $this->logApiCall('check_balance', $validated, $result);

            return $this->success($result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            Log::error('检查余额失败', [
                'merchant_id' => $this->getMerchant()?->id,
                'params' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return $this->serverError('检查余额失败');
        }
    }
}
