# 通讯协议和签名

## 通讯协议

### 公共请求参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| sign | String(32) | 参照签名方式说明 |
| agentAccount | String(20) | 商户账号 |
| busiBody | Json | 参照各业务接口模块 |

## 签名方式

### 签名算法
```
sign = md5(busiBody + md5Key)
```

### 签名说明
- **md5Key**: 请联系客户经理发送到开户邮箱
- **busiBody**: 业务参数的JSON字符串
- 将busiBody的JSON字符串与md5Key拼接后进行MD5加密

### 签名示例

**busiBody内容**：
```json
{
  "action": "CZ",
  "orderId": "Test00000002",
  "chargeAcct": "***********",
  "chargeCash": "10",
  "chargeType": "0",
  "retUrl": "http%3A%2F%2Fsy666.com%3A49999"
}
```

**md5Key**：
```
13D5C4F4910EDC34
```

**签名计算过程**：
1. 将busiBody转为JSON字符串
2. 拼接md5Key：`{"action":"CZ","orderId":"Test00000002","chargeAcct":"***********","chargeCash":"10","chargeType":"0","retUrl":"http%3A%2F%2Fsy666.com%3A49999"}13D5C4F4910EDC34`
3. 对拼接后的字符串进行MD5加密得到sign值