<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'channel',
        'recipient',
        'subject',
        'content',
        'headers',
        'params',
        'status',
        'retry_times',
        'max_retry',
        'next_retry_at',
        'sent_at',
        'response',
        'error_msg',
        'related_type',
        'related_id',
    ];

    protected $casts = [
        'headers' => 'json',
        'params' => 'json',
        'next_retry_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    // 发送状态常量
    const STATUS_PENDING = 0;    // 待发送
    const STATUS_SENDING = 1;    // 发送中
    const STATUS_SUCCESS = 2;    // 成功
    const STATUS_FAILED = 3;     // 失败

    // 通知渠道常量
    const CHANNEL_HTTP = 'http';
    const CHANNEL_EMAIL = 'email';
    const CHANNEL_SMS = 'sms';
    const CHANNEL_WEBHOOK = 'webhook';

    // 通知类型常量
    const TYPE_ORDER_STATUS = 'order_status';
    const TYPE_RECHARGE_RESULT = 'recharge_result';
    const TYPE_SYSTEM_ALERT = 'system_alert';
    const TYPE_BALANCE_ALERT = 'balance_alert';
    const TYPE_PAYMENT_NOTIFY = 'payment_notify';

    /**
     * 状态文本映射
     */
    public static $statusTexts = [
        self::STATUS_PENDING => '待发送',
        self::STATUS_SENDING => '发送中',
        self::STATUS_SUCCESS => '成功',
        self::STATUS_FAILED => '失败',
    ];

    /**
     * 渠道文本映射
     */
    public static $channelTexts = [
        self::CHANNEL_HTTP => 'HTTP请求',
        self::CHANNEL_EMAIL => '邮件',
        self::CHANNEL_SMS => '短信',
        self::CHANNEL_WEBHOOK => 'Webhook',
    ];

    /**
     * 类型文本映射
     */
    public static $typeTexts = [
        self::TYPE_ORDER_STATUS => '订单状态通知',
        self::TYPE_RECHARGE_RESULT => '充值结果通知',
        self::TYPE_SYSTEM_ALERT => '系统告警',
        self::TYPE_BALANCE_ALERT => '余额告警',
        self::TYPE_PAYMENT_NOTIFY => '支付通知',
    ];

    /**
     * 获取关联对象
     */
    public function related()
    {
        if (!$this->related_type || !$this->related_id) {
            return null;
        }

        $class = 'App\\Models\\' . studly_case($this->related_type);
        if (class_exists($class)) {
            return $class::find($this->related_id);
        }

        return null;
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::$statusTexts[$this->status] ?? '未知';
    }

    /**
     * 获取渠道文本
     */
    public function getChannelTextAttribute()
    {
        return self::$channelTexts[$this->channel] ?? '未知';
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute()
    {
        return self::$typeTexts[$this->type] ?? '未知';
    }

    /**
     * 是否可以重试
     */
    public function canRetry(): bool
    {
        return $this->status === self::STATUS_FAILED 
            && $this->retry_times < $this->max_retry
            && ($this->next_retry_at === null || $this->next_retry_at <= now());
    }

    /**
     * 是否已成功
     */
    public function isSuccess(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    /**
     * 是否已失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 标记为发送中
     */
    public function markAsSending(): void
    {
        $this->update([
            'status' => self::STATUS_SENDING,
            'sent_at' => now(),
        ]);
    }

    /**
     * 标记为成功
     */
    public function markAsSuccess($response = null): void
    {
        $this->update([
            'status' => self::STATUS_SUCCESS,
            'response' => $response,
            'error_msg' => null,
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed($errorMsg, $response = null): void
    {
        $nextRetryAt = null;
        
        // 如果还可以重试，计算下次重试时间
        if ($this->retry_times < $this->max_retry) {
            $delay = min(pow(2, $this->retry_times) * 60, 3600); // 指数退避，最大1小时
            $nextRetryAt = now()->addSeconds($delay);
        }
        
        $this->update([
            'status' => self::STATUS_FAILED,
            'retry_times' => $this->retry_times + 1,
            'next_retry_at' => $nextRetryAt,
            'response' => $response,
            'error_msg' => $errorMsg,
        ]);
    }

    /**
     * 创建通知记录
     */
    public static function createNotification(array $data): self
    {
        return self::create([
            'type' => $data['type'],
            'channel' => $data['channel'],
            'recipient' => $data['recipient'],
            'subject' => $data['subject'] ?? null,
            'content' => $data['content'],
            'headers' => $data['headers'] ?? null,
            'params' => $data['params'] ?? null,
            'max_retry' => $data['max_retry'] ?? 3,
            'related_type' => $data['related_type'] ?? null,
            'related_id' => $data['related_id'] ?? null,
        ]);
    }

    /**
     * 获取待重试的通知
     */
    public static function getPendingRetries()
    {
        return self::where('status', self::STATUS_FAILED)
            ->where('retry_times', '<', \DB::raw('max_retry'))
            ->where(function ($query) {
                $query->whereNull('next_retry_at')
                    ->orWhere('next_retry_at', '<=', now());
            })
            ->orderBy('created_at')
            ->get();
    }

    /**
     * 获取发送统计
     */
    public static function getSendStats($days = 7): array
    {
        $startDate = now()->subDays($days);
        
        $stats = self::where('created_at', '>=', $startDate)
            ->selectRaw('
                status,
                channel,
                COUNT(*) as count
            ')
            ->groupBy(['status', 'channel'])
            ->get();
            
        $result = [
            'total' => $stats->sum('count'),
            'by_status' => [],
            'by_channel' => [],
        ];
        
        foreach (self::$statusTexts as $status => $text) {
            $result['by_status'][$status] = [
                'name' => $text,
                'count' => $stats->where('status', $status)->sum('count'),
            ];
        }
        
        foreach (self::$channelTexts as $channel => $text) {
            $result['by_channel'][$channel] = [
                'name' => $text,
                'count' => $stats->where('channel', $channel)->sum('count'),
            ];
        }
        
        return $result;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按渠道筛选
     */
    public function scopeByChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：待发送
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：发送失败
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：发送成功
     */
    public function scopeSuccess($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }
}
