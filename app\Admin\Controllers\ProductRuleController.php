<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ProductRule;
use App\Models\Product;
use App\Models\ProductRule as ProductRuleModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ProductRuleController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ProductRule(['product']), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('product.name', '产品名称');
            $grid->column('rule_type', '规则类型')->using(ProductRuleModel::$typeTexts);
            $grid->column('description', '规则描述')->limit(50);
            $grid->column('created_at', '创建时间');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('product_id', '产品')->select(
                    Product::pluck('name', 'id')->toArray()
                );
                $filter->equal('rule_type', '规则类型')->select(ProductRuleModel::$typeTexts);
            });
            
            $grid->quickSearch(['product.name']);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ProductRule(['product']), function (Show $show) {
            $show->field('id');
            $show->field('product.name', '产品名称');
            $show->field('rule_type', '规则类型')->using(ProductRuleModel::$typeTexts);
            $show->field('rule_value', '规则值')->json();
            $show->field('description', '规则描述');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ProductRule(), function (Form $form) {
            $form->display('id');
            
            $form->select('product_id', '产品')
                ->options(Product::pluck('name', 'id'))
                ->required();
                
            $form->select('rule_type', '规则类型')
                ->options(ProductRuleModel::$typeTexts)
                ->required()
                ->when(ProductRuleModel::TYPE_LIMIT_PER_USER, function (Form $form) {
                    $form->number('rule_value.max_count', '最大购买次数')->required()->min(1);
                    $form->select('rule_value.period', '限制周期')->options([
                        'day' => '每天',
                        'week' => '每周',
                        'month' => '每月',
                        'year' => '每年',
                        'total' => '总计',
                    ])->default('month');
                })
                ->when(ProductRuleModel::TYPE_STOCK_LIMIT, function (Form $form) {
                    $form->number('rule_value.max_stock', '最大库存')->required()->min(1);
                })
                ->when(ProductRuleModel::TYPE_SALE_PERIOD, function (Form $form) {
                    $form->datetime('rule_value.start_time', '开始时间')->required();
                    $form->datetime('rule_value.end_time', '结束时间')->required();
                })
                ->when(ProductRuleModel::TYPE_VALID_PERIOD, function (Form $form) {
                    $form->number('rule_value.days', '有效天数')->required()->min(1);
                    $form->switch('rule_value.from_purchase', '从购买日期开始')->default(1);
                })
                ->when(ProductRuleModel::TYPE_MIN_PURCHASE, function (Form $form) {
                    $form->number('rule_value.min_quantity', '最小购买量')->required()->min(1);
                })
                ->when(ProductRuleModel::TYPE_MAX_PURCHASE, function (Form $form) {
                    $form->number('rule_value.max_quantity', '最大购买量')->required()->min(1);
                })
                ->when(ProductRuleModel::TYPE_USER_LEVEL, function (Form $form) {
                    $form->tags('rule_value.allowed_levels', '允许的用户等级')
                        ->options(['bronze' => '青铜', 'silver' => '白银', 'gold' => '黄金', 'platinum' => '铂金']);
                })
                ->when(ProductRuleModel::TYPE_REGION_LIMIT, function (Form $form) {
                    $form->tags('rule_value.allowed_regions', '允许的地区');
                });
            
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
