<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>话费充值系统API概览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .api-endpoint {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0.375rem;
        }
        .method-get { border-left-color: #28a745; }
        .method-post { border-left-color: #007bff; }
        .method-put { border-left-color: #ffc107; }
        .method-delete { border-left-color: #dc3545; }
        .auth-required { background-color: #fff3cd; }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">话费充值系统API概览</h1>
                <p class="text-center text-muted">基于Laravel 9的前后端分离API系统</p>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="row mb-5">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>系统信息</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><strong>版本:</strong> 1.0</li>
                            <li><strong>基础URL:</strong> {{ config('app.url') }}/api/v1</li>
                            <li><strong>认证方式:</strong> MD5签名认证</li>
                            <li><strong>响应格式:</strong> JSON</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>快速链接</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><a href="/api/v1/health" target="_blank">系统健康检查</a></li>
                            <li><a href="/api/v1/config/api-documentation" target="_blank">完整API文档</a></li>
                            <li><a href="/api-test" target="_blank">API测试工具</a></li>
                            <li><a href="/admin" target="_blank">后台管理</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 公开接口 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2>🔓 公开接口（无需认证）</h2>
                <p class="text-muted">这些接口可以直接调用，无需任何认证</p>

                <h4>系统配置</h4>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/health
                    <p class="mb-1">系统健康检查</p>
                </div>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/config/system
                    <p class="mb-1">获取系统配置信息</p>
                </div>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/config/supported-amounts
                    <p class="mb-1">获取支持的充值金额列表</p>
                </div>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/config/supported-carriers
                    <p class="mb-1">获取支持的运营商列表</p>
                </div>

                <h4>产品相关</h4>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/products
                    <p class="mb-1">获取产品列表</p>
                </div>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/products/{id}
                    <p class="mb-1">获取产品详情</p>
                </div>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/products/popular
                    <p class="mb-1">获取热门产品</p>
                </div>

                <h4>工具接口</h4>
                <div class="api-endpoint method-get">
                    <strong>GET</strong> /api/v1/supported-amounts
                    <p class="mb-1">获取支持的充值金额（兼容接口）</p>
                </div>
                <div class="api-endpoint method-post">
                    <strong>POST</strong> /api/v1/carrier-info
                    <p class="mb-1">根据手机号查询运营商信息</p>
                </div>
            </div>
        </div>

        <!-- 商户认证接口 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2>🔐 商户认证接口（需要MD5签名）</h2>
                <p class="text-muted">这些接口需要商户认证，使用MD5签名验证</p>

                <h4>充值相关</h4>
                <div class="api-endpoint method-post auth-required">
                    <strong>POST</strong> /api/v1/recharge/create-order
                    <p class="mb-1">创建话费充值订单</p>
                </div>
                <div class="api-endpoint method-post auth-required">
                    <strong>POST</strong> /api/v1/recharge/query-order
                    <p class="mb-1">查询充值订单状态</p>
                </div>

                <h4>余额管理</h4>
                <div class="api-endpoint method-get auth-required">
                    <strong>GET</strong> /api/v1/balance
                    <p class="mb-1">查询商户余额</p>
                </div>
                <div class="api-endpoint method-get auth-required">
                    <strong>GET</strong> /api/v1/balance/logs
                    <p class="mb-1">查询余额变动记录</p>
                </div>
                <div class="api-endpoint method-post auth-required">
                    <strong>POST</strong> /api/v1/balance/check
                    <p class="mb-1">检查余额是否足够</p>
                </div>

                <h4>订单管理</h4>
                <div class="api-endpoint method-get auth-required">
                    <strong>GET</strong> /api/v1/orders
                    <p class="mb-1">查询订单列表</p>
                </div>
                <div class="api-endpoint method-get auth-required">
                    <strong>GET</strong> /api/v1/orders/{orderNo}
                    <p class="mb-1">查询订单详情</p>
                </div>
                <div class="api-endpoint method-get auth-required">
                    <strong>GET</strong> /api/v1/orders/statistics
                    <p class="mb-1">获取订单统计信息</p>
                </div>

                <h4>产品购买</h4>
                <div class="api-endpoint method-post auth-required">
                    <strong>POST</strong> /api/v1/products/{id}/purchase
                    <p class="mb-1">购买产品（通过商户认证）</p>
                </div>
            </div>
        </div>

        <!-- 认证说明 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2>🔒 认证说明</h2>
                <div class="card">
                    <div class="card-body">
                        <h5>MD5签名认证</h5>
                        <p>商户API使用MD5签名认证，签名算法如下：</p>
                        <ol>
                            <li>将所有参数（除sign外）按键名排序</li>
                            <li>拼接成 <code>key1=value1&key2=value2</code> 格式</li>
                            <li>在末尾添加 <code>&key=商户密钥</code></li>
                            <li>对整个字符串进行MD5加密并转大写</li>
                        </ol>
                        
                        <h6>示例：</h6>
                        <div class="code-block">
原始参数：
{
    "merchant_id": "1001",
    "mobile": "13800138000",
    "amount": "10",
    "timestamp": "1640995200",
    "nonce": "abc123"
}

排序后拼接：
amount=10&merchant_id=1001&mobile=13800138000&nonce=abc123&timestamp=1640995200&key=your_secret_key

MD5加密并转大写：
sign = MD5("amount=10&merchant_id=1001&mobile=13800138000&nonce=abc123&timestamp=1640995200&key=your_secret_key").toUpperCase()
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 响应格式 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2>📋 响应格式</h2>
                <div class="card">
                    <div class="card-body">
                        <h5>统一响应格式</h5>
                        <div class="code-block">
{
    "success": true,
    "code": "SUCCESS",
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "timestamp": "2025-06-21 10:40:00"
}
                        </div>
                        
                        <h5 class="mt-3">常见错误码</h5>
                        <ul>
                            <li><code>SUCCESS</code> - 操作成功</li>
                            <li><code>VALIDATION_ERROR</code> - 参数验证失败</li>
                            <li><code>MISSING_MERCHANT_ID</code> - 缺少商户ID</li>
                            <li><code>INVALID_SIGNATURE</code> - 签名验证失败</li>
                            <li><code>INSUFFICIENT_BALANCE</code> - 商户余额不足</li>
                            <li><code>UNSUPPORTED_AMOUNT</code> - 不支持的充值金额</li>
                            <li><code>NOT_FOUND</code> - 资源不存在</li>
                            <li><code>SERVER_ERROR</code> - 服务器内部错误</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="text-center text-muted py-4">
            <p>&copy; {{ date('Y') }} 话费充值系统. All rights reserved.</p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
