<?php

namespace App\Admin\Controllers;

use App\Models\Order;
use App\Models\Product;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Box;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\DB;

class SalesReportController extends AdminController
{
    public function index(Content $content)
    {
        return $content
            ->title('销售统计')
            ->description('销售数据统计分析')
            ->body(function (Row $row) {
                // 第一行：统计卡片
                $row->column(3, $this->totalOrdersCard());
                $row->column(3, $this->totalRevenueCard());
                $row->column(3, $this->successRateCard());
                $row->column(3, $this->avgOrderAmountCard());
            })
            ->body(function (Row $row) {
                // 第二行：图表
                $row->column(6, $this->dailySalesChart());
                $row->column(6, $this->productSalesChart());
            })
            ->body(function (Row $row) {
                // 第三行：表格数据
                $row->column(12, $this->topProductsTable());
            });
    }

    /**
     * 总订单数卡片
     */
    private function totalOrdersCard()
    {
        $total = Order::where('order_type', Order::TYPE_PRODUCT)->count();
        $today = Order::where('order_type', Order::TYPE_PRODUCT)
            ->whereDate('created_at', today())
            ->count();

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">{$total}</h2>
        <p class="text-muted mb-0">今日：{$today}</p>
    </div>
    <i class="feather icon-shopping-cart font-size-40"></i>
</div>
HTML;

        return Card::make('总订单数', $content)->tool('<a href="/admin/orders">查看详情</a>');
    }

    /**
     * 总收入卡片
     */
    private function totalRevenueCard()
    {
        $total = Order::where('order_type', Order::TYPE_PRODUCT)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->sum('amount');
        $today = Order::where('order_type', Order::TYPE_PRODUCT)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->whereDate('orders.created_at', today())
            ->sum('amount');

        $total = number_format($total, 2);
        $today = number_format($today, 2);

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">¥{$total}</h2>
        <p class="text-muted mb-0">今日：¥{$today}</p>
    </div>
    <i class="feather icon-dollar-sign font-size-40"></i>
</div>
HTML;

        return Card::make('总收入', $content);
    }

    /**
     * 成功率卡片
     */
    private function successRateCard()
    {
        $total = Order::where('order_type', Order::TYPE_PRODUCT)
            ->whereIn('orders.status', [Order::STATUS_SUCCESS, Order::STATUS_FAILED])
            ->count();
        $success = Order::where('order_type', Order::TYPE_PRODUCT)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->count();

        $rate = $total > 0 ? round($success / $total * 100, 2) : 0;

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">{$rate}%</h2>
        <p class="text-muted mb-0">成功率</p>
    </div>
    <i class="feather icon-trending-up font-size-40"></i>
</div>
HTML;

        return Card::make('订单成功率', $content);
    }

    /**
     * 平均订单金额卡片
     */
    private function avgOrderAmountCard()
    {
        $avg = Order::where('order_type', Order::TYPE_PRODUCT)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->avg('amount');

        $avg = number_format($avg ?: 0, 2);

        $content = <<<HTML
<div class="d-flex justify-content-between align-items-center">
    <div>
        <h2 class="ml-1 font-w-700">¥{$avg}</h2>
        <p class="text-muted mb-0">平均订单金额</p>
    </div>
    <i class="feather icon-bar-chart-2 font-size-40"></i>
</div>
HTML;

        return Card::make('客单价', $content);
    }

    /**
     * 每日销售图表
     */
    private function dailySalesChart()
    {
        $data = Order::where('order_type', Order::TYPE_PRODUCT)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->where('orders.created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(orders.amount) as amount')
            )
            ->get();

        $dates = [];
        $counts = [];
        $amounts = [];

        foreach ($data as $item) {
            $dates[] = $item->date;
            $counts[] = $item->count;
            $amounts[] = round($item->amount, 2);
        }

        $chartData = [
            'labels' => $dates,
            'datasets' => [
                [
                    'label' => '订单数',
                    'data' => $counts,
                    'borderColor' => 'rgb(75, 192, 192)',
                    'yAxisID' => 'y-axis-1',
                ],
                [
                    'label' => '销售额',
                    'data' => $amounts,
                    'borderColor' => 'rgb(255, 99, 132)',
                    'yAxisID' => 'y-axis-2',
                ]
            ]
        ];

        $chartDataJson = json_encode($chartData);
        
        $chartHtml = <<<HTML
<canvas id="dailySalesChart" height="300"></canvas>
<script>
Dcat.ready(function () {
    const ctx = document.getElementById('dailySalesChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {$chartDataJson},
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                yAxes: [{
                    id: 'y-axis-1',
                    type: 'linear',
                    display: true,
                    position: 'left'
                }, {
                    id: 'y-axis-2',
                    type: 'linear',
                    display: true,
                    position: 'right',
                    gridLines: {
                        drawOnChartArea: false
                    }
                }]
            }
        }
    });
});
</script>
HTML;

        return Box::make('最近30天销售趋势', $chartHtml);
    }

    /**
     * 产品销售分布图
     */
    private function productSalesChart()
    {
        $data = Order::where('order_type', Order::TYPE_PRODUCT)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->groupBy('orders.product_id', 'products.name')
            ->select(
                'orders.product_id',
                'products.name',
                DB::raw('COUNT(*) as count')
            )
            ->orderBy('count', 'desc')
            ->limit(5)
            ->get();

        $labels = [];
        $values = [];

        foreach ($data as $item) {
            $labels[] = $item->name;
            $values[] = $item->count;
        }

        $chartData = [
            'labels' => $labels,
            'datasets' => [[
                'data' => $values,
                'backgroundColor' => [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                ],
            ]]
        ];

        $chartDataJson = json_encode($chartData);
        
        $chartHtml = <<<HTML
<canvas id="productSalesChart" height="300"></canvas>
<script>
Dcat.ready(function () {
    const ctx = document.getElementById('productSalesChart').getContext('2d');
    new Chart(ctx, {
        type: 'pie',
        data: {$chartDataJson},
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>
HTML;

        return Box::make('产品销售分布', $chartHtml);
    }

    /**
     * 热销产品表格
     */
    private function topProductsTable()
    {
        $products = Order::where('order_type', Order::TYPE_PRODUCT)
            ->where('orders.status', Order::STATUS_SUCCESS)
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->groupBy('orders.product_id', 'products.name', 'products.price')
            ->select(
                'orders.product_id',
                'products.name',
                'products.price',
                DB::raw('COUNT(*) as order_count'),
                DB::raw('SUM(orders.amount) as total_amount')
            )
            ->orderBy('order_count', 'desc')
            ->limit(10)
            ->get();

        $rows = '';
        foreach ($products as $index => $product) {
            $no = $index + 1;
            $totalAmount = number_format($product->total_amount, 2);
            $rows .= <<<HTML
<tr>
    <td>{$no}</td>
    <td>{$product->name}</td>
    <td>¥{$product->price}</td>
    <td>{$product->order_count}</td>
    <td>¥{$totalAmount}</td>
</tr>
HTML;
        }

        $tableHtml = <<<HTML
<table class="table table-hover">
    <thead>
        <tr>
            <th>排名</th>
            <th>产品名称</th>
            <th>单价</th>
            <th>销售数量</th>
            <th>销售总额</th>
        </tr>
    </thead>
    <tbody>
        {$rows}
    </tbody>
</table>
HTML;

        return Box::make('热销产品TOP10', $tableHtml);
    }
}