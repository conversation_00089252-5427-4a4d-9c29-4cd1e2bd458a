<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 充值API配置
    |--------------------------------------------------------------------------
    |
    | 这里配置充值系统的API接口地址、商户账号和密钥等信息
    |
    */

    /**
     * API接口地址
     */
    'api_url' => env('RECHARGE_API_URL', 'http://api.example.com/api'),

    /**
     * 商户账号
     */
    'agent_account' => env('RECHARGE_AGENT_ACCOUNT', ''),

    /**
     * MD5密钥
     */
    'md5_key' => env('RECHARGE_MD5_KEY', ''),

    /**
     * 请求超时时间（秒）
     */
    'timeout' => env('RECHARGE_TIMEOUT', 30),

    /**
     * 是否启用日志记录
     */
    'enable_log' => env('RECHARGE_ENABLE_LOG', true),

    /**
     * 错误码映射
     */
    'error_codes' => [
        1 => '操作成功',
        -1 => '账户不存在',
        -2 => '签名错误',
        -3 => '账户被暂停',
        -4 => '无API权限',
        -5 => '参数错误',
        -6 => '无产品权限',
        -7 => 'IP鉴权失败',
        -8 => '自定义面值和价格不合法',
        -9 => '号码异常',
        -10 => '产品不存在',
        -11 => '余额不足',
        -12 => '订单号重复',
        -13 => '订单号长度超限',
        -14 => '消息解密失败',
        -15 => '优惠券不存在',
        -16 => '充值失败',
        -17 => '请求频繁',
        -18 => '手机号不合法',
        -19 => '运营商类型有误',
        -20 => '订单类型有误',
        -21 => '代理商账户被暂停',
        -22 => '运营商地区维护',
        -98 => '产品暂停销售',
        -99 => '系统异常',
        -999 => '未知错误',
    ],

    /**
     * 订单状态码映射
     */
    'order_status' => [
        '16' => '缴费成功',
        '17' => '缴费失败',
        // 可以根据实际情况添加更多状态
    ],
];