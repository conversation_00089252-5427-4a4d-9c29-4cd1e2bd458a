<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MerchantBalanceLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'merchant_id',
        'type',
        'amount',
        'balance_before',
        'balance_after',
        'related_type',
        'related_id',
        'operator',
        'remark',
        'extra_data',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'extra_data' => 'json',
    ];

    // 变动类型常量
    const TYPE_RECHARGE = 'recharge';     // 充值
    const TYPE_CONSUME = 'consume';       // 消费
    const TYPE_REFUND = 'refund';         // 退款
    const TYPE_ADJUST = 'adjust';         // 调整
    const TYPE_FREEZE = 'freeze';         // 冻结
    const TYPE_UNFREEZE = 'unfreeze';     // 解冻

    /**
     * 类型文本映射
     */
    public static $typeTexts = [
        self::TYPE_RECHARGE => '充值',
        self::TYPE_CONSUME => '消费',
        self::TYPE_REFUND => '退款',
        self::TYPE_ADJUST => '调整',
        self::TYPE_FREEZE => '冻结',
        self::TYPE_UNFREEZE => '解冻',
    ];

    /**
     * 关联商户
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    /**
     * 获取关联对象
     */
    public function related()
    {
        if (!$this->related_type || !$this->related_id) {
            return null;
        }

        $class = 'App\\Models\\' . studly_case($this->related_type);
        if (class_exists($class)) {
            return $class::find($this->related_id);
        }

        return null;
    }

    /**
     * 获取类型文本
     */
    public function getTypeTextAttribute()
    {
        return self::$typeTexts[$this->type] ?? '未知';
    }

    /**
     * 获取变动方向
     */
    public function getDirectionAttribute()
    {
        return $this->amount > 0 ? 'in' : 'out';
    }

    /**
     * 获取变动方向文本
     */
    public function getDirectionTextAttribute()
    {
        return $this->amount > 0 ? '收入' : '支出';
    }

    /**
     * 获取绝对金额
     */
    public function getAbsAmountAttribute()
    {
        return abs($this->amount);
    }

    /**
     * 创建余额变动记录
     */
    public static function createLog(array $data): self
    {
        return self::create([
            'merchant_id' => $data['merchant_id'],
            'type' => $data['type'],
            'amount' => $data['amount'],
            'balance_before' => $data['balance_before'],
            'balance_after' => $data['balance_after'],
            'related_type' => $data['related_type'] ?? null,
            'related_id' => $data['related_id'] ?? null,
            'operator' => $data['operator'] ?? 'system',
            'remark' => $data['remark'] ?? '',
            'extra_data' => $data['extra_data'] ?? null,
        ]);
    }

    /**
     * 获取商户余额统计
     */
    public static function getBalanceStats($merchantId, $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        $stats = self::where('merchant_id', $merchantId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('
                type,
                COUNT(*) as count,
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_in,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_out
            ')
            ->groupBy('type')
            ->get()
            ->keyBy('type');

        $result = [];
        foreach (self::$typeTexts as $type => $text) {
            $stat = $stats->get($type);
            $result[$type] = [
                'name' => $text,
                'count' => $stat->count ?? 0,
                'total_in' => $stat->total_in ?? 0,
                'total_out' => $stat->total_out ?? 0,
            ];
        }

        return $result;
    }

    /**
     * 作用域：按商户筛选
     */
    public function scopeByMerchant($query, $merchantId)
    {
        return $query->where('merchant_id', $merchantId);
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：收入记录
     */
    public function scopeIncome($query)
    {
        return $query->where('amount', '>', 0);
    }

    /**
     * 作用域：支出记录
     */
    public function scopeExpense($query)
    {
        return $query->where('amount', '<', 0);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
