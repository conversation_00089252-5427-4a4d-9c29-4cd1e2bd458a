<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->comment('订单ID');
            $table->string('operator_type', 20)->comment('操作者类型：system,admin,user');
            $table->unsignedBigInteger('operator_id')->nullable()->comment('操作者ID');
            $table->string('action', 50)->comment('操作动作');
            $table->tinyInteger('from_status')->nullable()->comment('原状态');
            $table->tinyInteger('to_status')->nullable()->comment('新状态');
            $table->text('content')->nullable()->comment('日志内容');
            $table->json('extra_data')->nullable()->comment('额外数据');
            $table->timestamp('created_at')->nullable();
            
            $table->index('order_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_logs');
    }
};