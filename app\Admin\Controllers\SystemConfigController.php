<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SystemConfig;
use App\Models\SystemConfig as SystemConfigModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;

class SystemConfigController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SystemConfig(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('group', '分组')->label([
                'system' => 'primary',
                'payment' => 'success',
                'recharge' => 'warning',
                'notification' => 'info',
                'sms' => 'secondary',
                'email' => 'dark',
            ]);
            $grid->column('key', '配置键');
            $grid->column('name', '配置名称');
            $grid->column('value', '配置值')->limit(30);
            $grid->column('type', '数据类型')->using([
                'string' => '字符串',
                'int' => '整数',
                'float' => '浮点数',
                'bool' => '布尔值',
                'json' => 'JSON',
                'array' => '数组',
            ]);
            $grid->column('is_public', '公开')->switch();
            $grid->column('is_required', '必填')->switch();
            $grid->column('sort_order', '排序')->sortable();
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('group', '分组')->select([
                    'system' => '系统配置',
                    'payment' => '支付配置',
                    'recharge' => '充值配置',
                    'notification' => '通知配置',
                    'sms' => '短信配置',
                    'email' => '邮件配置',
                ]);
                $filter->like('key', '配置键');
                $filter->like('name', '配置名称');
                $filter->equal('type', '数据类型')->select([
                    'string' => '字符串',
                    'int' => '整数',
                    'float' => '浮点数',
                    'bool' => '布尔值',
                    'json' => 'JSON',
                    'array' => '数组',
                ]);
            });
            
            $grid->quickSearch(['key', 'name']);
            
            // 按分组和排序显示
            $grid->model()->orderBy('group')->orderBy('sort_order');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SystemConfig(), function (Show $show) {
            $show->field('id');
            $show->field('group', '分组');
            $show->field('key', '配置键');
            $show->field('name', '配置名称');
            $show->field('value', '配置值');
            $show->field('type', '数据类型');
            $show->field('description', '描述');
            $show->field('options', '可选值')->json();
            $show->field('is_public', '公开')->using([0 => '否', 1 => '是']);
            $show->field('is_required', '必填')->using([0 => '否', 1 => '是']);
            $show->field('sort_order', '排序');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SystemConfig(), function (Form $form) {
            $form->display('id');
            
            $form->select('group', '分组')->options([
                'system' => '系统配置',
                'payment' => '支付配置',
                'recharge' => '充值配置',
                'notification' => '通知配置',
                'sms' => '短信配置',
                'email' => '邮件配置',
            ])->required();
            
            $form->text('key', '配置键')->required()
                ->creationRules(['required', 'unique:system_configs,key'])
                ->updateRules(['required', "unique:system_configs,key,{{id}}"]);
            
            $form->text('name', '配置名称')->required();
            
            $form->select('type', '数据类型')->options([
                'string' => '字符串',
                'int' => '整数',
                'float' => '浮点数',
                'bool' => '布尔值',
                'json' => 'JSON',
                'array' => '数组',
            ])->required()->default('string')
            ->when('bool', function (Form $form) {
                $form->switch('value', '配置值');
            })
            ->when('int', function (Form $form) {
                $form->number('value', '配置值');
            })
            ->when('float', function (Form $form) {
                $form->decimal('value', '配置值');
            })
            ->when('json', function (Form $form) {
                $form->textarea('value', '配置值')->help('请输入有效的JSON格式');
            })
            ->when('array', function (Form $form) {
                $form->textarea('value', '配置值')->help('请输入有效的JSON数组格式');
            })
            ->when('string', function (Form $form) {
                $form->text('value', '配置值');
            });
            
            $form->textarea('description', '描述');
            
            $form->textarea('options', '可选值')->help('JSON格式，用于select类型的配置项');
            
            $form->switch('is_public', '公开')->help('公开的配置可以在前端访问');
            $form->switch('is_required', '必填');
            
            $form->number('sort_order', '排序')->default(0)->min(0);
            
            $form->display('created_at');
            $form->display('updated_at');
            
            // 保存后清除缓存
            $form->saved(function () {
                SystemConfigModel::clearCache();
            });
        });
    }

    /**
     * 配置管理页面
     */
    public function index(Content $content)
    {
        return $content
            ->title('系统配置')
            ->description('系统参数配置管理')
            ->body($this->grid());
    }

    /**
     * 快速配置页面
     */
    public function quick(Content $content)
    {
        return $content
            ->title('快速配置')
            ->description('常用配置快速设置')
            ->body(function () {
                $groups = [
                    'system' => '系统配置',
                    'payment' => '支付配置',
                    'recharge' => '充值配置',
                    'notification' => '通知配置',
                ];
                
                $html = '<div class="row">';
                
                foreach ($groups as $group => $name) {
                    $configs = SystemConfigModel::byGroup($group)->ordered()->get();
                    
                    if ($configs->isNotEmpty()) {
                        $html .= '<div class="col-md-6 mb-4">';
                        $html .= '<div class="card">';
                        $html .= '<div class="card-header"><h5>' . $name . '</h5></div>';
                        $html .= '<div class="card-body">';
                        
                        foreach ($configs as $config) {
                            $html .= '<div class="form-group">';
                            $html .= '<label>' . $config->name . '</label>';
                            $html .= '<p class="text-muted small">' . $config->description . '</p>';
                            $html .= '<input type="text" class="form-control" value="' . htmlspecialchars($config->value) . '" readonly>';
                            $html .= '</div>';
                        }
                        
                        $html .= '</div>';
                        $html .= '</div>';
                        $html .= '</div>';
                    }
                }
                
                $html .= '</div>';
                
                return $html;
            });
    }
}
