<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\RechargeProvider;
use App\Models\RechargeProvider as RechargeProviderModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RechargeProviderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RechargeProvider(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('code', '渠道编码');
            $grid->column('name', '渠道名称');
            $grid->column('api_url', 'API地址')->limit(50);
            $grid->column('weight', '权重')->sortable();
            $grid->column('status', '状态')->switch();
            $grid->column('success_rate', '成功率')->display(function ($value) {
                if ($value === null) return '-';
                return $value . '%';
            })->label([
                'default' => 'default',
            ])->if(function ($column) {
                return $column->getValue() >= 95;
            })->label('success')
            ->if(function ($column) {
                return $column->getValue() >= 90 && $column->getValue() < 95;
            })->label('warning')
            ->if(function ($column) {
                return $column->getValue() < 90;
            })->label('danger');
            
            $grid->column('avg_duration', '平均耗时')->display(function ($value) {
                if ($value === null) return '-';
                return $value . 'ms';
            });
            $grid->column('created_at', '创建时间');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('code', '渠道编码');
                $filter->like('name', '渠道名称');
                $filter->equal('status', '状态')->select([
                    0 => '禁用',
                    1 => '启用',
                ]);
            });
            
            $grid->quickSearch(['code', 'name']);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RechargeProvider(), function (Show $show) {
            $show->field('id');
            $show->field('code', '渠道编码');
            $show->field('name', '渠道名称');
            $show->field('api_url', 'API地址');
            $show->field('app_id', '应用ID');
            $show->field('app_secret', '应用密钥')->as(function ($value) {
                return str_repeat('*', strlen($value));
            });
            $show->field('config', '配置信息')->json();
            $show->field('weight', '权重');
            $show->field('status', '状态')->using([
                0 => '禁用',
                1 => '启用',
            ]);
            $show->field('success_rate', '成功率')->as(function ($value) {
                return $value ? $value . '%' : '-';
            });
            $show->field('avg_duration', '平均耗时')->as(function ($value) {
                return $value ? $value . 'ms' : '-';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RechargeProvider(), function (Form $form) {
            $form->display('id');
            
            $form->text('code', '渠道编码')->required()
                ->creationRules(['required', 'unique:recharge_providers'])
                ->updateRules(['required', "unique:recharge_providers,code,{{id}}"]);
            
            $form->text('name', '渠道名称')->required();
            $form->url('api_url', 'API地址')->required();
            $form->text('app_id', '应用ID')->required();
            $form->password('app_secret', '应用密钥')->required();
            
            $form->number('weight', '权重')->default(0)->min(0)->max(100)
                ->help('权重越高优先级越高，范围0-100');
            
            $form->switch('status', '状态')->default(1);
            
            // 配置信息
            $form->fieldset('配置信息', function (Form $form) {
                $form->tags('config.carriers', '支持运营商')->options([
                    'mobile' => '移动',
                    'unicom' => '联通',
                    'telecom' => '电信',
                ])->help('选择该渠道支持的运营商');
                
                $form->tags('config.supported_amounts', '支持金额')->default([5, 10, 20, 30, 50, 100])
                    ->help('输入支持的充值金额，用回车分隔');
                
                $form->number('config.timeout', '超时时间(秒)')->default(30)->min(5)->max(120);
                $form->number('config.max_retry', '最大重试次数')->default(3)->min(0)->max(10);
            });
            
            $form->display('success_rate', '成功率')->with(function ($value) {
                return $value ? $value . '%' : '-';
            });
            $form->display('avg_duration', '平均耗时')->with(function ($value) {
                return $value ? $value . 'ms' : '-';
            });
            
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
