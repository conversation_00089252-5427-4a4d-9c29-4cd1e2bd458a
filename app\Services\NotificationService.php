<?php

namespace App\Services;

use App\Models\NotificationLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * 发送通知
     */
    public function send(NotificationLog $log): bool
    {
        try {
            $log->markAsSending();
            
            $success = match ($log->channel) {
                NotificationLog::CHANNEL_HTTP => $this->sendHttp($log),
                NotificationLog::CHANNEL_WEBHOOK => $this->sendWebhook($log),
                NotificationLog::CHANNEL_EMAIL => $this->sendEmail($log),
                NotificationLog::CHANNEL_SMS => $this->sendSms($log),
                default => false,
            };
            
            if ($success) {
                $log->markAsSuccess();
                return true;
            } else {
                $log->markAsFailed('发送失败');
                return false;
            }
        } catch (\Exception $e) {
            $log->markAsFailed($e->getMessage());
            Log::error('通知发送失败', [
                'log_id' => $log->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 发送HTTP通知
     */
    private function sendHttp(NotificationLog $log): bool
    {
        $response = Http::withHeaders($log->headers ?? [])
            ->timeout(30)
            ->post($log->recipient, $log->params ?? []);
            
        $log->update(['response' => $response->body()]);
        
        return $response->successful();
    }

    /**
     * 发送Webhook通知
     */
    private function sendWebhook(NotificationLog $log): bool
    {
        return $this->sendHttp($log);
    }

    /**
     * 发送邮件通知
     */
    private function sendEmail(NotificationLog $log): bool
    {
        // 这里应该集成邮件发送服务
        // 例如使用Laravel的Mail功能
        return true;
    }

    /**
     * 发送短信通知
     */
    private function sendSms(NotificationLog $log): bool
    {
        // 这里应该集成短信发送服务
        // 例如阿里云短信、腾讯云短信等
        return true;
    }

    /**
     * 创建订单状态通知
     */
    public function createOrderNotification($order, $notifyUrl): NotificationLog
    {
        return NotificationLog::createNotification([
            'type' => NotificationLog::TYPE_ORDER_STATUS,
            'channel' => NotificationLog::CHANNEL_HTTP,
            'recipient' => $notifyUrl,
            'subject' => '订单状态通知',
            'content' => '订单状态已更新',
            'params' => [
                'order_no' => $order->order_no,
                'merchant_order_no' => $order->merchant_order_no,
                'status' => $order->status,
                'amount' => $order->amount,
                'updated_at' => $order->updated_at->toDateTimeString(),
            ],
            'related_type' => 'order',
            'related_id' => $order->id,
        ]);
    }

    /**
     * 创建充值结果通知
     */
    public function createRechargeNotification($rechargeRecord, $notifyUrl): NotificationLog
    {
        return NotificationLog::createNotification([
            'type' => NotificationLog::TYPE_RECHARGE_RESULT,
            'channel' => NotificationLog::CHANNEL_HTTP,
            'recipient' => $notifyUrl,
            'subject' => '充值结果通知',
            'content' => '充值结果已更新',
            'params' => [
                'order_no' => $rechargeRecord->order->order_no,
                'mobile' => $rechargeRecord->mobile,
                'amount' => $rechargeRecord->amount,
                'status' => $rechargeRecord->status,
                'completed_at' => $rechargeRecord->completed_at?->toDateTimeString(),
            ],
            'related_type' => 'recharge_record',
            'related_id' => $rechargeRecord->id,
        ]);
    }

    /**
     * 创建系统告警通知
     */
    public function createSystemAlert($title, $message, $recipients = []): array
    {
        $logs = [];
        
        foreach ($recipients as $recipient) {
            $logs[] = NotificationLog::createNotification([
                'type' => NotificationLog::TYPE_SYSTEM_ALERT,
                'channel' => NotificationLog::CHANNEL_EMAIL,
                'recipient' => $recipient,
                'subject' => $title,
                'content' => $message,
            ]);
        }
        
        return $logs;
    }

    /**
     * 处理待重试的通知
     */
    public function processRetries(): int
    {
        $logs = NotificationLog::getPendingRetries();
        $processed = 0;
        
        foreach ($logs as $log) {
            if ($this->send($log)) {
                $processed++;
            }
        }
        
        return $processed;
    }

    /**
     * 批量发送通知
     */
    public function sendBatch(array $notifications): array
    {
        $results = [];
        
        foreach ($notifications as $notification) {
            $log = NotificationLog::createNotification($notification);
            $results[] = [
                'log_id' => $log->id,
                'success' => $this->send($log),
            ];
        }
        
        return $results;
    }
}
