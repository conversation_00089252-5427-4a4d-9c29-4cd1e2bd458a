<?php

namespace Database\Seeders;

use App\Models\SystemConfig;
use Illuminate\Database\Seeder;

class SystemConfigSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $configs = [
            // 系统配置
            [
                'group' => SystemConfig::GROUP_SYSTEM,
                'key' => 'site_name',
                'value' => '话费充值系统',
                'type' => SystemConfig::TYPE_STRING,
                'name' => '网站名称',
                'description' => '系统显示的网站名称',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'group' => SystemConfig::GROUP_SYSTEM,
                'key' => 'site_logo',
                'value' => '/images/logo.png',
                'type' => SystemConfig::TYPE_STRING,
                'name' => '网站Logo',
                'description' => '网站Logo图片路径',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'group' => SystemConfig::GROUP_SYSTEM,
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => SystemConfig::TYPE_BOOL,
                'name' => '维护模式',
                'description' => '开启后系统将进入维护模式',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'group' => SystemConfig::GROUP_SYSTEM,
                'key' => 'max_order_amount',
                'value' => '1000',
                'type' => SystemConfig::TYPE_FLOAT,
                'name' => '最大订单金额',
                'description' => '单笔订单最大金额限制',
                'sort_order' => 4,
            ],
            
            // 支付配置
            [
                'group' => SystemConfig::GROUP_PAYMENT,
                'key' => 'alipay_enabled',
                'value' => 'true',
                'type' => SystemConfig::TYPE_BOOL,
                'name' => '启用支付宝',
                'description' => '是否启用支付宝支付',
                'sort_order' => 1,
            ],
            [
                'group' => SystemConfig::GROUP_PAYMENT,
                'key' => 'wechat_enabled',
                'value' => 'true',
                'type' => SystemConfig::TYPE_BOOL,
                'name' => '启用微信支付',
                'description' => '是否启用微信支付',
                'sort_order' => 2,
            ],
            [
                'group' => SystemConfig::GROUP_PAYMENT,
                'key' => 'payment_timeout',
                'value' => '1800',
                'type' => SystemConfig::TYPE_INT,
                'name' => '支付超时时间',
                'description' => '支付超时时间（秒）',
                'sort_order' => 3,
            ],
            
            // 充值配置
            [
                'group' => SystemConfig::GROUP_RECHARGE,
                'key' => 'recharge_timeout',
                'value' => '60',
                'type' => SystemConfig::TYPE_INT,
                'name' => '充值超时时间',
                'description' => '充值请求超时时间（秒）',
                'sort_order' => 1,
            ],
            [
                'group' => SystemConfig::GROUP_RECHARGE,
                'key' => 'max_retry_times',
                'value' => '3',
                'type' => SystemConfig::TYPE_INT,
                'name' => '最大重试次数',
                'description' => '充值失败最大重试次数',
                'sort_order' => 2,
            ],
            [
                'group' => SystemConfig::GROUP_RECHARGE,
                'key' => 'auto_retry_enabled',
                'value' => 'true',
                'type' => SystemConfig::TYPE_BOOL,
                'name' => '启用自动重试',
                'description' => '充值失败是否自动重试',
                'sort_order' => 3,
            ],
            [
                'group' => SystemConfig::GROUP_RECHARGE,
                'key' => 'supported_amounts',
                'value' => '[5,10,20,30,50,100,200,300,500]',
                'type' => SystemConfig::TYPE_JSON,
                'name' => '支持的充值金额',
                'description' => '系统支持的充值金额列表',
                'is_public' => true,
                'sort_order' => 4,
            ],
            
            // 通知配置
            [
                'group' => SystemConfig::GROUP_NOTIFICATION,
                'key' => 'order_notify_enabled',
                'value' => 'true',
                'type' => SystemConfig::TYPE_BOOL,
                'name' => '启用订单通知',
                'description' => '是否启用订单状态变更通知',
                'sort_order' => 1,
            ],
            [
                'group' => SystemConfig::GROUP_NOTIFICATION,
                'key' => 'notify_max_retry',
                'value' => '5',
                'type' => SystemConfig::TYPE_INT,
                'name' => '通知最大重试次数',
                'description' => '通知发送失败最大重试次数',
                'sort_order' => 2,
            ],
            [
                'group' => SystemConfig::GROUP_NOTIFICATION,
                'key' => 'notify_retry_interval',
                'value' => '300',
                'type' => SystemConfig::TYPE_INT,
                'name' => '通知重试间隔',
                'description' => '通知重试间隔时间（秒）',
                'sort_order' => 3,
            ],
            
            // 短信配置
            [
                'group' => SystemConfig::GROUP_SMS,
                'key' => 'sms_enabled',
                'value' => 'false',
                'type' => SystemConfig::TYPE_BOOL,
                'name' => '启用短信',
                'description' => '是否启用短信功能',
                'sort_order' => 1,
            ],
            [
                'group' => SystemConfig::GROUP_SMS,
                'key' => 'sms_provider',
                'value' => 'aliyun',
                'type' => SystemConfig::TYPE_STRING,
                'name' => '短信服务商',
                'description' => '短信服务提供商',
                'options' => ['aliyun' => '阿里云', 'tencent' => '腾讯云', 'huawei' => '华为云'],
                'sort_order' => 2,
            ],
            
            // 邮件配置
            [
                'group' => SystemConfig::GROUP_EMAIL,
                'key' => 'email_enabled',
                'value' => 'false',
                'type' => SystemConfig::TYPE_BOOL,
                'name' => '启用邮件',
                'description' => '是否启用邮件功能',
                'sort_order' => 1,
            ],
            [
                'group' => SystemConfig::GROUP_EMAIL,
                'key' => 'smtp_host',
                'value' => 'smtp.qq.com',
                'type' => SystemConfig::TYPE_STRING,
                'name' => 'SMTP服务器',
                'description' => 'SMTP服务器地址',
                'sort_order' => 2,
            ],
            [
                'group' => SystemConfig::GROUP_EMAIL,
                'key' => 'smtp_port',
                'value' => '587',
                'type' => SystemConfig::TYPE_INT,
                'name' => 'SMTP端口',
                'description' => 'SMTP服务器端口',
                'sort_order' => 3,
            ],
        ];

        foreach ($configs as $config) {
            SystemConfig::create($config);
        }
    }
}
