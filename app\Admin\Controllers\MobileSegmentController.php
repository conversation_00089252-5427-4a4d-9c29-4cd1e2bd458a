<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\MobileSegment;
use App\Models\MobileSegment as MobileSegmentModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Table;

class MobileSegmentController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new MobileSegment(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('segment', '号段');
            $grid->column('carrier', '运营商')->using(MobileSegmentModel::$carrierTexts)
                ->label([
                    'mobile' => 'success',
                    'unicom' => 'primary',
                    'telecom' => 'warning',
                ]);
            $grid->column('province', '省份');
            $grid->column('city', '城市');
            $grid->column('created_at', '创建时间');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('segment', '号段');
                $filter->equal('carrier', '运营商')->select(MobileSegmentModel::$carrierTexts);
                $filter->like('province', '省份');
                $filter->like('city', '城市');
            });
            
            $grid->quickSearch(['segment', 'province', 'city']);
            
            // 添加统计信息
            $grid->header(function () {
                $stats = MobileSegmentModel::getCarrierStats();
                $total = array_sum(array_column($stats, 'count'));
                
                $html = '<div class="row mb-3">';
                $html .= '<div class="col-md-3"><div class="info-box"><div class="info-box-content"><span class="info-box-text">总号段数</span><span class="info-box-number">' . $total . '</span></div></div></div>';
                
                foreach ($stats as $carrier => $stat) {
                    $color = $carrier === 'mobile' ? 'success' : ($carrier === 'unicom' ? 'primary' : 'warning');
                    $html .= '<div class="col-md-3"><div class="info-box"><div class="info-box-content"><span class="info-box-text">' . $stat['name'] . '</span><span class="info-box-number text-' . $color . '">' . $stat['count'] . '</span></div></div></div>';
                }
                
                $html .= '</div>';
                
                return $html;
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new MobileSegment(), function (Show $show) {
            $show->field('id');
            $show->field('segment', '号段');
            $show->field('carrier', '运营商')->using(MobileSegmentModel::$carrierTexts);
            $show->field('province', '省份');
            $show->field('city', '城市');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new MobileSegment(), function (Form $form) {
            $form->display('id');
            
            $form->text('segment', '号段')->required()
                ->rules('regex:/^1\d{6}$/')
                ->help('请输入7位数字号段，如：1340000')
                ->creationRules(['required', 'unique:mobile_segments'])
                ->updateRules(['required', "unique:mobile_segments,segment,{{id}}"]);
                
            $form->select('carrier', '运营商')
                ->options(MobileSegmentModel::$carrierTexts)
                ->required();
                
            $form->text('province', '省份')->required();
            $form->text('city', '城市')->required();
            
            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    /**
     * 号段统计页面
     */
    public function stats(Content $content)
    {
        return $content
            ->title('号段统计')
            ->description('运营商号段分布统计')
            ->body(function () {
                $carrierStats = MobileSegmentModel::getCarrierStats();
                $provinceStats = MobileSegmentModel::getProvinceStats();
                
                $carrierTable = new Table(['运营商', '号段数量', '占比'], []);
                $total = array_sum(array_column($carrierStats, 'count'));
                
                foreach ($carrierStats as $carrier => $stat) {
                    $percentage = $total > 0 ? round(($stat['count'] / $total) * 100, 2) : 0;
                    $carrierTable->rows[] = [
                        $stat['name'],
                        $stat['count'],
                        $percentage . '%'
                    ];
                }
                
                $provinceTable = new Table(['省份', '号段数量'], []);
                foreach (array_slice($provinceStats, 0, 10) as $province) {
                    $provinceTable->rows[] = [
                        $province['province'],
                        $province['count']
                    ];
                }
                
                return [
                    new Card('运营商分布', $carrierTable),
                    new Card('省份分布（前10）', $provinceTable),
                ];
            });
    }
}
