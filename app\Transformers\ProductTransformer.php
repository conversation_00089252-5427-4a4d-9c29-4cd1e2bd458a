<?php

namespace App\Transformers;

use App\Models\Product;
use League\Fractal\TransformerAbstract;

class ProductTransformer extends TransformerAbstract
{
    /**
     * 可包含的关联资源
     */
    protected array $availableIncludes = [
        'vouchers',
        'rules',
    ];

    /**
     * 转换产品数据
     */
    public function transform(Product $product): array
    {
        return [
            'id' => $product->id,
            'name' => $product->name,
            'code' => $product->code,
            'price' => (float) $product->price,
            'cost' => (float) $product->cost,
            'voucher_total_value' => (float) $product->voucher_total_value,
            'description' => $product->description,
            'status' => $product->status,
            'status_text' => $product->status_text,
            'sales_count' => $product->sales_count,
            'sort_order' => $product->sort_order,
            'created_at' => $product->created_at->toDateTimeString(),
            'updated_at' => $product->updated_at->toDateTimeString(),
        ];
    }

    /**
     * 包含券配置
     */
    public function includeVouchers(Product $product)
    {
        return $this->collection($product->vouchers, new ProductVoucherTransformer());
    }

    /**
     * 包含产品规则
     */
    public function includeRules(Product $product)
    {
        return $this->collection($product->rules, new ProductRuleTransformer());
    }
}
