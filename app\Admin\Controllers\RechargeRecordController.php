<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\RechargeRecord;
use App\Models\RechargeRecord as RechargeRecordModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RechargeRecordController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RechargeRecord(['order', 'order.merchant']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            
            $grid->column('id')->sortable();
            $grid->column('order.system_order_no', '系统订单号');
            $grid->column('order.merchant.name', '商户');
            $grid->column('provider', '充值渠道')->using([
                'unicom' => '联通',
                'mobile' => '移动',
                'telecom' => '电信',
            ])->label([
                'unicom' => 'primary',
                'mobile' => 'success',
                'telecom' => 'info',
            ]);
            $grid->column('provider_order_no', '渠道订单号');
            $grid->column('mobile', '手机号');
            $grid->column('amount', '充值金额')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('status', '状态')->using([
                0 => '待处理',
                1 => '提交中',
                2 => '已提交',
                3 => '处理中',
                10 => '成功',
                20 => '失败',
            ])->dot([
                0 => 'warning',
                1 => 'info',
                2 => 'info',
                3 => 'primary',
                10 => 'success',
                20 => 'danger',
            ]);
            $grid->column('retry_times', '重试次数');
            $grid->column('error_msg', '错误信息')->limit(30);
            $grid->column('submitted_at', '提交时间');
            $grid->column('completed_at', '完成时间');
            $grid->column('created_at', '创建时间');
            
            // 禁用创建和编辑
            $grid->disableCreateButton();
            $grid->disableEditButton();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_id', '订单ID');
                $filter->like('mobile', '手机号');
                $filter->equal('provider', '充值渠道')->select([
                    'unicom' => '联通',
                    'mobile' => '移动',
                    'telecom' => '电信',
                ]);
                $filter->equal('status', '状态')->select([
                    0 => '待处理',
                    1 => '提交中',
                    2 => '已提交',
                    3 => '处理中',
                    10 => '成功',
                    20 => '失败',
                ]);
                $filter->between('created_at', '创建时间')->datetime();
            });
            
            // 快速搜索
            $grid->quickSearch(['mobile', 'provider_order_no']);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RechargeRecord(['order', 'order.merchant']), function (Show $show) {
            $show->field('id');
            $show->field('order.system_order_no', '系统订单号');
            $show->field('order.merchant.name', '商户');
            $show->field('provider', '充值渠道')->using([
                'unicom' => '联通',
                'mobile' => '移动',
                'telecom' => '电信',
            ]);
            $show->field('provider_order_no', '渠道订单号');
            $show->field('mobile', '手机号');
            $show->field('amount', '充值金额');
            $show->field('status', '状态')->using([
                0 => '待处理',
                1 => '提交中',
                2 => '已提交',
                3 => '处理中',
                10 => '成功',
                20 => '失败',
            ]);
            $show->field('retry_times', '重试次数');
            $show->field('error_code', '错误码');
            $show->field('error_msg', '错误信息');
            $show->field('request_data', '请求数据')->json();
            $show->field('response_data', '响应数据')->json();
            $show->field('submitted_at', '提交时间');
            $show->field('completed_at', '完成时间');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            $show->disableEditButton();
            $show->disableDeleteButton();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RechargeRecord(), function (Form $form) {
            $form->display('id');
            $form->display('mobile');
            $form->display('amount');
            $form->display('status');
            
            $form->disableCreatingCheck();
            $form->disableEditingCheck();
            $form->disableViewCheck();
        });
    }
}