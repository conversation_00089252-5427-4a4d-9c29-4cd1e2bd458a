#!/bin/bash

echo "开始安装Laravel 9兼容的扩展包..."

# API认证和权限
echo "安装API认证和权限管理包..."
composer require laravel/sanctum
composer require spatie/laravel-permission

# 验证和安全
echo "安装验证和安全相关包..."
composer require spatie/laravel-validation-rules

# 数据处理
echo "安装数据处理相关包..."
composer require maatwebsite/excel
composer require spatie/laravel-fractal

# 队列和任务
echo "安装队列监控包..."
composer require laravel/horizon
composer require romanzipp/laravel-queue-monitor

# 开发工具（仅开发环境）
echo "安装开发工具..."
composer require laravel/telescope --dev
composer require barryvdh/laravel-debugbar --dev
composer require rap2hpoutre/laravel-log-viewer

# API文档生成
echo "安装API文档生成工具..."
composer require knuckleswtf/scribe

# 缓存和性能
echo "安装缓存和性能优化包..."
composer require spatie/laravel-responsecache

# 数据库相关
echo "安装数据库相关包..."
composer require spatie/laravel-query-builder

echo "所有包安装完成！"
echo "请运行以下命令完成配置："
echo "php artisan vendor:publish --provider=\"Laravel\Sanctum\SanctumServiceProvider\""
echo "php artisan vendor:publish --provider=\"Spatie\Permission\PermissionServiceProvider\""
echo "php artisan vendor:publish --tag=telescope-migrations"
echo "php artisan vendor:publish --tag=horizon-config"
echo "php artisan migrate"
