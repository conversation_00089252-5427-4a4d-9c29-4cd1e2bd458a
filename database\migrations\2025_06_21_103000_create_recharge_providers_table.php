<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recharge_providers', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->unique()->comment('渠道编码');
            $table->string('name', 100)->comment('渠道名称');
            $table->string('api_url', 255)->comment('API地址');
            $table->string('app_id', 100)->comment('应用ID');
            $table->string('app_secret', 255)->comment('应用密钥');
            $table->json('config')->nullable()->comment('其他配置');
            $table->integer('weight')->default(0)->comment('权重');
            $table->boolean('status')->default(true)->comment('状态：0-禁用，1-启用');
            $table->decimal('success_rate', 5, 2)->nullable()->comment('成功率');
            $table->integer('avg_duration')->nullable()->comment('平均耗时(ms)');
            $table->timestamps();
            
            $table->index(['status', 'weight']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recharge_providers');
    }
};
