<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_vouchers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->comment('用户ID');
            $table->foreignId('order_id')->constrained()->comment('来源订单ID');
            $table->foreignId('product_id')->constrained()->comment('产品ID');
            $table->integer('voucher_value')->comment('券面值');
            $table->string('voucher_code', 32)->unique()->comment('券编码');
            $table->tinyInteger('status')->default(0)->comment('状态：0-未使用，1-已使用，2-已过期');
            $table->foreignId('used_order_id')->nullable()->constrained('orders')->comment('使用订单ID');
            $table->timestamp('used_at')->nullable()->comment('使用时间');
            $table->timestamp('expired_at')->nullable()->comment('过期时间');
            $table->timestamps();
            
            $table->index('user_id');
            $table->index(['status', 'expired_at']);
            $table->index('voucher_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_vouchers');
    }
};