# API接口模块开发文档

## 模块概述

API接口模块为第三方商户提供标准化的RESTful API服务，实现话费充值、余额查询、订单管理等核心功能。采用统一的请求响应格式，支持签名验证和权限控制。

## 技术架构

### API版本管理
```php
// routes/api.php
Route::prefix('v1')->group(function () {
    Route::middleware(['api.auth', 'api.sign'])->group(function () {
        Route::post('/recharge', [RechargeController::class, 'store']);
        Route::post('/balance', [BalanceController::class, 'query']);
        Route::post('/order', [OrderController::class, 'query']);
        // ... 更多路由
    });
});
```

### 统一请求格式
```json
{
    "sign": "32位MD5签名",
    "agentAccount": "商户账号",
    "busiBody": {
        "action": "操作指令",
        // 业务参数
    }
}
```

### 统一响应格式
```json
{
    "code": 1,
    "message": "操作成功",
    "data": {
        // 返回数据
    }
}
```

## 核心功能实现

### 1. 基础控制器

```php
// app/Http/Controllers/Api/BaseController.php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

abstract class BaseController extends Controller
{
    /**
     * 成功响应
     */
    protected function success($data = null, $message = '操作成功'): JsonResponse
    {
        return response()->json([
            'code' => 1,
            'message' => $message,
            'data' => $data,
        ]);
    }
    
    /**
     * 错误响应
     */
    protected function error($code, $message = null): JsonResponse
    {
        $errors = config('api.errors');
        $message = $message ?? ($errors[$code] ?? '未知错误');
        
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => null,
        ]);
    }
    
    /**
     * 获取业务参数
     */
    protected function getBusiBody(): array
    {
        return request()->input('busiBody', []);
    }
}
```

### 2. 签名验证中间件

```php
// app/Http/Middleware/ApiSignature.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Merchant;

class ApiSignature
{
    public function handle(Request $request, Closure $next)
    {
        $sign = $request->input('sign');
        $agentAccount = $request->input('agentAccount');
        $busiBody = $request->input('busiBody', []);
        
        // 验证必要参数
        if (!$sign || !$agentAccount) {
            return $this->errorResponse(-1, '缺少必要参数');
        }
        
        // 获取商户信息
        $merchant = Merchant::where('account', $agentAccount)->first();
        if (!$merchant) {
            return $this->errorResponse(-3, '商户不存在');
        }
        
        // 验证签名
        $calculatedSign = $this->calculateSign($busiBody, $merchant->md5_key);
        if ($sign !== $calculatedSign) {
            return $this->errorResponse(-2, '签名错误');
        }
        
        // 将商户信息存入请求
        $request->merge(['merchant' => $merchant]);
        
        return $next($request);
    }
    
    private function calculateSign($busiBody, $md5Key): string
    {
        $jsonStr = json_encode($busiBody, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        return md5($jsonStr . $md5Key);
    }
    
    private function errorResponse($code, $message)
    {
        return response()->json([
            'code' => $code,
            'message' => $message,
            'data' => null,
        ]);
    }
}
```

### 3. 充值接口实现

```php
// app/Http/Controllers/Api/RechargeController.php
namespace App\Http\Controllers\Api;

use App\Services\RechargeService;
use App\Http\Requests\Api\RechargeRequest;
use Illuminate\Support\Facades\DB;

class RechargeController extends BaseController
{
    private $rechargeService;
    
    public function __construct(RechargeService $rechargeService)
    {
        $this->rechargeService = $rechargeService;
    }
    
    public function store(RechargeRequest $request)
    {
        $busiBody = $this->getBusiBody();
        
        // 验证action
        if ($busiBody['action'] !== 'CZ') {
            return $this->error(-4, 'action错误');
        }
        
        try {
            DB::beginTransaction();
            
            // 检查订单号是否重复
            $exists = Order::where('merchant_id', $request->merchant->id)
                ->where('merchant_order_no', $busiBody['orderId'])
                ->exists();
                
            if ($exists) {
                return $this->error(-12, '订单号重复');
            }
            
            // 创建充值订单
            $order = $this->rechargeService->createOrder([
                'merchant_id' => $request->merchant->id,
                'merchant_order_no' => $busiBody['orderId'],
                'mobile' => $busiBody['prodPhoneNum'],
                'amount' => $busiBody['czMoney'],
                'notify_url' => $busiBody['notifyUrl'] ?? null,
            ]);
            
            // 提交充值
            $result = $this->rechargeService->submitRecharge($order);
            
            DB::commit();
            
            return $this->success([
                'orderId' => $order->merchant_order_no,
                'systemOrderId' => $order->system_order_no,
                'status' => $order->status,
                'message' => '充值提交成功',
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('充值失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->error(-99, '系统错误');
        }
    }
}
```

### 4. 请求验证类

```php
// app/Http/Requests/Api/RechargeRequest.php
namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class RechargeRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            'busiBody.orderId' => 'required|string|max:64',
            'busiBody.prodPhoneNum' => 'required|regex:/^1[3-9]\d{9}$/',
            'busiBody.czMoney' => 'required|integer|in:5,10,20,30,50,100,200,300,500',
            'busiBody.notifyUrl' => 'nullable|url',
        ];
    }
    
    public function messages()
    {
        return [
            'busiBody.orderId.required' => '订单号不能为空',
            'busiBody.prodPhoneNum.required' => '手机号不能为空',
            'busiBody.prodPhoneNum.regex' => '手机号格式错误',
            'busiBody.czMoney.required' => '充值金额不能为空',
            'busiBody.czMoney.in' => '充值金额不支持',
            'busiBody.notifyUrl.url' => '回调地址格式错误',
        ];
    }
}
```

### 5. 余额查询接口

```php
// app/Http/Controllers/Api/BalanceController.php
namespace App\Http\Controllers\Api;

class BalanceController extends BaseController
{
    public function query()
    {
        $busiBody = $this->getBusiBody();
        
        if ($busiBody['action'] !== 'YE') {
            return $this->error(-4, 'action错误');
        }
        
        $merchant = request()->merchant;
        
        return $this->success([
            'balance' => number_format($merchant->balance, 2, '.', ''),
            'frozenBalance' => number_format($merchant->frozen_balance, 2, '.', ''),
            'availableBalance' => number_format($merchant->balance - $merchant->frozen_balance, 2, '.', ''),
        ]);
    }
}
```

### 6. 订单查询接口

```php
// app/Http/Controllers/Api/OrderController.php
namespace App\Http\Controllers\Api;

use App\Models\Order;

class OrderController extends BaseController
{
    public function query()
    {
        $busiBody = $this->getBusiBody();
        
        if ($busiBody['action'] !== 'CX') {
            return $this->error(-4, 'action错误');
        }
        
        $order = Order::where('merchant_id', request()->merchant->id)
            ->where('merchant_order_no', $busiBody['orderId'])
            ->first();
            
        if (!$order) {
            return $this->error(-13, '订单不存在');
        }
        
        return $this->success([
            'orderId' => $order->merchant_order_no,
            'systemOrderId' => $order->system_order_no,
            'status' => $order->status,
            'statusDesc' => $order->getStatusText(),
            'amount' => $order->amount,
            'mobile' => $order->mobile,
            'createdAt' => $order->created_at->format('Y-m-d H:i:s'),
            'completedAt' => $order->completed_at?->format('Y-m-d H:i:s'),
        ]);
    }
}
```

## 服务层设计

### 1. 充值服务

```php
// app/Services/RechargeService.php
namespace App\Services;

use App\Models\Order;
use App\Models\Merchant;
use App\Jobs\ProcessRecharge;
use Illuminate\Support\Str;

class RechargeService
{
    /**
     * 创建充值订单
     */
    public function createOrder(array $data): Order
    {
        $order = new Order();
        $order->system_order_no = $this->generateOrderNo();
        $order->merchant_id = $data['merchant_id'];
        $order->merchant_order_no = $data['merchant_order_no'];
        $order->mobile = $data['mobile'];
        $order->amount = $data['amount'];
        $order->notify_url = $data['notify_url'];
        $order->status = Order::STATUS_PENDING;
        $order->save();
        
        return $order;
    }
    
    /**
     * 提交充值任务
     */
    public function submitRecharge(Order $order): bool
    {
        // 检查余额
        $merchant = Merchant::lockForUpdate()->find($order->merchant_id);
        if ($merchant->balance < $order->amount) {
            throw new \Exception('余额不足');
        }
        
        // 冻结金额
        $merchant->balance -= $order->amount;
        $merchant->frozen_balance += $order->amount;
        $merchant->save();
        
        // 更新订单状态
        $order->status = Order::STATUS_PROCESSING;
        $order->save();
        
        // 投递异步任务
        ProcessRecharge::dispatch($order)->onQueue('recharge');
        
        return true;
    }
    
    /**
     * 生成系统订单号
     */
    private function generateOrderNo(): string
    {
        return date('YmdHis') . Str::random(6);
    }
}
```

### 2. 通知服务

```php
// app/Services/NotificationService.php
namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Facades\Http;

class NotificationService
{
    /**
     * 发送订单状态通知
     */
    public function notifyOrderStatus(Order $order): bool
    {
        if (!$order->notify_url) {
            return true;
        }
        
        $data = [
            'orderId' => $order->merchant_order_no,
            'systemOrderId' => $order->system_order_no,
            'status' => $order->status,
            'statusDesc' => $order->getStatusText(),
            'amount' => $order->amount,
            'mobile' => $order->mobile,
        ];
        
        // 计算签名
        $merchant = $order->merchant;
        $sign = md5(json_encode($data) . $merchant->md5_key);
        
        try {
            $response = Http::timeout(10)->post($order->notify_url, [
                'sign' => $sign,
                'data' => $data,
            ]);
            
            return $response->successful();
        } catch (\Exception $e) {
            \Log::error('通知失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
```

## 异常处理

### 1. API异常处理器

```php
// app/Exceptions/ApiException.php
namespace App\Exceptions;

use Exception;

class ApiException extends Exception
{
    protected $errorCode;
    
    public function __construct($errorCode, $message = null)
    {
        $this->errorCode = $errorCode;
        $errors = config('api.errors');
        $message = $message ?? ($errors[$errorCode] ?? '未知错误');
        
        parent::__construct($message);
    }
    
    public function render()
    {
        return response()->json([
            'code' => $this->errorCode,
            'message' => $this->getMessage(),
            'data' => null,
        ]);
    }
}
```

### 2. 统一异常处理

```php
// app/Exceptions/Handler.php
public function render($request, Throwable $exception)
{
    if ($request->is('api/*')) {
        if ($exception instanceof ValidationException) {
            return response()->json([
                'code' => -5,
                'message' => $exception->errors()[array_key_first($exception->errors())][0],
                'data' => null,
            ]);
        }
        
        if ($exception instanceof ApiException) {
            return $exception->render();
        }
        
        // 生产环境隐藏详细错误
        if (app()->environment('production')) {
            return response()->json([
                'code' => -99,
                'message' => '系统错误',
                'data' => null,
            ]);
        }
    }
    
    return parent::render($request, $exception);
}
```

## 接口测试

### 1. 单元测试

```php
// tests/Feature/Api/RechargeTest.php
namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\Merchant;

class RechargeTest extends TestCase
{
    public function test_recharge_with_valid_data()
    {
        $merchant = Merchant::factory()->create([
            'balance' => 1000,
        ]);
        
        $data = [
            'sign' => $this->generateSign([
                'action' => 'CZ',
                'orderId' => 'TEST001',
                'prodPhoneNum' => '***********',
                'czMoney' => 100,
            ], $merchant->md5_key),
            'agentAccount' => $merchant->account,
            'busiBody' => [
                'action' => 'CZ',
                'orderId' => 'TEST001',
                'prodPhoneNum' => '***********',
                'czMoney' => 100,
            ],
        ];
        
        $response = $this->postJson('/api/v1/recharge', $data);
        
        $response->assertStatus(200)
            ->assertJson([
                'code' => 1,
                'message' => '充值提交成功',
            ]);
            
        $this->assertDatabaseHas('orders', [
            'merchant_order_no' => 'TEST001',
            'amount' => 100,
        ]);
    }
    
    private function generateSign($data, $key)
    {
        return md5(json_encode($data) . $key);
    }
}
```

### 2. 接口文档

使用 OpenAPI 3.0 规范编写接口文档：

```yaml
# docs/openapi.yaml
openapi: 3.0.0
info:
  title: 话费充值API
  version: 1.0.0
servers:
  - url: http://api.example.com/api/v1
paths:
  /recharge:
    post:
      summary: 话费充值
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sign:
                  type: string
                  description: MD5签名
                agentAccount:
                  type: string
                  description: 商户账号
                busiBody:
                  type: object
                  properties:
                    action:
                      type: string
                      enum: [CZ]
                    orderId:
                      type: string
                    prodPhoneNum:
                      type: string
                    czMoney:
                      type: integer
```

## 性能优化

### 1. 接口限流

```php
// app/Http/Kernel.php
protected $middlewareGroups = [
    'api' => [
        'throttle:60,1', // 每分钟60次请求限制
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
    ],
];
```

### 2. 响应缓存

```php
// 使用Redis缓存频繁查询的数据
public function getProductList()
{
    return Cache::remember('api:products', 300, function () {
        return Product::active()->get();
    });
}
```

## 安全措施

1. **IP白名单**：限制API访问IP
2. **签名验证**：防止请求篡改
3. **参数验证**：严格验证输入参数
4. **频率限制**：防止恶意请求
5. **日志审计**：记录所有API请求

## 监控和日志

### 1. 请求日志中间件

```php
// app/Http/Middleware/ApiLogger.php
public function handle($request, Closure $next)
{
    $startTime = microtime(true);
    
    $response = $next($request);
    
    $duration = microtime(true) - $startTime;
    
    \Log::channel('api')->info('API Request', [
        'method' => $request->method(),
        'path' => $request->path(),
        'ip' => $request->ip(),
        'merchant' => $request->input('agentAccount'),
        'duration' => round($duration * 1000, 2) . 'ms',
        'status' => $response->status(),
    ]);
    
    return $response;
}
```

## 下一步计划

1. 实现 GraphQL API
2. 添加 WebSocket 实时通知
3. 支持批量充值接口
4. 接入 API Gateway

---

最后更新：2024-01-20