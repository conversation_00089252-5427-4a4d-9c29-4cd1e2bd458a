# 产品管理模块开发文档

## 模块概述

产品管理模块负责话费券产品套餐的创建、配置和管理。该模块是整个系统的核心，定义了可销售的产品类型、包含的话费券配置、定价策略等关键业务逻辑。

## 数据库设计

### 1. 产品表（products）

```sql
CREATE TABLE `products` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '产品名称',
  `code` varchar(50) NOT NULL COMMENT '产品编码',
  `description` text COMMENT '产品描述',
  `price` decimal(10,2) NOT NULL COMMENT '销售价格',
  `cost` decimal(10,2) NOT NULL DEFAULT 0 COMMENT '成本价格',
  `total_value` decimal(10,2) NOT NULL COMMENT '券总面值',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-下架，1-上架',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `sales_count` int(11) NOT NULL DEFAULT 0 COMMENT '销售数量',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status_sort` (`status`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';
```

### 2. 产品券配置表（product_vouchers）

```sql
CREATE TABLE `product_vouchers` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) UNSIGNED NOT NULL COMMENT '产品ID',
  `voucher_value` int(11) NOT NULL COMMENT '券面值（5,10,20,30,50）',
  `voucher_count` int(11) NOT NULL COMMENT '券数量',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_product_vouchers_product` FOREIGN KEY (`product_id`) 
    REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品券配置表';
```

### 3. 产品规则表（product_rules）

```sql
CREATE TABLE `product_rules` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) UNSIGNED NOT NULL COMMENT '产品ID',
  `rule_type` varchar(50) NOT NULL COMMENT '规则类型：limit_per_user,valid_period等',
  `rule_value` json NOT NULL COMMENT '规则值',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_product_rule` (`product_id`, `rule_type`),
  CONSTRAINT `fk_product_rules_product` FOREIGN KEY (`product_id`) 
    REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品规则表';
```

## 模型设计

### 1. Product 模型

```php
// app/Models/Product.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use SoftDeletes;
    
    protected $fillable = [
        'name', 'code', 'description', 'price', 
        'cost', 'total_value', 'status', 'sort'
    ];
    
    protected $casts = [
        'price' => 'decimal:2',
        'cost' => 'decimal:2',
        'total_value' => 'decimal:2',
        'status' => 'boolean',
    ];
    
    // 状态常量
    const STATUS_OFFLINE = 0;
    const STATUS_ONLINE = 1;
    
    /**
     * 产品包含的券配置
     */
    public function vouchers()
    {
        return $this->hasMany(ProductVoucher::class);
    }
    
    /**
     * 产品规则
     */
    public function rules()
    {
        return $this->hasMany(ProductRule::class);
    }
    
    /**
     * 获取指定规则
     */
    public function getRule($ruleType)
    {
        $rule = $this->rules()->where('rule_type', $ruleType)->first();
        return $rule ? $rule->rule_value : null;
    }
    
    /**
     * 计算利润率
     */
    public function getProfitRateAttribute()
    {
        if ($this->price == 0) return 0;
        return round(($this->price - $this->cost) / $this->price * 100, 2);
    }
    
    /**
     * 作用域：上架产品
     */
    public function scopeOnline($query)
    {
        return $query->where('status', self::STATUS_ONLINE);
    }
    
    /**
     * 检查库存（如果有库存限制）
     */
    public function checkStock(): bool
    {
        $stockLimit = $this->getRule('stock_limit');
        if (!$stockLimit) return true;
        
        return $this->sales_count < $stockLimit['max_stock'];
    }
}
```

### 2. ProductVoucher 模型

```php
// app/Models/ProductVoucher.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductVoucher extends Model
{
    protected $fillable = [
        'product_id', 'voucher_value', 'voucher_count'
    ];
    
    // 支持的券面值
    const VALID_VALUES = [5, 10, 20, 30, 50];
    
    /**
     * 所属产品
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
    
    /**
     * 获取券总价值
     */
    public function getTotalValueAttribute()
    {
        return $this->voucher_value * $this->voucher_count;
    }
}
```

## 服务层实现

### 1. 产品服务

```php
// app/Services/ProductService.php
namespace App\Services;

use App\Models\Product;
use App\Models\ProductVoucher;
use Illuminate\Support\Facades\DB;
use App\Exceptions\BusinessException;

class ProductService
{
    /**
     * 创建产品
     */
    public function create(array $data): Product
    {
        DB::beginTransaction();
        try {
            // 创建产品
            $product = Product::create([
                'name' => $data['name'],
                'code' => $data['code'],
                'description' => $data['description'] ?? '',
                'price' => $data['price'],
                'cost' => $data['cost'] ?? 0,
                'total_value' => 0, // 稍后计算
                'status' => $data['status'] ?? Product::STATUS_OFFLINE,
                'sort' => $data['sort'] ?? 0,
            ]);
            
            // 创建券配置
            $totalValue = 0;
            foreach ($data['vouchers'] as $voucher) {
                ProductVoucher::create([
                    'product_id' => $product->id,
                    'voucher_value' => $voucher['value'],
                    'voucher_count' => $voucher['count'],
                ]);
                $totalValue += $voucher['value'] * $voucher['count'];
            }
            
            // 更新总面值
            $product->update(['total_value' => $totalValue]);
            
            // 创建规则
            if (!empty($data['rules'])) {
                foreach ($data['rules'] as $ruleType => $ruleValue) {
                    $product->rules()->create([
                        'rule_type' => $ruleType,
                        'rule_value' => $ruleValue,
                    ]);
                }
            }
            
            DB::commit();
            return $product;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 更新产品
     */
    public function update(Product $product, array $data): Product
    {
        DB::beginTransaction();
        try {
            // 更新基本信息
            $product->update([
                'name' => $data['name'] ?? $product->name,
                'description' => $data['description'] ?? $product->description,
                'price' => $data['price'] ?? $product->price,
                'cost' => $data['cost'] ?? $product->cost,
                'status' => $data['status'] ?? $product->status,
                'sort' => $data['sort'] ?? $product->sort,
            ]);
            
            // 更新券配置
            if (isset($data['vouchers'])) {
                $product->vouchers()->delete();
                $totalValue = 0;
                foreach ($data['vouchers'] as $voucher) {
                    ProductVoucher::create([
                        'product_id' => $product->id,
                        'voucher_value' => $voucher['value'],
                        'voucher_count' => $voucher['count'],
                    ]);
                    $totalValue += $voucher['value'] * $voucher['count'];
                }
                $product->update(['total_value' => $totalValue]);
            }
            
            DB::commit();
            return $product->fresh();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
    
    /**
     * 上下架产品
     */
    public function toggleStatus(Product $product): bool
    {
        $newStatus = $product->status == Product::STATUS_ONLINE 
            ? Product::STATUS_OFFLINE 
            : Product::STATUS_ONLINE;
            
        return $product->update(['status' => $newStatus]);
    }
    
    /**
     * 获取可售产品列表
     */
    public function getAvailableProducts()
    {
        return Product::online()
            ->with(['vouchers', 'rules'])
            ->orderBy('sort', 'asc')
            ->orderBy('id', 'desc')
            ->get()
            ->filter(function ($product) {
                return $product->checkStock();
            });
    }
}
```

### 2. 产品验证服务

```php
// app/Services/ProductValidationService.php
namespace App\Services;

use App\Models\Product;
use App\Models\Order;
use App\Exceptions\BusinessException;

class ProductValidationService
{
    /**
     * 验证用户是否可以购买产品
     */
    public function validatePurchase(Product $product, $userId): void
    {
        // 检查产品状态
        if ($product->status != Product::STATUS_ONLINE) {
            throw new BusinessException('产品已下架');
        }
        
        // 检查库存
        if (!$product->checkStock()) {
            throw new BusinessException('产品库存不足');
        }
        
        // 检查购买限制
        $this->checkPurchaseLimit($product, $userId);
        
        // 检查时间限制
        $this->checkTimeLimit($product);
    }
    
    /**
     * 检查购买限制
     */
    private function checkPurchaseLimit(Product $product, $userId): void
    {
        $limitRule = $product->getRule('limit_per_user');
        if (!$limitRule) return;
        
        $purchaseCount = Order::where('user_id', $userId)
            ->where('product_id', $product->id)
            ->whereIn('status', [Order::STATUS_PAID, Order::STATUS_COMPLETED])
            ->count();
            
        if ($purchaseCount >= $limitRule['max_count']) {
            throw new BusinessException('已达到购买限制');
        }
    }
    
    /**
     * 检查时间限制
     */
    private function checkTimeLimit(Product $product): void
    {
        $timeRule = $product->getRule('sale_period');
        if (!$timeRule) return;
        
        $now = now();
        $startTime = \Carbon\Carbon::parse($timeRule['start_time']);
        $endTime = \Carbon\Carbon::parse($timeRule['end_time']);
        
        if ($now < $startTime) {
            throw new BusinessException('活动尚未开始');
        }
        
        if ($now > $endTime) {
            throw new BusinessException('活动已结束');
        }
    }
}
```

## 后台管理实现

### 1. 产品管理控制器

```php
// app/Admin/Controllers/ProductController.php
namespace App\Admin\Controllers;

use App\Admin\Repositories\Product;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ProductController extends AdminController
{
    protected function grid()
    {
        return Grid::make(new Product(['vouchers']), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('code', '产品编码');
            $grid->column('name', '产品名称');
            $grid->column('price', '销售价格')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('cost', '成本价格')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('total_value', '券总面值')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('profit_rate', '利润率')->display(function () {
                return $this->profit_rate . '%';
            })->label();
            $grid->column('vouchers', '券配置')->display(function ($vouchers) {
                return collect($vouchers)->map(function ($v) {
                    return "{$v['voucher_value']}元×{$v['voucher_count']}张";
                })->implode('<br>');
            });
            $grid->column('status', '状态')->switch();
            $grid->column('sales_count', '销售数量');
            $grid->column('created_at', '创建时间');
            
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append(new CopyProductAction());
            });
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '产品名称');
                $filter->like('code', '产品编码');
                $filter->equal('status', '状态')->select([
                    0 => '下架',
                    1 => '上架',
                ]);
            });
        });
    }
    
    protected function form()
    {
        return Form::make(new Product(), function (Form $form) {
            $form->display('id');
            $form->text('code', '产品编码')->required()
                ->creationRules(['required', 'unique:products'])
                ->updateRules(['required', "unique:products,code,{{id}}"]);
            $form->text('name', '产品名称')->required();
            $form->textarea('description', '产品描述');
            $form->currency('price', '销售价格')->symbol('¥')->required();
            $form->currency('cost', '成本价格')->symbol('¥')->default(0);
            $form->switch('status', '状态')->default(0);
            $form->number('sort', '排序')->default(0);
            
            // 券配置
            $form->table('vouchers', '券配置', function (Form\NestedForm $table) {
                $table->select('voucher_value', '面值')->options([
                    5 => '5元',
                    10 => '10元',
                    20 => '20元',
                    30 => '30元',
                    50 => '50元',
                ])->required();
                $table->number('voucher_count', '数量')->min(1)->required();
            });
            
            // 产品规则
            $form->fieldset('产品规则', function (Form $form) {
                $form->number('limit_per_user', '每人限购')->min(0)
                    ->help('0表示不限制');
                $form->number('stock_limit', '库存限制')->min(0)
                    ->help('0表示不限制');
                $form->datetimeRange('sale_period.start_time', 'sale_period.end_time', '销售时间');
            });
            
            $form->display('created_at');
            $form->display('updated_at');
            
            // 保存前回调
            $form->saving(function (Form $form) {
                if ($form->vouchers) {
                    $totalValue = collect($form->vouchers)->sum(function ($item) {
                        return $item['voucher_value'] * $item['voucher_count'];
                    });
                    $form->total_value = $totalValue;
                }
            });
        });
    }
}
```

### 2. 产品复制操作

```php
// app/Admin/Actions/CopyProductAction.php
namespace App\Admin\Actions;

use Dcat\Admin\Actions\RowAction;
use App\Models\Product;

class CopyProductAction extends RowAction
{
    protected $title = '复制';

    public function handle()
    {
        $product = Product::with(['vouchers', 'rules'])->find($this->getKey());
        
        $newProduct = $product->replicate();
        $newProduct->code = $product->code . '_copy_' . time();
        $newProduct->name = $product->name . ' (副本)';
        $newProduct->status = Product::STATUS_OFFLINE;
        $newProduct->sales_count = 0;
        $newProduct->save();
        
        // 复制券配置
        foreach ($product->vouchers as $voucher) {
            $newProduct->vouchers()->create($voucher->toArray());
        }
        
        // 复制规则
        foreach ($product->rules as $rule) {
            $newProduct->rules()->create($rule->toArray());
        }
        
        return $this->response()
            ->success('复制成功')
            ->redirect('/admin/products');
    }
}
```

## API接口实现

### 1. 产品列表接口

```php
// app/Http/Controllers/Api/ProductController.php
namespace App\Http\Controllers\Api;

use App\Services\ProductService;

class ProductController extends BaseController
{
    private $productService;
    
    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }
    
    public function index()
    {
        $busiBody = $this->getBusiBody();
        
        if ($busiBody['action'] !== 'CHECK_SPU') {
            return $this->error(-4, 'action错误');
        }
        
        $products = $this->productService->getAvailableProducts();
        
        $data = $products->map(function ($product) {
            return [
                'productId' => $product->id,
                'productCode' => $product->code,
                'productName' => $product->name,
                'price' => $product->price,
                'totalValue' => $product->total_value,
                'vouchers' => $product->vouchers->map(function ($v) {
                    return [
                        'value' => $v->voucher_value,
                        'count' => $v->voucher_count,
                    ];
                }),
            ];
        });
        
        return $this->success([
            'products' => $data,
            'total' => $data->count(),
        ]);
    }
}
```

## 业务规则

### 1. 产品定价规则
- 销售价格必须大于0
- 成本价格用于计算利润率
- 总面值自动计算，不可手动修改

### 2. 券配置规则
- 支持的面值：5、10、20、30、50元
- 每种面值可配置多张
- 至少配置一种券

### 3. 购买限制规则
- 每人限购：限制单个用户购买次数
- 库存限制：限制产品总销售数量
- 时间限制：限制销售时间段

## 性能优化

### 1. 查询优化
```php
// 预加载关联数据
Product::with(['vouchers', 'rules'])->get();

// 缓存热门产品
Cache::remember('hot_products', 3600, function () {
    return Product::online()
        ->orderBy('sales_count', 'desc')
        ->limit(10)
        ->get();
});
```

### 2. 索引优化
```sql
-- 添加复合索引
ALTER TABLE products ADD INDEX idx_status_sort_id(status, sort, id);
ALTER TABLE product_vouchers ADD INDEX idx_product_value(product_id, voucher_value);
```

## 下一步计划

1. 实现产品推荐算法
2. 添加产品标签功能
3. 支持产品组合销售
4. 实现动态定价策略

---

最后更新：2024-01-20