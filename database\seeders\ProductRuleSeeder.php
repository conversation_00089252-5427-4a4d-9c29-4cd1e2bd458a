<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductRule;
use Illuminate\Database\Seeder;

class ProductRuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取所有产品
        $products = Product::all();
        
        if ($products->isEmpty()) {
            $this->command->info('没有找到产品，请先创建产品数据');
            return;
        }

        foreach ($products as $product) {
            // 为每个产品创建一些示例规则
            $this->createRulesForProduct($product);
        }
    }

    /**
     * 为产品创建规则
     */
    private function createRulesForProduct(Product $product): void
    {
        $rules = [
            // 每人限购规则
            [
                'product_id' => $product->id,
                'rule_type' => ProductRule::TYPE_LIMIT_PER_USER,
                'rule_value' => [
                    'max_count' => 5,
                    'period' => 'month', // 每月限购
                ],
            ],
            
            // 库存限制规则
            [
                'product_id' => $product->id,
                'rule_type' => ProductRule::TYPE_STOCK_LIMIT,
                'rule_value' => [
                    'max_stock' => 1000,
                ],
            ],
            
            // 销售时间段规则
            [
                'product_id' => $product->id,
                'rule_type' => ProductRule::TYPE_SALE_PERIOD,
                'rule_value' => [
                    'start_time' => now()->format('Y-m-d H:i:s'),
                    'end_time' => now()->addMonths(3)->format('Y-m-d H:i:s'),
                ],
            ],
            
            // 券有效期规则
            [
                'product_id' => $product->id,
                'rule_type' => ProductRule::TYPE_VALID_PERIOD,
                'rule_value' => [
                    'days' => 365,
                    'from_purchase' => true, // 从购买日期开始计算
                ],
            ],
            
            // 最小购买量规则
            [
                'product_id' => $product->id,
                'rule_type' => ProductRule::TYPE_MIN_PURCHASE,
                'rule_value' => [
                    'min_quantity' => 1,
                ],
            ],
            
            // 最大购买量规则
            [
                'product_id' => $product->id,
                'rule_type' => ProductRule::TYPE_MAX_PURCHASE,
                'rule_value' => [
                    'max_quantity' => 10,
                ],
            ],
        ];

        foreach ($rules as $rule) {
            ProductRule::create($rule);
        }
    }
}
