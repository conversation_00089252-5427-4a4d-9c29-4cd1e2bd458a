<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Recharge\RechargeServiceInterface;
use App\Services\Recharge\RechargeService as RechargeApiService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // 绑定充值服务接口
        $this->app->bind(RechargeServiceInterface::class, RechargeApiService::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
