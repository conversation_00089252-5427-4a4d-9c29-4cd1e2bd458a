<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Product;
use App\Models\ProductVoucher;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ProductController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Product(['vouchers']), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('code', '产品编码');
            $grid->column('name', '产品名称');
            $grid->column('price', '销售价格')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('cost', '成本价格')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('total_value', '券总面值')->display(function ($value) {
                return '¥' . $value;
            });
            $grid->column('profit_rate', '利润率')->display(function () {
                return $this->profit_rate . '%';
            })->label();
            $grid->column('vouchers', '券配置')->display(function ($vouchers) {
                return collect($vouchers)->map(function ($v) {
                    return "{$v['voucher_value']}元×{$v['voucher_count']}张";
                })->implode('<br>');
            });
            $grid->column('status', '状态')->switch();
            $grid->column('sales_count', '销售数量');
            $grid->column('sort', '排序');
            $grid->column('created_at', '创建时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '产品名称');
                $filter->like('code', '产品编码');
                $filter->equal('status', '状态')->select([0 => '下架', 1 => '上架']);
            });

            $grid->quickSearch(['name', 'code']);
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Product(['vouchers']), function (Show $show) {
            $show->field('id');
            $show->field('code', '产品编码');
            $show->field('name', '产品名称');
            $show->field('description', '产品描述');
            $show->field('price', '销售价格');
            $show->field('cost', '成本价格');
            $show->field('total_value', '券总面值');
            $show->field('profit_rate', '利润率')->as(function () {
                return $this->profit_rate . '%';
            });
            $show->field('status', '状态')->using([0 => '下架', 1 => '上架']);
            $show->field('sort', '排序');
            $show->field('sales_count', '销售数量');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');

            $show->relation('vouchers', '券配置', function ($model) {
                $grid = new Grid(new \App\Admin\Repositories\ProductVoucher);
                $grid->model()->where('product_id', $model->id);
                
                $grid->voucher_value('面值')->display(function ($value) {
                    return $value . '元';
                });
                $grid->voucher_count('数量');
                $grid->total_value('总价值')->display(function () {
                    return '¥' . $this->total_value;
                });

                $grid->disableActions();
                $grid->disableCreateButton();
                $grid->disableRefreshButton();
                $grid->disableFilterButton();

                return $grid;
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Product(), function (Form $form) {
            $form->display('id');
            $form->text('code', '产品编码')->required()
                ->creationRules(['required', 'unique:products'])
                ->updateRules(['required', "unique:products,code,{{id}}"]);
            $form->text('name', '产品名称')->required();
            $form->textarea('description', '产品描述');
            $form->currency('price', '销售价格')->symbol('¥')->required();
            $form->currency('cost', '成本价格')->symbol('¥')->default(0);
            $form->switch('status', '状态')->default(0);
            $form->number('sort', '排序')->default(0);

            // 券配置
            $form->hasMany('vouchers', '券配置', function (Form\NestedForm $form) {
                $form->select('voucher_value', '面值')->options([
                    5 => '5元',
                    10 => '10元',
                    20 => '20元',
                    30 => '30元',
                    50 => '50元',
                ])->required();
                $form->number('voucher_count', '数量')->min(1)->required();
            })->useTable();

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');

            // 保存前回调
            $form->saving(function (Form $form) {
                // 计算券总面值
                $totalValue = 0;
                if ($form->vouchers) {
                    foreach ($form->vouchers as $voucher) {
                        if (!isset($voucher['_remove_']) || !$voucher['_remove_']) {
                            $totalValue += $voucher['voucher_value'] * $voucher['voucher_count'];
                        }
                    }
                }
                $form->total_value = $totalValue;
            });
        });
    }
}