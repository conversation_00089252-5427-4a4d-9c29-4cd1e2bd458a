<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RechargeProvider extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'api_url',
        'app_id',
        'app_secret',
        'config',
        'weight',
        'status',
        'success_rate',
        'avg_duration',
    ];

    protected $casts = [
        'config' => 'json',
        'status' => 'boolean',
        'success_rate' => 'decimal:2',
    ];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * 获取配置项
     */
    public function getConfig($key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * 更新统计信息
     */
    public function updateStats(): void
    {
        $stats = RechargeRecord::where('provider', $this->code)
            ->where('created_at', '>=', now()->subDays(7))
            ->selectRaw('
                COUNT(*) as total,
                SUM(status = ?) as success,
                AVG(TIMESTAMPDIFF(MICROSECOND, submitted_at, completed_at)) as avg_duration
            ', [RechargeRecord::STATUS_SUCCESS])
            ->first();
            
        if ($stats->total > 0) {
            $this->update([
                'success_rate' => ($stats->success / $stats->total) * 100,
                'avg_duration' => $stats->avg_duration / 1000, // 转换为毫秒
            ]);
        }
    }

    /**
     * 作用域：启用的渠道
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 作用域：按权重排序
     */
    public function scopeOrderByWeight($query)
    {
        return $query->orderBy('weight', 'desc');
    }

    /**
     * 关联充值记录
     */
    public function rechargeRecords()
    {
        return $this->hasMany(RechargeRecord::class, 'provider', 'code');
    }
}
