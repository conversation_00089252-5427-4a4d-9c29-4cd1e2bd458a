<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RechargeProvider extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'api_url',
        'app_id',
        'app_secret',
        'config',
        'supported_carriers',
        'supported_amounts',
        'min_amount',
        'max_amount',
        'weight',
        'status',
        'success_rate',
        'avg_duration',
        'balance',
        'daily_limit',
        'monthly_limit',
    ];

    protected $casts = [
        'config' => 'json',
        'supported_carriers' => 'array',
        'supported_amounts' => 'array',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'status' => 'boolean',
        'success_rate' => 'decimal:2',
        'balance' => 'decimal:2',
        'daily_limit' => 'decimal:2',
        'monthly_limit' => 'decimal:2',
    ];

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * 获取配置项
     */
    public function getConfig($key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }

    /**
     * 更新统计信息
     */
    public function updateStats(): void
    {
        $stats = RechargeRecord::where('provider', $this->code)
            ->where('created_at', '>=', now()->subDays(7))
            ->selectRaw('
                COUNT(*) as total,
                SUM(status = ?) as success,
                AVG(TIMESTAMPDIFF(MICROSECOND, submitted_at, completed_at)) as avg_duration
            ', [RechargeRecord::STATUS_SUCCESS])
            ->first();
            
        if ($stats->total > 0) {
            $this->update([
                'success_rate' => ($stats->success / $stats->total) * 100,
                'avg_duration' => $stats->avg_duration / 1000, // 转换为毫秒
            ]);
        }
    }

    /**
     * 作用域：启用的渠道
     */
    public function scopeEnabled($query)
    {
        return $query->where('status', self::STATUS_ENABLED);
    }

    /**
     * 作用域：按权重排序
     */
    public function scopeOrderByWeight($query)
    {
        return $query->orderBy('weight', 'desc');
    }

    /**
     * 关联充值记录
     */
    public function rechargeRecords()
    {
        return $this->hasMany(RechargeRecord::class, 'provider', 'code');
    }

    /**
     * 是否启用
     */
    public function isEnabled(): bool
    {
        return $this->status === self::STATUS_ENABLED;
    }

    /**
     * 是否支持指定运营商
     */
    public function supportsCarrier(string $carrier): bool
    {
        return in_array($carrier, $this->supported_carriers ?? []);
    }

    /**
     * 是否支持指定金额
     */
    public function supportsAmount(float $amount): bool
    {
        // 检查金额范围
        if ($this->min_amount && $amount < $this->min_amount) {
            return false;
        }
        if ($this->max_amount && $amount > $this->max_amount) {
            return false;
        }

        // 检查支持的金额列表
        if (!empty($this->supported_amounts)) {
            return in_array($amount, $this->supported_amounts);
        }

        return true;
    }

    /**
     * 检查余额是否充足
     */
    public function hasEnoughBalance(float $amount): bool
    {
        return $this->balance >= $amount;
    }

    /**
     * 是否可用
     */
    public function isAvailable(float $amount = 0, string $carrier = ''): bool
    {
        // 检查状态
        if (!$this->isEnabled()) {
            return false;
        }

        // 检查金额支持
        if ($amount > 0 && !$this->supportsAmount($amount)) {
            return false;
        }

        // 检查运营商支持
        if ($carrier && !$this->supportsCarrier($carrier)) {
            return false;
        }

        // 检查余额
        if ($amount > 0 && !$this->hasEnoughBalance($amount)) {
            return false;
        }

        return true;
    }
}
