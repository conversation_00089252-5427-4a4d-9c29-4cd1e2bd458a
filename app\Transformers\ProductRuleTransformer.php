<?php

namespace App\Transformers;

use App\Models\ProductRule;
use League\Fractal\TransformerAbstract;

class ProductRuleTransformer extends TransformerAbstract
{
    /**
     * 转换产品规则数据
     */
    public function transform(ProductRule $rule): array
    {
        return [
            'id' => $rule->id,
            'rule_type' => $rule->rule_type,
            'type_text' => $rule->type_text,
            'rule_value' => $rule->rule_value,
            'description' => $rule->description,
        ];
    }
}
