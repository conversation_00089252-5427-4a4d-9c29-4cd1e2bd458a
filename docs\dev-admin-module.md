# 后台管理模块开发文档

## 模块概述

后台管理模块基于 Dcat Admin 构建，提供系统的可视化管理界面。本模块负责所有后台功能的实现，包括用户管理、权限控制、数据统计等。

## 技术架构

### Dcat Admin 配置
```php
// config/admin.php
return [
    // 应用名称
    'name' => '话费充值管理系统',
    
    // 应用logo
    'logo' => '<img src="/images/logo.png">',
    
    // 路由配置
    'route' => [
        'prefix' => env('ADMIN_ROUTE_PREFIX', 'admin'),
        'namespace' => 'App\\Admin\\Controllers',
        'middleware' => ['web', 'admin'],
    ],
    
    // 安装目录
    'directory' => app_path('Admin'),
    
    // 标题
    'title' => '话费充值管理系统',
];
```

### 目录结构
```
app/Admin/
├── Controllers/          # 控制器
│   ├── HomeController.php
│   ├── UserController.php
│   ├── ProductController.php
│   ├── OrderController.php
│   └── ...
├── Extensions/          # 扩展组件
├── Forms/              # 表单类
├── Metrics/            # 数据统计卡片
├── Repositories/       # 数据仓库
├── bootstrap.php       # 启动文件
└── routes.php         # 路由配置
```

## 核心功能实现

### 1. 仪表盘（Dashboard）

```php
// app/Admin/Controllers/HomeController.php
namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Http\Controllers\Controller;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;

class HomeController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->header('仪表盘')
            ->description('系统概览')
            ->body(function (Row $row) {
                $row->column(3, new Examples\TotalUsers());
                $row->column(3, new Examples\NewOrders());
                $row->column(3, new Examples\TotalRevenue());
                $row->column(3, new Examples\RechargeSuccess());
            })
            ->body(function (Row $row) {
                $row->column(6, new Examples\OrderChart());
                $row->column(6, new Examples\RevenueChart());
            });
    }
}
```

### 2. 用户管理

```php
// app/Admin/Controllers/UserController.php
namespace App\Admin\Controllers;

use App\Admin\Repositories\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserController extends AdminController
{
    protected function grid()
    {
        return Grid::make(new User(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('username', '用户名');
            $grid->column('mobile', '手机号');
            $grid->column('balance', '余额')->display(function ($value) {
                return '¥' . number_format($value, 2);
            });
            $grid->column('status', '状态')->using([
                0 => '禁用',
                1 => '正常',
            ])->dot([
                0 => 'danger',
                1 => 'success',
            ]);
            $grid->column('created_at', '注册时间');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('username');
                $filter->like('mobile');
                $filter->equal('status')->select([
                    0 => '禁用',
                    1 => '正常',
                ]);
                $filter->between('created_at')->datetime();
            });
        });
    }
    
    protected function form()
    {
        return Form::make(new User(), function (Form $form) {
            $form->display('id');
            $form->text('username')->required();
            $form->mobile('mobile')->required();
            $form->decimal('balance')->default(0);
            $form->switch('status')->default(1);
            
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
```

### 3. 权限管理集成

```php
// app/Admin/bootstrap.php
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;

Admin::menu()->add([
    [
        'id' => 1,
        'title' => '仪表盘',
        'icon' => 'feather icon-bar-chart-2',
        'uri' => '/',
        'parent_id' => 0,
    ],
    [
        'id' => 2,
        'title' => '用户管理',
        'icon' => 'feather icon-users',
        'uri' => '',
        'parent_id' => 0,
    ],
    [
        'id' => 21,
        'title' => '用户列表',
        'uri' => 'users',
        'parent_id' => 2,
    ],
    [
        'id' => 3,
        'title' => '产品管理',
        'icon' => 'feather icon-package',
        'uri' => '',
        'parent_id' => 0,
    ],
    // ... 更多菜单项
]);
```

## 自定义组件

### 1. 统计卡片组件

```php
// app/Admin/Metrics/TotalUsers.php
namespace App\Admin\Metrics;

use Dcat\Admin\Widgets\Metrics\Card;
use Illuminate\Contracts\Support\Renderable;
use App\Models\User;

class TotalUsers extends Card
{
    protected $label = '用户总数';

    protected function init()
    {
        parent::init();

        $this->title($this->label);
        $this->dropdown([
            '7' => '最近7天',
            '30' => '最近30天',
            '365' => '最近一年',
        ]);
    }

    public function handle(Request $request)
    {
        $days = $request->get('option', 30);
        
        $total = User::query()
            ->when($days != 365, function ($query) use ($days) {
                $query->where('created_at', '>=', now()->subDays($days));
            })
            ->count();

        $this->content($total);
    }

    public function renderContent()
    {
        $content = parent::renderContent();

        return <<<HTML
<div class="d-flex justify-content-between align-items-center mt-1" style="margin-bottom: 2px">
    <h2 class="ml-1 font-large-1">{$content}</h2>
    <span class="feather icon-users font-medium-5"></span>
</div>
HTML;
    }
}
```

### 2. 自定义表单组件

```php
// app/Admin/Extensions/Form/WangEditor.php
namespace App\Admin\Extensions\Form;

use Dcat\Admin\Form\Field;

class WangEditor extends Field
{
    protected $view = 'admin.wang-editor';

    protected static $css = [
        '/vendor/wangeditor/wangEditor.min.css',
    ];

    protected static $js = [
        '/vendor/wangeditor/wangEditor.min.js',
    ];

    public function render()
    {
        $this->script = <<<EOT
var E = window.wangEditor;
var editor = new E('#{$this->id}');
editor.customConfig.onchange = function (html) {
    $('input[name="{$this->column}"]').val(html);
}
editor.create();
EOT;
        return parent::render();
    }
}
```

## 开发规范

### 1. 控制器规范
- 继承 `AdminController` 基类
- 使用 Repository 模式处理数据
- Grid、Form、Show 方法分离
- 适当使用 trait 复用代码

### 2. 表单验证
```php
protected function form()
{
    return Form::make(new Product(), function (Form $form) {
        $form->text('name')->required()
            ->rules('unique:products,name,{{id}}')
            ->help('产品名称必须唯一');
            
        $form->decimal('price')->required()
            ->rules('min:0.01')
            ->help('价格必须大于0');
            
        $form->saving(function (Form $form) {
            // 保存前的数据处理
        });
        
        $form->saved(function (Form $form) {
            // 保存后的操作
        });
    });
}
```

### 3. 数据表格优化
```php
protected function grid()
{
    return Grid::make(new Order(), function (Grid $grid) {
        // 默认排序
        $grid->model()->orderBy('id', 'desc');
        
        // 禁用不需要的功能
        $grid->disableCreateButton();
        $grid->disableEditButton();
        
        // 批量操作
        $grid->batchActions(function ($batch) {
            $batch->disableDelete();
        });
        
        // 工具栏
        $grid->tools(function ($tools) {
            $tools->append(new ExportButton());
        });
        
        // 行操作
        $grid->actions(function ($actions) {
            $actions->append(new RechargeAction());
        });
    });
}
```

## 性能优化

### 1. 查询优化
```php
// 使用预加载减少查询
$grid->model()->with(['user', 'product']);

// 只查询需要的字段
$grid->model()->select(['id', 'order_no', 'amount', 'status']);

// 使用索引
Schema::table('orders', function (Blueprint $table) {
    $table->index(['user_id', 'status']);
    $table->index('created_at');
});
```

### 2. 缓存策略
```php
// 菜单缓存
Admin::menu()->cache();

// 权限缓存
Admin::permission()->cache();

// 数据缓存
$data = Cache::remember('dashboard_stats', 300, function () {
    return [
        'users' => User::count(),
        'orders' => Order::count(),
        'revenue' => Order::sum('amount'),
    ];
});
```

## 安全措施

### 1. 权限控制
```php
// 路由权限
$router->resource('users', UserController::class)
    ->middleware('admin.permission:user.management');

// 方法级权限
public function destroy($id)
{
    if (!Admin::user()->can('user.delete')) {
        return $this->response()->error('无权限')->send();
    }
    // 删除逻辑
}
```

### 2. 操作日志
```php
// 自动记录操作日志
Admin::enableLogs();

// 自定义日志
Admin::log([
    'user_id' => Admin::user()->id,
    'path' => request()->path(),
    'method' => request()->method(),
    'ip' => request()->ip(),
    'input' => request()->all(),
]);
```

## 常见问题

### 1. 自定义主题
```php
// config/admin.php
'layout' => [
    'color' => 'blue-light',
    'body_class' => ['sidebar-mini'],
    'horizontal_menu' => false,
    'sidebar_collapsed' => false,
]
```

### 2. 多语言支持
```php
// 创建语言文件
// resources/lang/zh_CN/admin.php
return [
    'users' => '用户管理',
    'products' => '产品管理',
    'orders' => '订单管理',
];

// 使用
$grid->column('name', admin_trans('admin.users'));
```

## 下一步计划

1. 实现数据导出功能
2. 添加图表统计组件
3. 优化移动端适配
4. 集成消息通知系统

---

最后更新：2024-01-20