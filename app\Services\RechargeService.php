<?php

namespace App\Services;

use App\Models\Order;
use App\Models\RechargeRecord;
use App\Models\RechargeProvider;
use App\Services\Recharge\RechargeServiceInterface;
use App\Services\Recharge\Requests\RechargeRequest;
use App\Services\Recharge\Requests\OrderQueryRequest;
use App\Services\Recharge\SignatureService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RechargeService
{
    protected $orderService;
    protected $mobileCarrierService;
    protected $rechargeApiService;

    public function __construct(
        OrderService $orderService,
        MobileCarrierService $mobileCarrierService,
        RechargeServiceInterface $rechargeApiService
    ) {
        $this->orderService = $orderService;
        $this->mobileCarrierService = $mobileCarrierService;
        $this->rechargeApiService = $rechargeApiService;
    }

    /**
     * 处理充值订单
     */
    public function processRechargeOrder(Order $order): array
    {
        try {
            DB::beginTransaction();

            // 检查订单状态
            if ($order->status !== Order::STATUS_PAID) {
                return ['success' => false, 'message' => '订单状态不正确'];
            }

            if ($order->type !== Order::TYPE_RECHARGE) {
                return ['success' => false, 'message' => '不是充值订单'];
            }

            // 获取手机号运营商信息
            $carrierInfo = $this->mobileCarrierService->getCarrierInfo($order->mobile);
            if ($carrierInfo['carrier'] === 'unknown') {
                return ['success' => false, 'message' => '不支持的手机号段'];
            }

            // 检查充值限制
            $limitCheck = $this->checkRechargeLimit($order->mobile, $order->amount);
            if (!$limitCheck['allowed']) {
                return ['success' => false, 'message' => $limitCheck['message']];
            }

            // 选择充值渠道
            $provider = $this->selectRechargeProvider($order->mobile, $order->amount);
            if (!$provider) {
                return ['success' => false, 'message' => '暂无可用的充值渠道'];
            }

            // 更新订单状态为充值中
            $this->orderService->updateOrderStatus($order, Order::STATUS_RECHARGING, 'system', '开始充值');

            // 创建充值记录
            $rechargeRecord = RechargeRecord::create([
                'order_id' => $order->id,
                'provider_id' => $provider->id,
                'provider_order_no' => $this->generateProviderOrderNo(),
                'mobile' => $order->mobile,
                'amount' => $order->amount,
                'carrier' => $carrierInfo['carrier'],
                'province' => $carrierInfo['province'],
                'city' => $carrierInfo['city'],
                'status' => RechargeRecord::STATUS_PENDING,
            ]);

            // 调用充值接口
            $rechargeResult = $this->callRechargeApi($provider, $rechargeRecord);

            if ($rechargeResult['success']) {
                // 充值成功
                $rechargeRecord->update([
                    'status' => RechargeRecord::STATUS_SUCCESS,
                    'provider_response' => $rechargeResult['response'],
                    'charge_id' => $rechargeResult['charge_id'] ?? null,
                    'completed_at' => now(),
                ]);

                $this->orderService->updateOrderStatus($order, Order::STATUS_SUCCESS, 'system', '充值成功');

                DB::commit();

                Log::info('充值成功', [
                    'order_id' => $order->id,
                    'recharge_record_id' => $rechargeRecord->id,
                    'mobile' => $order->mobile,
                    'amount' => $order->amount,
                ]);

                return [
                    'success' => true,
                    'recharge_record' => $rechargeRecord,
                    'message' => '充值成功',
                ];
            } else {
                // 充值失败
                $rechargeRecord->update([
                    'status' => RechargeRecord::STATUS_FAILED,
                    'provider_response' => $rechargeResult['response'],
                    'error_message' => $rechargeResult['message'],
                ]);

                $this->orderService->updateOrderStatus($order, Order::STATUS_FAILED, 'system', '充值失败：' . $rechargeResult['message']);

                // 处理退款（如果是商户订单）
                if ($order->merchant_id) {
                    $this->processRefund($order, '充值失败退款');
                }

                DB::commit();

                Log::warning('充值失败', [
                    'order_id' => $order->id,
                    'recharge_record_id' => $rechargeRecord->id,
                    'error' => $rechargeResult['message'],
                ]);

                return [
                    'success' => false,
                    'recharge_record' => $rechargeRecord,
                    'message' => $rechargeResult['message'],
                ];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('处理充值订单失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '充值处理失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 选择充值渠道
     */
    protected function selectRechargeProvider(string $mobile, float $amount): ?RechargeProvider
    {
        // 获取可用的充值渠道
        $providers = $this->mobileCarrierService->getAvailableProviders($mobile);
        
        if (empty($providers)) {
            return null;
        }

        // 选择权重最高的可用渠道
        foreach ($providers as $providerData) {
            $provider = RechargeProvider::find($providerData['id']);
            
            if ($provider && $provider->isEnabled() && $provider->supportsAmount($amount)) {
                return $provider;
            }
        }

        return null;
    }

    /**
     * 调用充值API
     */
    protected function callRechargeApi(RechargeProvider $provider, RechargeRecord $record): array
    {
        try {
            // 构建充值请求
            $rechargeRequest = new RechargeRequest(
                $record->provider_order_no,  // orderId
                $record->mobile,             // chargeAcct
                (string)$record->amount      // chargeCash
            );

            // 设置运营商信息（如果有）
            if ($record->carrier) {
                $carrierMap = [
                    'mobile' => '中国移动',
                    'unicom' => '中国联通',
                    'telecom' => '中国电信',
                ];
                $ispName = $carrierMap[$record->carrier] ?? $record->carrier;
                $rechargeRequest->setIspName($ispName);
            }

            // 设置省份信息（如果有）
            if ($record->province) {
                $rechargeRequest->setProvince($record->province);
            }

            // 设置回调地址
            $rechargeRequest->setRetUrl(route('api.recharge.notify', ['provider' => $provider->id]));

            // 调用充值服务
            $response = $this->rechargeApiService->recharge($rechargeRequest);

            // 记录请求和响应数据
            $record->update([
                'request_data' => $rechargeRequest->toBusiBody(),
                'response_data' => $response->rawResponse,
                'submitted_at' => now(),
            ]);

            if ($response->isSuccess()) {
                return [
                    'success' => true,
                    'response' => $response->rawResponse,
                    'charge_id' => $response->getChargeId(),
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $response->getErrorMessage(),
                    'response' => $response->rawResponse,
                ];
            }
        } catch (\Exception $e) {
            Log::error('调用充值API失败', [
                'provider_id' => $provider->id,
                'record_id' => $record->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'API调用失败：' . $e->getMessage(),
                'response' => null,
            ];
        }
    }

    /**
     * 处理充值回调
     */
    public function handleRechargeCallback(int $providerId, array $data): array
    {
        try {
            $provider = RechargeProvider::find($providerId);
            if (!$provider) {
                return ['success' => false, 'message' => '充值渠道不存在'];
            }

            // 验证回调签名
            if (!$this->verifyCallbackSignature($provider, $data)) {
                return ['success' => false, 'message' => '签名验证失败'];
            }

            // 查找充值记录
            $record = RechargeRecord::where('provider_order_no', $data['order_no'])
                ->where('provider_id', $providerId)
                ->first();

            if (!$record) {
                return ['success' => false, 'message' => '充值记录不存在'];
            }

            // 更新充值状态
            $status = $data['status'] === 'success' ? RechargeRecord::STATUS_SUCCESS : RechargeRecord::STATUS_FAILED;
            $record->update([
                'status' => $status,
                'provider_response' => $data,
                'completed_at' => now(),
            ]);

            // 更新订单状态
            $orderStatus = $status === RechargeRecord::STATUS_SUCCESS ? Order::STATUS_SUCCESS : Order::STATUS_FAILED;
            $this->orderService->updateOrderStatus($record->order, $orderStatus, 'callback', '充值回调更新');

            // 如果充值失败，处理退款
            if ($status === RechargeRecord::STATUS_FAILED) {
                $this->processRefund($record->order, '充值回调确认失败退款');
            }

            Log::info('处理充值回调', [
                'provider_id' => $providerId,
                'record_id' => $record->id,
                'status' => $status,
            ]);

            return ['success' => true, 'message' => '回调处理成功'];
        } catch (\Exception $e) {
            Log::error('处理充值回调失败', [
                'provider_id' => $providerId,
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return ['success' => false, 'message' => '回调处理失败'];
        }
    }

    /**
     * 验证回调签名
     */
    protected function verifyCallbackSignature(RechargeProvider $provider, array $data): bool
    {
        try {
            // 使用SignatureService验证签名
            $signatureService = new SignatureService();

            // 从回调数据中提取签名
            $signature = $data['sign'] ?? '';
            if (empty($signature)) {
                Log::warning('回调数据中缺少签名', ['provider_id' => $provider->id]);
                return false;
            }

            // 移除签名字段，准备验证
            $verifyData = $data;
            unset($verifyData['sign']);

            // 获取MD5密钥（从配置或环境变量中获取）
            $md5Key = config('recharge.md5_key', env('RECHARGE_MD5_KEY', 'default_key'));

            // 验证签名
            return $signatureService->verifySign($signature, $verifyData, $md5Key);
        } catch (\Exception $e) {
            Log::error('验证回调签名失败', [
                'provider_id' => $provider->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 生成渠道订单号
     */
    protected function generateProviderOrderNo(): string
    {
        return 'RCH' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 查询充值状态
     */
    public function queryRechargeStatus(RechargeRecord $record): array
    {
        try {
            $provider = $record->provider;
            if (!$provider) {
                return ['success' => false, 'message' => '充值渠道不存在'];
            }

            // 构建查询请求
            $queryRequest = new OrderQueryRequest($record->provider_order_no);

            // 调用查询服务
            $response = $this->rechargeApiService->queryOrder($queryRequest);

            // 记录查询响应
            $record->update([
                'response_data' => array_merge($record->response_data ?? [], [
                    'query_response' => $response->rawResponse,
                    'query_time' => now()->toDateTimeString(),
                ]),
            ]);

            if ($response->isSuccess()) {
                return [
                    'success' => true,
                    'status' => $response->getOrderStatusText(),
                    'order_success' => $response->isOrderSuccess(),
                    'message' => '查询成功',
                    'charge_id' => $response->chargeId,
                    'finish_time' => $response->finishTime,
                    'order_payment' => $response->orderPayment,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $response->getErrorMessage(),
                ];
            }
        } catch (\Exception $e) {
            Log::error('查询充值状态失败', [
                'record_id' => $record->id,
                'error' => $e->getMessage(),
            ]);

            return ['success' => false, 'message' => '查询失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取充值统计
     */
    public function getRechargeStatistics(array $filters = []): array
    {
        $query = RechargeRecord::query();

        // 应用过滤条件
        if (!empty($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        if (!empty($filters['carrier'])) {
            $query->where('carrier', $filters['carrier']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return [
            'total_count' => $query->count(),
            'total_amount' => $query->sum('amount'),
            'success_count' => $query->where('status', RechargeRecord::STATUS_SUCCESS)->count(),
            'success_amount' => $query->where('status', RechargeRecord::STATUS_SUCCESS)->sum('amount'),
            'failed_count' => $query->where('status', RechargeRecord::STATUS_FAILED)->count(),
            'pending_count' => $query->where('status', RechargeRecord::STATUS_PENDING)->count(),
            'success_rate' => $query->count() > 0 ? 
                round(($query->where('status', RechargeRecord::STATUS_SUCCESS)->count() / $query->count()) * 100, 2) : 0,
        ];
    }

    /**
     * 更新订单状态（委托给OrderService）
     */
    public function updateOrderStatus(Order $order, $status, $operator = 'system', $remark = ''): bool
    {
        return $this->orderService->updateOrderStatus($order, $status, $operator, $remark);
    }

    /**
     * 检查充值限制
     */
    protected function checkRechargeLimit(string $mobile, float $amount): array
    {
        try {
            // 获取配置的限制
            $dailyLimit = config('recharge.limits.daily_limit_per_mobile', 1000);
            $monthlyLimit = config('recharge.limits.monthly_limit_per_mobile', 5000);

            // 检查今日充值总额
            $todayAmount = RechargeRecord::where('mobile', $mobile)
                ->where('status', RechargeRecord::STATUS_SUCCESS)
                ->whereDate('created_at', today())
                ->sum('amount');

            if ($todayAmount + $amount > $dailyLimit) {
                return [
                    'allowed' => false,
                    'message' => "超过每日充值限额 {$dailyLimit} 元，今日已充值 {$todayAmount} 元",
                ];
            }

            // 检查本月充值总额
            $monthlyAmount = RechargeRecord::where('mobile', $mobile)
                ->where('status', RechargeRecord::STATUS_SUCCESS)
                ->whereYear('created_at', now()->year)
                ->whereMonth('created_at', now()->month)
                ->sum('amount');

            if ($monthlyAmount + $amount > $monthlyLimit) {
                return [
                    'allowed' => false,
                    'message' => "超过每月充值限额 {$monthlyLimit} 元，本月已充值 {$monthlyAmount} 元",
                ];
            }

            return ['allowed' => true, 'message' => '检查通过'];
        } catch (\Exception $e) {
            Log::error('检查充值限制失败', [
                'mobile' => $mobile,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            // 出现异常时允许充值，避免影响正常业务
            return ['allowed' => true, 'message' => '检查异常，允许充值'];
        }
    }

    /**
     * 处理退款
     */
    protected function processRefund(Order $order, string $reason = '充值失败退款'): bool
    {
        try {
            // 只有已支付的订单才能退款
            if ($order->status !== Order::STATUS_PAID && $order->status !== Order::STATUS_FAILED) {
                return false;
            }

            // 获取支付金额
            $refundAmount = $order->actual_amount ?? $order->amount;

            if ($order->merchant_id) {
                // 商户订单：退回商户余额
                $merchant = $order->merchant;
                if ($merchant) {
                    $result = $merchant->refund(
                        $refundAmount,
                        'order',
                        $order->id,
                        'system',
                        $reason
                    );

                    if ($result) {
                        Log::info('充值失败退款成功', [
                            'order_id' => $order->id,
                            'merchant_id' => $order->merchant_id,
                            'amount' => $refundAmount,
                            'reason' => $reason,
                        ]);
                        return true;
                    }
                }
            } else {
                // 用户订单：退回券（如果使用了券）
                if ($order->voucher_deduct > 0) {
                    // 恢复券的使用状态
                    \App\Models\UserVoucher::where('used_order_id', $order->id)
                        ->update([
                            'status' => \App\Models\UserVoucher::STATUS_UNUSED,
                            'used_order_id' => null,
                            'used_at' => null,
                        ]);

                    Log::info('充值失败恢复券', [
                        'order_id' => $order->id,
                        'voucher_deduct' => $order->voucher_deduct,
                    ]);
                }
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('处理退款失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
