<?php

namespace App\Services;

use App\Models\Order;
use App\Models\RechargeRecord;
use App\Models\RechargeProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RechargeService
{
    protected $orderService;
    protected $mobileCarrierService;

    public function __construct(OrderService $orderService, MobileCarrierService $mobileCarrierService)
    {
        $this->orderService = $orderService;
        $this->mobileCarrierService = $mobileCarrierService;
    }

    /**
     * 处理充值订单
     */
    public function processRechargeOrder(Order $order): array
    {
        try {
            DB::beginTransaction();

            // 检查订单状态
            if ($order->status !== Order::STATUS_PAID) {
                return ['success' => false, 'message' => '订单状态不正确'];
            }

            if ($order->type !== Order::TYPE_RECHARGE) {
                return ['success' => false, 'message' => '不是充值订单'];
            }

            // 获取手机号运营商信息
            $carrierInfo = $this->mobileCarrierService->getCarrierInfo($order->mobile);
            if ($carrierInfo['carrier'] === 'unknown') {
                return ['success' => false, 'message' => '不支持的手机号段'];
            }

            // 选择充值渠道
            $provider = $this->selectRechargeProvider($order->mobile, $order->amount);
            if (!$provider) {
                return ['success' => false, 'message' => '暂无可用的充值渠道'];
            }

            // 更新订单状态为充值中
            $this->orderService->updateOrderStatus($order, Order::STATUS_RECHARGING, 'system', '开始充值');

            // 创建充值记录
            $rechargeRecord = RechargeRecord::create([
                'order_id' => $order->id,
                'provider_id' => $provider->id,
                'provider_order_no' => $this->generateProviderOrderNo(),
                'mobile' => $order->mobile,
                'amount' => $order->amount,
                'carrier' => $carrierInfo['carrier'],
                'province' => $carrierInfo['province'],
                'city' => $carrierInfo['city'],
                'status' => RechargeRecord::STATUS_PENDING,
            ]);

            // 调用充值接口
            $rechargeResult = $this->callRechargeApi($provider, $rechargeRecord);

            if ($rechargeResult['success']) {
                // 充值成功
                $rechargeRecord->update([
                    'status' => RechargeRecord::STATUS_SUCCESS,
                    'provider_response' => $rechargeResult['response'],
                    'completed_at' => now(),
                ]);

                $this->orderService->updateOrderStatus($order, Order::STATUS_SUCCESS, 'system', '充值成功');

                DB::commit();

                Log::info('充值成功', [
                    'order_id' => $order->id,
                    'recharge_record_id' => $rechargeRecord->id,
                    'mobile' => $order->mobile,
                    'amount' => $order->amount,
                ]);

                return [
                    'success' => true,
                    'recharge_record' => $rechargeRecord,
                    'message' => '充值成功',
                ];
            } else {
                // 充值失败
                $rechargeRecord->update([
                    'status' => RechargeRecord::STATUS_FAILED,
                    'provider_response' => $rechargeResult['response'],
                    'error_message' => $rechargeResult['message'],
                ]);

                $this->orderService->updateOrderStatus($order, Order::STATUS_FAILED, 'system', '充值失败：' . $rechargeResult['message']);

                DB::commit();

                Log::warning('充值失败', [
                    'order_id' => $order->id,
                    'recharge_record_id' => $rechargeRecord->id,
                    'error' => $rechargeResult['message'],
                ]);

                return [
                    'success' => false,
                    'recharge_record' => $rechargeRecord,
                    'message' => $rechargeResult['message'],
                ];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('处理充值订单失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => '充值处理失败：' . $e->getMessage(),
            ];
        }
    }

    /**
     * 选择充值渠道
     */
    protected function selectRechargeProvider(string $mobile, float $amount): ?RechargeProvider
    {
        // 获取可用的充值渠道
        $providers = $this->mobileCarrierService->getAvailableProviders($mobile);
        
        if (empty($providers)) {
            return null;
        }

        // 选择权重最高的可用渠道
        foreach ($providers as $providerData) {
            $provider = RechargeProvider::find($providerData['id']);
            
            if ($provider && $provider->isEnabled() && $provider->supportsAmount($amount)) {
                return $provider;
            }
        }

        return null;
    }

    /**
     * 调用充值API
     */
    protected function callRechargeApi(RechargeProvider $provider, RechargeRecord $record): array
    {
        try {
            // 模拟充值API调用
            // 实际项目中这里会调用真实的充值接口
            
            $config = $provider->config;
            $apiUrl = $config['api_url'] ?? '';
            $apiKey = $config['api_key'] ?? '';

            // 构建请求参数
            $params = [
                'order_no' => $record->provider_order_no,
                'mobile' => $record->mobile,
                'amount' => $record->amount,
                'notify_url' => route('api.recharge.notify', ['provider' => $provider->id]),
            ];

            // 模拟API响应
            $success = mt_rand(1, 100) <= 95; // 95%成功率

            if ($success) {
                return [
                    'success' => true,
                    'response' => [
                        'code' => '0000',
                        'message' => '充值成功',
                        'order_no' => $record->provider_order_no,
                        'provider_order_no' => 'PROV' . time() . mt_rand(1000, 9999),
                    ],
                ];
            } else {
                return [
                    'success' => false,
                    'message' => '充值失败：余额不足',
                    'response' => [
                        'code' => '1001',
                        'message' => '余额不足',
                    ],
                ];
            }
        } catch (\Exception $e) {
            Log::error('调用充值API失败', [
                'provider_id' => $provider->id,
                'record_id' => $record->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'API调用失败：' . $e->getMessage(),
                'response' => null,
            ];
        }
    }

    /**
     * 处理充值回调
     */
    public function handleRechargeCallback(int $providerId, array $data): array
    {
        try {
            $provider = RechargeProvider::find($providerId);
            if (!$provider) {
                return ['success' => false, 'message' => '充值渠道不存在'];
            }

            // 验证回调签名
            if (!$this->verifyCallbackSignature($provider, $data)) {
                return ['success' => false, 'message' => '签名验证失败'];
            }

            // 查找充值记录
            $record = RechargeRecord::where('provider_order_no', $data['order_no'])
                ->where('provider_id', $providerId)
                ->first();

            if (!$record) {
                return ['success' => false, 'message' => '充值记录不存在'];
            }

            // 更新充值状态
            $status = $data['status'] === 'success' ? RechargeRecord::STATUS_SUCCESS : RechargeRecord::STATUS_FAILED;
            $record->update([
                'status' => $status,
                'provider_response' => $data,
                'completed_at' => now(),
            ]);

            // 更新订单状态
            $orderStatus = $status === RechargeRecord::STATUS_SUCCESS ? Order::STATUS_SUCCESS : Order::STATUS_FAILED;
            $this->orderService->updateOrderStatus($record->order, $orderStatus, 'callback', '充值回调更新');

            Log::info('处理充值回调', [
                'provider_id' => $providerId,
                'record_id' => $record->id,
                'status' => $status,
            ]);

            return ['success' => true, 'message' => '回调处理成功'];
        } catch (\Exception $e) {
            Log::error('处理充值回调失败', [
                'provider_id' => $providerId,
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return ['success' => false, 'message' => '回调处理失败'];
        }
    }

    /**
     * 验证回调签名
     */
    protected function verifyCallbackSignature(RechargeProvider $provider, array $data): bool
    {
        // 实际项目中需要根据不同渠道的签名规则进行验证
        // 这里简化处理
        return true;
    }

    /**
     * 生成渠道订单号
     */
    protected function generateProviderOrderNo(): string
    {
        return 'RCH' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 查询充值状态
     */
    public function queryRechargeStatus(RechargeRecord $record): array
    {
        try {
            $provider = $record->provider;
            if (!$provider) {
                return ['success' => false, 'message' => '充值渠道不存在'];
            }

            // 调用渠道查询接口
            // 这里模拟查询结果
            $queryResult = [
                'success' => true,
                'status' => $record->status === RechargeRecord::STATUS_PENDING ? 'processing' : 'completed',
                'message' => '查询成功',
            ];

            return $queryResult;
        } catch (\Exception $e) {
            Log::error('查询充值状态失败', [
                'record_id' => $record->id,
                'error' => $e->getMessage(),
            ]);

            return ['success' => false, 'message' => '查询失败'];
        }
    }

    /**
     * 获取充值统计
     */
    public function getRechargeStatistics(array $filters = []): array
    {
        $query = RechargeRecord::query();

        // 应用过滤条件
        if (!empty($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        if (!empty($filters['carrier'])) {
            $query->where('carrier', $filters['carrier']);
        }

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        return [
            'total_count' => $query->count(),
            'total_amount' => $query->sum('amount'),
            'success_count' => $query->where('status', RechargeRecord::STATUS_SUCCESS)->count(),
            'success_amount' => $query->where('status', RechargeRecord::STATUS_SUCCESS)->sum('amount'),
            'failed_count' => $query->where('status', RechargeRecord::STATUS_FAILED)->count(),
            'pending_count' => $query->where('status', RechargeRecord::STATUS_PENDING)->count(),
            'success_rate' => $query->count() > 0 ? 
                round(($query->where('status', RechargeRecord::STATUS_SUCCESS)->count() / $query->count()) * 100, 2) : 0,
        ];
    }
}
