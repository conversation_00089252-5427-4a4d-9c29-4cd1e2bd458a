<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recharge_records', function (Blueprint $table) {
            $table->string('charge_id')->nullable()->after('provider_order_no')->comment('充值渠道返回的充值ID');
            $table->integer('provider_id')->nullable()->after('provider')->comment('充值渠道ID');
            $table->string('carrier')->nullable()->after('amount')->comment('运营商');
            $table->string('province')->nullable()->after('carrier')->comment('省份');
            $table->string('city')->nullable()->after('province')->comment('城市');
            $table->json('provider_response')->nullable()->after('response_data')->comment('充值渠道响应数据');
            $table->string('error_message')->nullable()->after('error_msg')->comment('错误信息');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recharge_records', function (Blueprint $table) {
            $table->dropColumn([
                'charge_id',
                'provider_id', 
                'carrier',
                'province',
                'city',
                'provider_response',
                'error_message'
            ]);
        });
    }
};
