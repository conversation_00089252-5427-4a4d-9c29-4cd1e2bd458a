<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mobile_segments', function (Blueprint $table) {
            $table->id();
            $table->string('segment', 7)->unique()->comment('号段前7位');
            $table->string('carrier', 20)->comment('运营商：mobile,unicom,telecom');
            $table->string('province', 50)->comment('省份');
            $table->string('city', 50)->comment('城市');
            $table->timestamps();
            
            $table->index('carrier');
            $table->index('province');
            $table->index(['carrier', 'province']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mobile_segments');
    }
};
