<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_configs', function (Blueprint $table) {
            $table->id();
            $table->string('group', 50)->comment('配置分组：system,payment,recharge,notification等');
            $table->string('key', 100)->comment('配置键');
            $table->text('value')->nullable()->comment('配置值');
            $table->string('type', 20)->default('string')->comment('数据类型：string,int,float,bool,json,array');
            $table->string('name', 100)->comment('配置名称');
            $table->string('description', 255)->nullable()->comment('配置描述');
            $table->json('options')->nullable()->comment('可选值（用于select类型）');
            $table->boolean('is_public')->default(false)->comment('是否公开（前端可访问）');
            $table->boolean('is_required')->default(false)->comment('是否必填');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            
            $table->unique(['group', 'key']);
            $table->index('group');
            $table->index('is_public');
            $table->index(['group', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_configs');
    }
};
