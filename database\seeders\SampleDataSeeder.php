<?php

namespace Database\Seeders;

use App\Models\Merchant;
use App\Models\Product;
use App\Models\ProductVoucher;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 创建示例商户
        $merchant = Merchant::create([
            'account' => 'test_merchant',
            'name' => '测试商户',
            'contact_name' => '张三',
            'contact_phone' => '***********',
            'email' => '<EMAIL>',
            'md5_key' => Str::random(32),
            'balance' => 10000.00,
            'frozen_balance' => 0,
            'status' => true,
            'ip_whitelist' => ['127.0.0.1'],
            'remark' => '测试商户账号',
        ]);

        // 创建示例产品
        $products = [
            [
                'name' => '充100送200话费券',
                'code' => 'PROD_100_200',
                'description' => '充值100元，获得总面值200元的话费券',
                'price' => 100.00,
                'cost' => 90.00,
                'status' => true,
                'sort' => 1,
                'vouchers' => [
                    ['value' => 50, 'count' => 2],
                    ['value' => 20, 'count' => 5],
                ]
            ],
            [
                'name' => '充50送100话费券',
                'code' => 'PROD_50_100',
                'description' => '充值50元，获得总面值100元的话费券',
                'price' => 50.00,
                'cost' => 45.00,
                'status' => true,
                'sort' => 2,
                'vouchers' => [
                    ['value' => 20, 'count' => 3],
                    ['value' => 10, 'count' => 4],
                ]
            ],
            [
                'name' => '充30送50话费券',
                'code' => 'PROD_30_50',
                'description' => '充值30元，获得总面值50元的话费券',
                'price' => 30.00,
                'cost' => 27.00,
                'status' => true,
                'sort' => 3,
                'vouchers' => [
                    ['value' => 10, 'count' => 3],
                    ['value' => 5, 'count' => 4],
                ]
            ],
        ];

        foreach ($products as $productData) {
            $vouchers = $productData['vouchers'];
            unset($productData['vouchers']);
            
            // 计算券总面值
            $totalValue = 0;
            foreach ($vouchers as $voucher) {
                $totalValue += $voucher['value'] * $voucher['count'];
            }
            $productData['total_value'] = $totalValue;
            
            // 创建产品
            $product = Product::create($productData);
            
            // 创建券配置
            foreach ($vouchers as $voucher) {
                ProductVoucher::create([
                    'product_id' => $product->id,
                    'voucher_value' => $voucher['value'],
                    'voucher_count' => $voucher['count'],
                ]);
            }
        }

        $this->command->info('示例数据创建成功！');
        $this->command->info('商户账号：test_merchant');
        $this->command->info('MD5密钥：' . $merchant->md5_key);
    }
}