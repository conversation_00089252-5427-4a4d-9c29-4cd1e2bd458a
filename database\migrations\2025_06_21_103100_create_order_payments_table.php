<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->comment('订单ID');
            $table->string('payment_no', 64)->unique()->comment('支付流水号');
            $table->string('payment_method', 20)->comment('支付方式：balance,alipay,wechat');
            $table->decimal('payment_amount', 10, 2)->comment('支付金额');
            $table->decimal('voucher_deduct', 10, 2)->default(0)->comment('券抵扣金额');
            $table->tinyInteger('status')->default(0)->comment('支付状态：0-待支付，1-已支付，2-已退款');
            $table->timestamp('paid_at')->nullable()->comment('支付时间');
            $table->timestamp('refunded_at')->nullable()->comment('退款时间');
            $table->timestamps();
            
            $table->index('order_id');
            $table->index('payment_method');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_payments');
    }
};
