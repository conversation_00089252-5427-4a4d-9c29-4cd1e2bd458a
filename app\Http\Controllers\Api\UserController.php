<?php

namespace App\Http\Controllers\Api;

use App\Services\VoucherService;
use App\Services\OrderService;
use App\Transformers\UserVoucherTransformer;
use App\Transformers\OrderTransformer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class UserController extends BaseApiController
{
    protected $voucherService;
    protected $orderService;

    public function __construct(VoucherService $voucherService, OrderService $orderService)
    {
        $this->voucherService = $voucherService;
        $this->orderService = $orderService;
        $this->middleware('auth:sanctum');
    }

    /**
     * 获取用户信息
     */
    public function profile()
    {
        try {
            $user = Auth::user();
            
            return $this->success([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'mobile' => $user->mobile,
                'avatar' => $user->avatar,
                'status' => $user->status,
                'created_at' => $user->created_at->toDateTimeString(),
            ]);
        } catch (\Exception $e) {
            return $this->serverError('获取用户信息失败');
        }
    }

    /**
     * 更新用户信息
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = Auth::user();

            $validated = $this->validateRequest([
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $user->id,
                'mobile' => 'nullable|regex:/^1[3-9]\d{9}$/|unique:users,mobile,' . $user->id,
                'avatar' => 'nullable|url',
            ]);

            $user->update($validated);

            return $this->success([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'mobile' => $user->mobile,
                'avatar' => $user->avatar,
            ], '个人信息更新成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('更新个人信息失败');
        }
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'current_password' => 'required',
                'password' => ['required', 'confirmed', Password::min(8)],
            ]);

            $user = Auth::user();

            if (!Hash::check($validated['current_password'], $user->password)) {
                return $this->error('当前密码不正确', 'INVALID_PASSWORD', 400);
            }

            $user->update([
                'password' => Hash::make($validated['password']),
            ]);

            return $this->success(null, '密码修改成功');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('密码修改失败');
        }
    }

    /**
     * 获取用户统计信息
     */
    public function statistics()
    {
        try {
            $user = Auth::user();

            $stats = [
                'total_orders' => $user->orders()->count(),
                'success_orders' => $user->orders()->where('status', \App\Models\Order::STATUS_SUCCESS)->count(),
                'pending_orders' => $user->orders()->where('status', \App\Models\Order::STATUS_PENDING)->count(),
                'total_amount' => $user->orders()->where('status', \App\Models\Order::STATUS_SUCCESS)->sum('amount'),
                'available_vouchers' => $user->availableVouchers()->count(),
                'total_voucher_value' => $user->availableVouchers()->sum('voucher_value'),
                'used_vouchers' => $user->usedVouchers()->count(),
            ];

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->serverError('获取统计信息失败');
        }
    }

    /**
     * 获取用户券列表
     */
    public function vouchers(Request $request)
    {
        try {
            $user = Auth::user();
            $status = $request->get('status', 'available'); // available, used, expired
            $perPage = min($request->get('per_page', 20), 100);

            $query = $user->vouchers()->with(['product', 'order', 'usedOrder']);

            switch ($status) {
                case 'used':
                    $query->used();
                    break;
                case 'expired':
                    $query->expired();
                    break;
                default:
                    $query->available();
                    break;
            }

            $vouchers = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return $this->paginated($vouchers, new UserVoucherTransformer());

        } catch (\Exception $e) {
            return $this->serverError('获取券列表失败');
        }
    }

    /**
     * 获取券统计信息
     */
    public function voucherStats()
    {
        try {
            $user = Auth::user();
            $stats = $this->voucherService->getUserVoucherStats($user->id);

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->serverError('获取券统计失败');
        }
    }

    /**
     * 获取用户订单列表
     */
    public function orders(Request $request)
    {
        try {
            $user = Auth::user();
            $type = $request->get('type'); // product, recharge
            $status = $request->get('status');
            $perPage = min($request->get('per_page', 20), 100);

            $query = $user->orders()->with(['product', 'rechargeRecord', 'payments']);

            if ($type) {
                $query->where('type', $type);
            }

            if ($status !== null) {
                $query->where('status', $status);
            }

            $orders = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return $this->paginated($orders, new OrderTransformer(), ['product', 'recharge_record', 'payments']);

        } catch (\Exception $e) {
            return $this->serverError('获取订单列表失败');
        }
    }

    /**
     * 获取单个订单详情
     */
    public function orderDetail($orderId)
    {
        try {
            $user = Auth::user();
            
            $order = $user->orders()
                ->with(['product', 'rechargeRecord', 'payments', 'logs'])
                ->findOrFail($orderId);

            return $this->item($order, new OrderTransformer(), ['product', 'recharge_record', 'payments']);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFound('订单不存在');
        } catch (\Exception $e) {
            return $this->serverError('获取订单详情失败');
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder($orderId)
    {
        try {
            $user = Auth::user();
            
            $order = $user->orders()
                ->where('status', \App\Models\Order::STATUS_PENDING)
                ->findOrFail($orderId);

            $success = $this->orderService->updateOrderStatus(
                $order,
                \App\Models\Order::STATUS_CANCELLED,
                'user',
                '用户取消订单'
            );

            if ($success) {
                return $this->success(null, '订单已取消');
            } else {
                return $this->error('取消订单失败', 'CANCEL_FAILED');
            }

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFound('订单不存在或无法取消');
        } catch (\Exception $e) {
            return $this->serverError('取消订单失败');
        }
    }

    /**
     * 计算最优券使用方案
     */
    public function calculateOptimalVouchers(Request $request)
    {
        try {
            $validated = $this->validateRequest([
                'amount' => 'required|numeric|min:0.01',
                'voucher_ids' => 'nullable|array',
                'voucher_ids.*' => 'integer',
            ]);

            $user = Auth::user();
            
            $result = $this->voucherService->calculateOptimalVoucherUsage(
                $user->id,
                $validated['amount'],
                $validated['voucher_ids'] ?? null
            );

            return $this->success($result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationError($e->errors());
        } catch (\Exception $e) {
            return $this->serverError('计算券使用方案失败');
        }
    }
}
