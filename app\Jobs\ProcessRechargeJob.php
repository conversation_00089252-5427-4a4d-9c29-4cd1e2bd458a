<?php

namespace App\Jobs;

use App\Models\Order;
use App\Services\RechargeService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessRechargeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $order;

    /**
     * 创建新的任务实例
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * 执行任务
     */
    public function handle(RechargeService $rechargeService): void
    {
        try {
            Log::info('开始处理充值订单', [
                'order_id' => $this->order->id,
                'order_no' => $this->order->order_no,
                'mobile' => $this->order->mobile,
                'amount' => $this->order->amount,
            ]);

            $result = $rechargeService->processRechargeOrder($this->order);

            if ($result['success']) {
                Log::info('充值订单处理成功', [
                    'order_id' => $this->order->id,
                    'recharge_record_id' => $result['recharge_record']->id ?? null,
                ]);
            } else {
                Log::warning('充值订单处理失败', [
                    'order_id' => $this->order->id,
                    'error' => $result['message'],
                ]);
            }
        } catch (\Exception $e) {
            Log::error('充值任务执行失败', [
                'order_id' => $this->order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常，让队列系统处理重试
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('充值任务最终失败', [
            'order_id' => $this->order->id,
            'error' => $exception->getMessage(),
        ]);

        // 可以在这里发送告警通知
        // 或者将订单状态标记为失败
    }
}
