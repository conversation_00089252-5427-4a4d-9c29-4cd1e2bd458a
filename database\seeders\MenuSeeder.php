<?php

namespace Database\Seeders;

use Dcat\Admin\Models\Menu;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 获取或创建业务管理父菜单
        $businessMenu = Menu::firstOrCreate([
            'title' => '充值渠道管理',
        ], [
            'icon' => 'fa-shopping-cart',
            'uri' => '',
            'parent_id' => 0,
            'order' => 100,
        ]);

        // 商户管理
        Menu::firstOrCreate([
            'title' => '商户管理',
            'parent_id' => $businessMenu->id,
        ], [
            'icon' => 'fa-users',
            'uri' => 'merchants',
            'order' => 1,
        ]);

        // 产品管理
        Menu::firstOrCreate([
            'title' => '产品管理',
            'parent_id' => $businessMenu->id,
        ], [
            'icon' => 'fa-cube',
            'uri' => 'products',
            'order' => 2,
        ]);

        // 订单管理
        Menu::firstOrCreate([
            'title' => '订单管理',
            'parent_id' => $businessMenu->id,
        ], [
            'icon' => 'fa-list-alt',
            'uri' => 'orders',
            'order' => 3,
        ]);

        // 充值管理父菜单
        $rechargeMenu = Menu::firstOrCreate([
            'title' => '充值管理',
        ], [
            'icon' => 'fa-mobile',
            'uri' => '',
            'parent_id' => 0,
            'order' => 200,
        ]);

        // 充值记录
        Menu::firstOrCreate([
            'title' => '充值记录',
            'parent_id' => $rechargeMenu->id,
        ], [
            'icon' => 'fa-history',
            'uri' => 'recharge-records',
            'order' => 1,
        ]);

        // 统计报表父菜单
        $reportMenu = Menu::firstOrCreate([
            'title' => '统计报表',
        ], [
            'icon' => 'fa-bar-chart',
            'uri' => '',
            'parent_id' => 0,
            'order' => 300,
        ]);

        // 销售统计
        Menu::firstOrCreate([
            'title' => '销售统计',
            'parent_id' => $reportMenu->id,
        ], [
            'icon' => 'fa-line-chart',
            'uri' => 'reports/sales',
            'order' => 1,
        ]);

        // 充值统计
        Menu::firstOrCreate([
            'title' => '充值统计',
            'parent_id' => $reportMenu->id,
        ], [
            'icon' => 'fa-pie-chart',
            'uri' => 'reports/recharge',
            'order' => 2,
        ]);
    }
}
